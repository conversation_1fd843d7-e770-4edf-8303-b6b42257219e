image: node:20
stages:
  - hypertest
  - test
  - build
  - push
  - pre-deploy
  - deploy

variables:
  DOCKER_HOST: tcp://dind.gitlab-ci:2375
  DOCKER_TLS_CERTDIR: ''
  SERVICE_NAME: payoneer
  SONAR: sonar-scanner -Dsonar.sources=. -Dsonar.javascript.lcov.reportPaths=./coverage/lcov.info -Dsonar.testExecutionReportPaths=./coverage/test-reporter.xml -Dsonar.test.inclusions='**/*.spec.js,**/*.spec.ts,**/*.spec.jsx' -Dsonar.exclusions='src/**/*.spec.js,src/**/*.spec.ts,src/**/*.spec.jsx,src/**/*.test.js,src/**/*.test.jsx,src/migrations/**,src/kafka/**,src/**/*.module.ts,src/**/*.model.ts,src/**/*.guard.ts,src/**/*.dto.ts,src/**/*.type.ts,src/**/*.generator.ts,src/**/*.helper.ts,src/database/migrations/**,src/**/*.enum.ts,src/**/*.interface.ts,src/**/*.input.ts,src/**/*.outputs.ts,src/beneficiary/dtos/inputs/**,src/beneficiary/dtos/outputs/**,src/beneficiary/services/abstract-services/**,src/common/common.module.ts,src/common/data-types/**,src/common/dtos/**,src/common/error.custom.error.ts,src/common/external-service/**,src/app.module.ts,src/main.ts,src/**/*.index.ts,src/common/messages/error.messages.ts,src/common/repositories/*.repository.ts,src/grpc.service.options.ts,src/**/**/**/*.index.ts,src/**/**/*.index.ts,src/**/**/**/**/*.index.ts,src/**/**/*.entity.ts,package.json,**/package.json,package-lock.json,**/package-lock.json,test/**/*.ts,test/*.ts,test/**/**/*.ts,src/common/entities/account.entitiy.ts,src/common/repositories/base-repository.ts,src/common/constants/maps.ts'

  DOCKER_IMAGE: ringcentral/web-tools:node18-openjdk17-alpine
  CYCLONEDX_VERSION: cyclonedx/bom --force
  NPM_COMMAND: npm run test:cov --silent
  NODE_VERSION: node:20
  TEST_COV: npm run test:coverage
  PACK_MANAGER: npm

  # # Test env variables
  # PROVIDER_PROGRAM_ID: 'random-provider-program-id'
  # PROVIDER_API_BASE_URL: 'https://api.provider.test'
  # PROVIDER_API_AUTH_URL: 'https://api.provider.test/auth'
  # PROVIDER_CLIENT_ID: 'test-client-id'
  # PROVIDER_CLIENT_SECRET: 'test-client-secret'
  # GRPC_PAYMENT_SERVICE_URL: 'grpc://payments.service.test'
  # PRIVATE_API_KEY: 'test-private-key'
  # PROVIDER_CREDENTIALS: 'ewogICJkZWZhdWx0IjogewogICAgImRlZmF1bHQiOiB7CiAgICAgICJwcm9ncmFtX2lkIjogInRlc3RpbmctcHJvZ3JhbS1pZCIsCiAgICAgICJ0b2tlbiI6ICJPVWRJWW5GblExcDZkMHROWTJzemFUSm1UVXhIVldaaVZHVldaSEI2VkVnNlFrTlNSbmN4T1c1dlRrTjZZMjR6VXc9PSIKICAgIH0KICB9Cn0='
  # # JSON string for PAYONEER_SERVICE_PG_DB
  # PAYONEER_SERVICE_PG_DB: '{"host": "localhost", "port": 5432, "database": "testdb", "user": "testuser", "password": "testpassword"}'
  # GATEWAY_URL: 'https://gateway.test'
  # CONTRACT_SERVICE_URL: 'https://contracts.service.test'
  # SKUAD_SINGAPORE_ENTITY_ID: 'test-skuad-singapore-entity-id'
  # USER_TOKEN: 'test-user-token'
  # PAYMENT_SERVICE_GRPC_API_KEY: 'test-grpc-api-key'
  # REDIRECT_URI: 'https://pay-stg.skuad.in/dashboard'
  # SSO_PROGRAM_ID: '100236700'
  # PWP_CLIENT_ID: 'XL8ImHinofC62Ls4HwGhapQGmETNW0gw'
  # PWP_SECRET_ID: 'J52wlSBgmSjAPBfB'

include:
  - project: 'skuad/ci-templates'
    ref: main
    file:
      - '/.backend.yml'
  - project: 'skuad/ci-templates'
    ref: main
    file:
      - '/.secrets.yml'
  - project: 'skuad/ci-templates'
    ref: main
    file:
      - 'sonarqube-templates/.sonarqube-npm.yml'
  - project: 'skuad/ci-templates'
    ref: main
    file:
      - 'hypertest-template/.hypt.yml'

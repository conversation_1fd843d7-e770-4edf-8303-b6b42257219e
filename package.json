{"name": "payoneer-service", "version": "0.0.2", "description": "", "author": "", "private": true, "license": "UNLICENSED", "engines": {"node": ">=20.0.0"}, "scripts": {"update:cov": "htcli update-coverage --deduplicate --config-file-path .htConf.js", "start:nyc": "nyc nest start", "run-test": "nyc --reporter html htcli start-new-test --config-file-path .htConf.js", "update-ht-packages": "htcli update-ht-packages --package-manager npm --config-file-path .htConf.js", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug=9250 --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "generate:migration": "npm run build && ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:generate -d src/common/database/data-source.ts src/database/migrations/migrations", "run:migration": "cross-env NODE_ENV=production npm run typeorm migration:run -- -d src/common/database/data-source.ts", "revert:migration": "cross-env NODE_ENV=production npm run typeorm migration:revert -- -d src/common/database/data-source.ts", "run:migration:dev": "cross-env NODE_ENV=development npm run build && npm run typeorm migration:run -- -d src/common/database/data-source.ts", "revert:migration:dev": "cross-env NODE_ENV=development npm run build && npm run typeorm migration:revert -- -d src/common/database/data-source.ts", "migration:test": "nest build && npm run test:container:start && npm run typeorm migration:run -- -d src/common/database/data-source.test.ts"}, "dependencies": {"@apollo/subgraph": "^2.10.0", "@faker-js/faker": "^9.0.0", "@grpc/grpc-js": "^1.11.2", "@grpc/proto-loader": "^0.7.13", "@hypertestco/ht-cli": "0.2.28-76", "@hypertestco/node-sdk": "0.2.28-76", "@nestjs/apollo": "^12.2.0", "@nestjs/axios": "^3.0.3", "@nestjs/common": "^10.4.1", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.4.1", "@nestjs/cqrs": "^10.2.7", "@nestjs/graphql": "^12.2.0", "@nestjs/mapped-types": "^2.0.5", "@nestjs/microservices": "^10.4.1", "@nestjs/platform-express": "^10.4.1", "@nestjs/schedule": "^4.1.0", "@nestjs/terminus": "^10.2.3", "@nestjs/typeorm": "^10.0.2", "@skuad/proto-utils": "^0.2.5-alpha.4", "@types/iso-3166-2": "^1.0.3", "apollo-server-express": "^3.13.0", "axios": "^1.7.7", "big.js": "^7.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.7", "dayjs": "^1.11.13", "graphql": "^16.10.0", "i18n-iso-countries": "^7.12.0", "iso-3166-2": "^1.0.0", "nest-winston": "^1.9.7", "nestjs-grpc-reflection": "^0.2.2", "nyc": "17.1.0", "pg": "^8.13.0", "request": "^2.88.2", "rimraf": "^6.0.1", "rxjs": "^7.8.1", "skuad-utils-ts": "git+https://npm-pull:<EMAIL>/skuad/skuad-utils-ts.git#feat/CommonLoggingUtils", "typeorm": "^0.3.20", "typeorm-naming-strategies": "^4.1.0", "underscore": "^1.13.7", "uuid": "^10.0.0", "winston": "^3.14.2"}, "devDependencies": {"@nestjs/cli": "^10.4.5", "@nestjs/schematics": "^10.1.4", "@nestjs/testing": "^10.4.1", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/node": "^22.5.4", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^8.5.0", "@typescript-eslint/parser": "^8.5.0", "cross-env": "^7.0.3", "eslint": "^9.10.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "i18n-iso-countries": "^7.12.0", "jest": "^29.7.0", "jest-each": "^29.7.0", "jest-sonar-reporter": "^2.0.0", "lint-staged": "^15.2.10", "prettier": "^3.3.3", "sonarqube-scanner": "^4.2.2", "ts-jest": "^29.2.5", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.6.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testTimeout": 36000, "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coveragePathIgnorePatterns": ["src/database/migrations/.*", "src/.*/enum\\.ts", "src/.*/module\\.ts", "src/.*/dto\\.ts", "src/.*/interface\\.ts", "src/.*/input\\.ts", "src/.*/outputs\\.ts", "src/beneficiary/dtos/inputs/.*", "src/beneficiary/dtos/outputs/.", "src/beneficiary/services/abstract-services/.", "src/common/common.module.ts", "src/common/data-types/common.data-type.ts", "src/common/dtos/.", "src/common/error custom.error.ts", "src/common/external-service/external-service.module.ts", "src/common/external-service/payments/payments.module.ts", "src/app.module.ts", "src/grpc.service.options.ts", "payout/commands/impl/process-payout-payment-request.command.ts", "payout/commands/impl/process-payout-instruction.command.ts", "payout/commands/impl/process-payin-payment-request.command.ts", "src/.*\\.module\\.ts$", "src/main.ts", "src/.*\\.index\\.ts$", "src/.*\\.interface\\.ts$", "src/common/external-service/payments/common/create-client-grpc.option.ts", "src/common/messages/error.messages.ts", "src/common/repositories/.", "src/common/data-types/."], "coverageDirectory": "../coverage", "testEnvironment": "node", "testResultsProcessor": "jest-sonar-reporter"}, "jestSonar": {"reportPath": "coverage", "reportFile": "test-reporter.xml", "indent": 4}, "volta": {"node": "20.19.1"}}
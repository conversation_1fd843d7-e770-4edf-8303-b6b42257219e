{"numFailedTestSuites": 1, "numFailedTests": 4, "numPassedTestSuites": 0, "numPassedTests": 39, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 1, "numTotalTests": 43, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1753166996963, "success": false, "testResults": [{"leaks": false, "numFailingTests": 4, "numPassingTests": 39, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1753167000582, "runtime": 3501, "slow": false, "start": 1753166997081}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts", "testResults": [{"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) should be defined", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should be defined"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "initialize"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) initialize should set internal fields correctly", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should set internal fields correctly"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "initialize"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) initialize should handle programService returning empty string", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle programService returning empty string"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "initialize", "bank configuration logic"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) initialize bank configuration logic should fetch bank config and map bank name when bankNameOptions exist", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should fetch bank config and map bank name when bankNameOptions exist"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "initialize", "bank configuration logic"], "duration": 2, "failureDetails": [{"matcherResult": {"expected": "UNKNOWN_BANK", "message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m\"UNKNOWN_BANK\"\u001b[39m\nReceived: \u001b[31mundefined\u001b[39m", "name": "toBe", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m\"UNKNOWN_BANK\"\u001b[39m\nReceived: \u001b[31mundefined\u001b[39m\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:193:51)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) initialize bank configuration logic should handle case when bankNameOptions exist but bank name not found in options", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle case when bankNameOptions exist but bank name not found in options"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "initialize", "bank configuration logic"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) initialize bank configuration logic should handle case when bankNameOptions do not exist for currency", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle case when bankNameOptions do not exist for currency"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "initialize", "bank configuration logic"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) initialize bank configuration logic should handle case when config values do not have name property", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle case when config values do not have name property"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "initialize", "bank configuration logic"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) initialize bank configuration logic should handle case when config values name does not have options property", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle case when config values name does not have options property"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "initialize", "bank configuration logic"], "duration": 2, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of null (reading 'values')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:50:14)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:307:9)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) initialize bank configuration logic should handle case when config is null or undefined", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle case when config is null or undefined"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "initialize", "bank configuration logic"], "duration": 2, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of null (reading 'USD')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:50:20)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:330:9)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) initialize bank configuration logic should handle case when config values is null or undefined", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle case when config values is null or undefined"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "initialize", "bank configuration logic"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) initialize bank configuration logic should handle different entity types correctly", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle different entity types correctly"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "initialize", "bank configuration logic"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) initialize bank configuration logic should handle case when payoneerValue is undefined in options", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle case when payoneerValue is undefined in options"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "initialize", "bank configuration logic"], "duration": 1, "failureDetails": [{"matcherResult": {"expected": "BANK_OF_AMERICA", "message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m\"BANK_OF_AMERICA\"\u001b[39m\nReceived: \u001b[31mundefined\u001b[39m", "name": "toBe", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m\"BANK_OF_AMERICA\"\u001b[39m\nReceived: \u001b[31mundefined\u001b[39m\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:408:51)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) initialize bank configuration logic should handle empty options array", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle empty options array"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "initialize", "bank configuration logic"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) initialize bank configuration logic should handle contractService.getConfigByKey throwing an error", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle contractService.getConfigByKey throwing an error"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "sync"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) sync should call createEmptyPayoutProfile, see isProfileAlreadyCreated() = false, call create, and return beneficiaryId", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should call createEmptyPayoutProfile, see isProfileAlreadyCreated() = false, call create, and return beneficiaryId"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "sync"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) sync should call update if profile is already created", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should call update if profile is already created"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "sync"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) sync should handle errors by calling handleBeneficiaryCreationError and throw RpcException", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should handle errors by calling handleBeneficiaryCreationError and throw RpcException"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "create"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) create should remove state fields if country != US, call registerPayee, then beneficiaryRpcClient.updateOne", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should remove state fields if country != US, call registerPayee, then beneficiaryRpcClient.updateOne"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "create"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) create should keep state fields if country == US", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should keep state fields if country == US"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "create"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) create should throw CustomError if registerPayee fails", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should throw CustomError if register<PERSON><PERSON><PERSON> fails"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "create"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) create should throw CustomError if updateOne returns error", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "passed", "title": "should throw CustomError if updateOne returns error"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "createEmptyPayoutProfile"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) createEmptyPayoutProfile should call createOne and set relevant fields on success", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should call createOne and set relevant fields on success"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "createEmptyPayoutProfile"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) createEmptyPayoutProfile should throw CustomError if createOne returns invalid data", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should throw CustomError if createOne returns invalid data"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "createEmptyPayoutProfile"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) createEmptyPayoutProfile should catch promise rejections and throw CustomError if createOne rejects", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should catch promise rejections and throw CustomError if createOne rejects"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "update"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) update should do nothing if there are no mismatches, then updateOne, and return beneficiaryId", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should do nothing if there are no mismatches, then updateOne, and return beneficiaryId"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "update"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) update should call updatePayeePersonalDetails if mismatch in personal details", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should call updatePayeePersonalDetails if mismatch in personal details"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "update"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) update should call updatePayeeBankAccountDetails if mismatch in bank details", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should call updatePayeeBankAccountDetails if mismatch in bank details"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "update"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) update should throw CustomError if updateOne returns an error object", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should throw CustomError if updateOne returns an error object"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "update"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) update should handle setPayeeDetails throwing an error, re-throw as RpcException", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle setPayeeDetails throwing an error, re-throw as RpcException"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "updatePayeePersonalDetails"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) updatePayeePersonalDetails should succeed if editPayeeProfile returns [data,null] and date_of_birth is valid", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should succeed if editPayeeProfile returns [data,null] and date_of_birth is valid"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "updatePayeePersonalDetails"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) updatePayeePersonalDetails should throw CustomError if editPayeeProfile fails", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should throw CustomError if editPayeeProfile fails"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "updatePayeePersonalDetails"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) updatePayeePersonalDetails should handle invalid date_of_birth scenario (RangeError) if you code for it", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle invalid date_of_birth scenario (RangeError) if you code for it"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "updatePayeeBankAccountDetails"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) updatePayeeBankAccountDetails should succeed if editTransferMethod returns [data, null]", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should succeed if editTransferMethod returns [data, null]"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "updatePayeeBankAccountDetails"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) updatePayeeBankAccountDetails should throw CustomError if editTransferMethod fails", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should throw CustomError if editTransferMethod fails"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "setPayeeDetails"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGB<PERSON>andler (Full Coverage) setPayeeDetails should set payeeDetails if getPayeeExtendedDetails is successful", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should set payeeDetails if getPayeeExtendedDetails is successful"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "setPayeeDetails"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGB<PERSON><PERSON>ler (Full Coverage) setPayeeDetails should throw CustomError if getPayeeExtendedDetails returns error", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should throw CustomError if getPayeeExtendedDetails returns error"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "setPayeeDetails"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) setPayeeDetails should throw CustomError if payeeInfo is null or undefined", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should throw CustomError if payeeInfo is null or undefined"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "validateAndGetRegisterPayeeInput"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) validateAndGetRegisterPayeeInput should return transformed register payee input on success", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return transformed register payee input on success"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "validateAndGetRegisterPayeeInput"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) validateAndGetRegisterPayeeInput should throw CustomError if getRegisterPayeeFormat returns an error", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should throw CustomError if getRegisterPayeeFormat returns an error"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "validateAndGetRegisterPayeeInput"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) validateAndGetRegisterPayeeInput should throw CustomError if format result missing payout_method.fields.items", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should throw CustomError if format result missing payout_method.fields.items"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "validateAndGetRegisterPayeeInput"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) validateAndGetRegisterPayeeInput should throw CustomError if validateAndTransformRegisterPayeeInput throws", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should throw CustomError if validateAndTransformRegisterPayeeInput throws"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "handleBeneficiaryCreationError"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) handleBeneficiaryCreationError should log the error and throw an RpcException", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should log the error and throw an RpcException"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "handleBeneficiaryCreationError"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) handleBeneficiaryCreationError should use errorInfo?.message if available", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should use errorInfo?.message if available"}], "failureMessage": "\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › initialize › bank configuration logic › should handle case when bankNameOptions exist but bank name not found in options\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: \u001b[32m\"UNKNOWN_BANK\"\u001b[39m\n    Received: \u001b[31mundefined\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 191 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 192 |\u001b[39m         \u001b[90m// Bank name should remain unchanged when not found in options\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 193 |\u001b[39m         expect(input\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankName)\u001b[33m.\u001b[39mtoBe(\u001b[32m'UNKNOWN_BANK'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 194 |\u001b[39m       })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 195 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 196 |\u001b[39m       it(\u001b[32m'should handle case when bankNameOptions do not exist for currency'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:193:51)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › initialize › bank configuration logic › should handle case when config is null or undefined\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of null (reading 'values')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 49 |\u001b[39m     \u001b[36mconst\u001b[39m bankNameOptions \u001b[33m=\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 50 |\u001b[39m       config\u001b[33m.\u001b[39mvalues[createBeneficiaryGrpcInput\u001b[33m?\u001b[39m\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m?\u001b[39m\u001b[33m.\u001b[39mcurrency]\u001b[33m?\u001b[39m\u001b[33m.\u001b[39mname\u001b[33m?\u001b[39m\u001b[33m.\u001b[39moptions\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 51 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 52 |\u001b[39m     \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39mbankNameOptions) {\u001b[22m\n\u001b[2m     \u001b[90m 53 |\u001b[39m       \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mlogger\u001b[33m.\u001b[39mwarn(\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:50:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:307:9)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › initialize › bank configuration logic › should handle case when config values is null or undefined\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of null (reading 'USD')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 49 |\u001b[39m     \u001b[36mconst\u001b[39m bankNameOptions \u001b[33m=\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 50 |\u001b[39m       config\u001b[33m.\u001b[39mvalues[createBeneficiaryGrpcInput\u001b[33m?\u001b[39m\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m?\u001b[39m\u001b[33m.\u001b[39mcurrency]\u001b[33m?\u001b[39m\u001b[33m.\u001b[39mname\u001b[33m?\u001b[39m\u001b[33m.\u001b[39moptions\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 51 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 52 |\u001b[39m     \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39mbankNameOptions) {\u001b[22m\n\u001b[2m     \u001b[90m 53 |\u001b[39m       \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mlogger\u001b[33m.\u001b[39mwarn(\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:50:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:330:9)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › initialize › bank configuration logic › should handle empty options array\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: \u001b[32m\"BANK_OF_AMERICA\"\u001b[39m\n    Received: \u001b[31mundefined\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 406 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 407 |\u001b[39m         \u001b[90m// Bank name should remain unchanged when options array is empty\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 408 |\u001b[39m         expect(input\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankName)\u001b[33m.\u001b[39mtoBe(\u001b[32m'BANK_OF_AMERICA'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 409 |\u001b[39m       })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 410 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 411 |\u001b[39m       it(\u001b[32m'should handle contractService.getConfigByKey throwing an error'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:408:51)\u001b[22m\u001b[2m\u001b[22m\n"}], "wasInterrupted": false}
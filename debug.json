{"numFailedTestSuites": 1, "numFailedTests": 29, "numPassedTestSuites": 0, "numPassedTests": 3, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 1, "numTotalTests": 32, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1753166305296, "success": false, "testResults": [{"leaks": false, "numFailingTests": 29, "numPassingTests": 3, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1753166309299, "runtime": 3863, "slow": false, "start": 1753166305436}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts", "testResults": [{"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) should be defined", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should be defined"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "initialize"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:109:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) initialize should set internal fields correctly", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should set internal fields correctly"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "initialize"], "duration": 2, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:121:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) initialize should handle programService returning empty string", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle programService returning empty string"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "sync"], "duration": 2, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:128:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) sync should call createEmptyPayoutProfile, see isProfileAlreadyCreated() = false, call create, and return beneficiaryId", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should call createEmptyPayoutProfile, see isProfileAlreadyCreated() = false, call create, and return beneficiaryId"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "sync"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:128:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) sync should call update if profile is already created", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should call update if profile is already created"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "sync"], "duration": 2, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:128:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) sync should handle errors by calling handleBeneficiaryCreationError and throw RpcException", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle errors by calling handleBeneficiaryCreationError and throw RpcException"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "create"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:173:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) create should remove state fields if country != US, call registerPayee, then beneficiaryRpcClient.updateOne", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should remove state fields if country != US, call registerPayee, then beneficiaryRpcClient.updateOne"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "create"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:173:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) create should keep state fields if country == US", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should keep state fields if country == US"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "create"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:173:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) create should throw CustomError if registerPayee fails", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should throw CustomError if register<PERSON><PERSON><PERSON> fails"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "create"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:173:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) create should throw CustomError if updateOne returns error", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should throw CustomError if updateOne returns error"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "createEmptyPayoutProfile"], "duration": 2, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:239:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) createEmptyPayoutProfile should call createOne and set relevant fields on success", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should call createOne and set relevant fields on success"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "createEmptyPayoutProfile"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:239:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) createEmptyPayoutProfile should throw CustomError if createOne returns invalid data", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should throw CustomError if createOne returns invalid data"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "createEmptyPayoutProfile"], "duration": 2, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:239:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) createEmptyPayoutProfile should catch promise rejections and throw CustomError if createOne rejects", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should catch promise rejections and throw CustomError if createOne rejects"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "update"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:272:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) update should do nothing if there are no mismatches, then updateOne, and return beneficiaryId", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should do nothing if there are no mismatches, then updateOne, and return beneficiaryId"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "update"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:272:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) update should call updatePayeePersonalDetails if mismatch in personal details", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should call updatePayeePersonalDetails if mismatch in personal details"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "update"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:272:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) update should call updatePayeeBankAccountDetails if mismatch in bank details", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should call updatePayeeBankAccountDetails if mismatch in bank details"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "update"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:272:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) update should throw CustomError if updateOne returns an error object", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should throw CustomError if updateOne returns an error object"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "update"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:272:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) update should handle setPayeeDetails throwing an error, re-throw as RpcException", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle setPayeeDetails throwing an error, re-throw as RpcException"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "updatePayeePersonalDetails"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:357:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) updatePayeePersonalDetails should succeed if editPayeeProfile returns [data,null] and date_of_birth is valid", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should succeed if editPayeeProfile returns [data,null] and date_of_birth is valid"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "updatePayeePersonalDetails"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:357:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) updatePayeePersonalDetails should throw CustomError if editPayeeProfile fails", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should throw CustomError if editPayeeProfile fails"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "updatePayeePersonalDetails"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:357:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) updatePayeePersonalDetails should handle invalid date_of_birth scenario (RangeError) if you code for it", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle invalid date_of_birth scenario (RangeError) if you code for it"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "updatePayeeBankAccountDetails"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:405:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) updatePayeeBankAccountDetails should succeed if editTransferMethod returns [data, null]", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should succeed if editTransferMethod returns [data, null]"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "updatePayeeBankAccountDetails"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:405:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) updatePayeeBankAccountDetails should throw CustomError if editTransferMethod fails", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should throw CustomError if editTransferMethod fails"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "setPayeeDetails"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:425:7)"], "fullName": "CreateBeneficiaryForPayoneerGB<PERSON>andler (Full Coverage) setPayeeDetails should set payeeDetails if getPayeeExtendedDetails is successful", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should set payeeDetails if getPayeeExtendedDetails is successful"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "setPayeeDetails"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:425:7)"], "fullName": "CreateBeneficiaryForPayoneerGB<PERSON><PERSON>ler (Full Coverage) setPayeeDetails should throw CustomError if getPayeeExtendedDetails returns error", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should throw CustomError if getPayeeExtendedDetails returns error"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "setPayeeDetails"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:425:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) setPayeeDetails should throw CustomError if payeeInfo is null or undefined", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should throw CustomError if payeeInfo is null or undefined"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "validateAndGetRegisterPayeeInput"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:451:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) validateAndGetRegisterPayeeInput should return transformed register payee input on success", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return transformed register payee input on success"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "validateAndGetRegisterPayeeInput"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:451:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) validateAndGetRegisterPayeeInput should throw CustomError if getRegisterPayeeFormat returns an error", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should throw CustomError if getRegisterPayeeFormat returns an error"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "validateAndGetRegisterPayeeInput"], "duration": 7, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:451:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) validateAndGetRegisterPayeeInput should throw CustomError if format result missing payout_method.fields.items", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should throw CustomError if format result missing payout_method.fields.items"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "validateAndGetRegisterPayeeInput"], "duration": 0, "failureDetails": [{}], "failureMessages": ["TypeError: Cannot read properties of undefined (reading 'bankCountry')\n    at CreateBeneficiaryForPayoneerGBTHandler.initialize (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts:45:53)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts:451:7)"], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) validateAndGetRegisterPayeeInput should throw CustomError if validateAndTransformRegisterPayeeInput throws", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should throw CustomError if validateAndTransformRegisterPayeeInput throws"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "handleBeneficiaryCreationError"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) handleBeneficiaryCreationError should log the error and throw an RpcException", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should log the error and throw an RpcException"}, {"ancestorTitles": ["CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)", "handleBeneficiaryCreationError"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "CreateBeneficiaryForPayoneerGBTHandler (Full Coverage) handleBeneficiaryCreationError should use errorInfo?.message if available", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should use errorInfo?.message if available"}], "failureMessage": "\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › initialize › should set internal fields correctly\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:109:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › initialize › should handle programService returning empty string\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:121:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › sync › should call createEmptyPayoutProfile, see isProfileAlreadyCreated() = false, call create, and return beneficiaryId\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:128:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › sync › should call update if profile is already created\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:128:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › sync › should handle errors by calling handleBeneficiaryCreationError and throw RpcException\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:128:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › create › should remove state fields if country != US, call registerPayee, then beneficiaryRpcClient.updateOne\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:173:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › create › should keep state fields if country == US\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:173:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › create › should throw CustomError if registerPayee fails\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:173:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › create › should throw CustomError if updateOne returns error\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:173:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › createEmptyPayoutProfile › should call createOne and set relevant fields on success\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:239:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › createEmptyPayoutProfile › should throw CustomError if createOne returns invalid data\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:239:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › createEmptyPayoutProfile › should catch promise rejections and throw CustomError if createOne rejects\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:239:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › update › should do nothing if there are no mismatches, then updateOne, and return beneficiaryId\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:272:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › update › should call updatePayeePersonalDetails if mismatch in personal details\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:272:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › update › should call updatePayeeBankAccountDetails if mismatch in bank details\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:272:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › update › should throw CustomError if updateOne returns an error object\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:272:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › update › should handle setPayeeDetails throwing an error, re-throw as RpcException\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:272:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › updatePayeePersonalDetails › should succeed if editPayeeProfile returns [data,null] and date_of_birth is valid\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:357:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › updatePayeePersonalDetails › should throw CustomError if editPayeeProfile fails\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:357:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › updatePayeePersonalDetails › should handle invalid date_of_birth scenario (RangeError) if you code for it\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:357:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › updatePayeeBankAccountDetails › should succeed if editTransferMethod returns [data, null]\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:405:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › updatePayeeBankAccountDetails › should throw CustomError if editTransferMethod fails\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:405:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › setPayeeDetails › should set payeeDetails if getPayeeExtendedDetails is successful\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:425:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › setPayeeDetails › should throw CustomError if getPayeeExtendedDetails returns error\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:425:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › setPayeeDetails › should throw CustomError if payeeInfo is null or undefined\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:425:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › validateAndGetRegisterPayeeInput › should return transformed register payee input on success\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:451:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › validateAndGetRegisterPayeeInput › should throw CustomError if getRegisterPayeeFormat returns an error\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:451:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › validateAndGetRegisterPayeeInput › should throw CustomError if format result missing payout_method.fields.items\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:451:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCreateBeneficiaryForPayoneerGBTHandler (Full Coverage) › validateAndGetRegisterPayeeInput › should throw CustomError if validateAndTransformRegisterPayeeInput throws\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'bankCountry')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m { key\u001b[33m,\u001b[39m type } \u001b[33m=\u001b[39m getBankComfigKeys(\u001b[22m\n\u001b[2m     \u001b[90m 44 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mentityType\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 45 |\u001b[39m       createBeneficiaryGrpcInput\u001b[33m.\u001b[39mpayoutDetailsInput\u001b[33m.\u001b[39mbankCountry\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 46 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 47 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mcontractService\u001b[33m.\u001b[39mgetConfigByKey(key\u001b[33m,\u001b[39m type)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CreateBeneficiaryForPayoneerGBTHandler.initialize (\u001b[22m\u001b[2mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.ts\u001b[2m:45:53)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:451:7)\u001b[22m\u001b[2m\u001b[22m\n"}], "wasInterrupted": false}
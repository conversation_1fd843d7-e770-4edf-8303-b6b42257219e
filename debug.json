{"numFailedTestSuites": 1, "numFailedTests": 0, "numPassedTestSuites": 0, "numPassedTests": 0, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 1, "numTodoTests": 0, "numTotalTestSuites": 1, "numTotalTests": 0, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1753166856186, "success": false, "testResults": [{"failureMessage": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    Cannot find module 'src/common/external-service/contract-service/services/contract-service-gql.service' from 'beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts'\n\n    \u001b[0m \u001b[90m 19 |\u001b[39m } \u001b[36mfrom\u001b[39m \u001b[32m'../../../common/data-types/payoneer-v4.data.types'\u001b[39m\u001b[33m;\u001b[39m\n     \u001b[90m 20 |\u001b[39m \u001b[36mimport\u001b[39m { payoneer } \u001b[36mfrom\u001b[39m \u001b[32m'@skuad/proto-utils/dist/interface/proto/payoneer/payoneer'\u001b[39m\u001b[33m;\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 21 |\u001b[39m \u001b[36mimport\u001b[39m { \u001b[33mContractService\u001b[39m } \u001b[36mfrom\u001b[39m \u001b[32m'src/common/external-service/contract-service/services/contract-service-gql.service'\u001b[39m\u001b[33m;\u001b[39m\n     \u001b[90m    |\u001b[39m \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 22 |\u001b[39m\n     \u001b[90m 23 |\u001b[39m describe(\u001b[32m'CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n     \u001b[90m 24 |\u001b[39m   \u001b[36mlet\u001b[39m handler\u001b[33m:\u001b[39m \u001b[33mCreateBeneficiaryForPayoneerGBTHandler\u001b[39m\u001b[33m;\u001b[39m\u001b[0m\n\n      \u001b[2mat Resolver._throwModNotFoundError (\u001b[22m../node_modules/jest-resolve/build/resolver.js\u001b[2m:427:11)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[0m\u001b[36mbeneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts\u001b[39m\u001b[0m\u001b[2m:21:1)\u001b[22m\n", "leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 0, "runtime": 0, "slow": false, "start": 0}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testExecError": {"code": "MODULE_NOT_FOUND", "hint": "", "requireStack": ["/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts"], "siblingWithSimilarExtensionFound": false, "moduleName": "src/common/external-service/contract-service/services/contract-service-gql.service", "_originalMessage": "Cannot find module 'src/common/external-service/contract-service/services/contract-service-gql.service' from 'beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts'"}, "testFilePath": "/Users/<USER>/Documents/SkuadCode/payoneer/src/beneficiary/services/handlers/create-beneficiary-payoneer-gbt-handler.service.spec.ts", "testResults": []}], "wasInterrupted": false}
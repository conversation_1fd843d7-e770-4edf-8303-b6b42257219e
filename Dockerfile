FROM node:20-alpine AS builder
RUN apk add --no-cache --virtual .gyp bash git
WORKDIR /app
COPY package*.json .npmrc ./
RUN npm install --no-progress --prefer-offline --no-audit
COPY . .
RUN npm run build

FROM node:20-alpine AS app
RUN apk add --no-cache bash curl
WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /usr/lib /usr/lib/
COPY --from=builder /app/dist ./dist
COPY . .
RUN chown -R node:node /app
USER node
CMD [ "npm", "run", "start" ]

// import * as dotenv from 'dotenv';
const dotenv = require('dotenv');
dotenv.config();
const requestTypes = {
  HTTP: 'HTTP',
  GRAPHQL: 'GRAPHQL',
  GRPC: 'GRPC',
};

module.exports = {
  htBackendBaseUrl: 'http://v2-beta-external.hypertest.co:8001',

  // URL of HyperTest server (Required)
  // "htCliRefreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiIxMiIsImlhdCI6MTcwOTI3MTQ4OSwiZXhwIjoxOTY4NDcxNDg5fQ.ydeb5x8BjJvloavgwEjh4H0HMwKp59TGd6PHGAtqrm8",  // Auth token for the CLI (Required)
  // Auth token for the CLI (Required)
  serviceIdentifier: 'bf823165-02ee-4bb7-af69-a1657ee8953d', // UUID for the service (Required)
  requestTypesToTest: [requestTypes.GRPC], // What kind of requests to include in the test
  // "httpCandidateUrl": "", // HTTP URL of App under test (Optional)
  // "graphqlCandidateUrl": "http://localhost:5001", // GraphQL URL of App under test (Optional)
  appStartCommand: 'npm', // Command to start the app (Required)
  appStartCommandArgs: ['run', 'start:nyc'], // App start command arguments (Required)
  appWorkingDirectory: __dirname, // Working directory for the app (default: current working dir) (Optional)
  appStartTimeoutSec: 90, // Timeout for the start command (default: 10) (Optional)
  showAppLogs: true, // Whether to show app logs (default: false) (Optional)
  shouldReportHeaderDiffs: false, // Whether to report differences in headers (default: false) (Optional)
  testBatchSize: 50, // Number of concurrent test requests (default: 50) (Optional)
  // "testRequestsLimit": 10, // Number requests to test (Optional)
  httpReqFiltersArr: [], // "<GET /users>", "<ANY REGEX:^/payments>" (Optional)
  htExtraHeaders: { authorization: 'Basic SHlwZXJUZXN0LURlbW86SHlwZXJUZXN0LURlbW8=' }, // Object containing additional headers for HyperTest server requests (Optional)
  fastMode: false,
  shouldExcludeUnmodifiedRequests: true,
  shouldIgnoreDifferencesInUnmodifiedRequests: true,
  masterBranch: process.env.HYPT_COV_BRANCH || 'master',
  reservedAppPorts: [3000],
  // "initialTimestamp": "", // Initial timestamp in ISO format (Optional)
  // "finalTimestamp": "", // Final timestamp in ISO format (Optional)
  // "cliServerHost": "", // HT CLI server Host to be Used by Clients(server ignores this) (default: localhost) (Optional)
  // "sdkServerHost": "", // HT SDK server Host to be Used by Clients(server ignores this) (default: localhost) (Optional)
  // exitCodeSetter({ testResult }) {
  //    console.log('==test results==')
  //    if testResult
  //    console.log(testResult)
  //    return 0;
  //   },
  exitCodeSetter({ testResult }) {
    console.log('==test results==');
    if (testResult) {
      console.log(testResult);
      if (testResult.failed > 0) {
        console.log('Setting exit code to 1');
        return 1; // Exit code 1 if there are failed tests
      } else {
        console.log('Setting exit code to 0');
        return 0; // Exit code 0 if there are no failed tests
      }
    }
  },
};
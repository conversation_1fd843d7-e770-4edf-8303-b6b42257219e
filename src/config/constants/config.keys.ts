export enum ConfigKeys {
  PROVIDER_API_BASE_URL = 'PROVIDER_API_BASE_URL',
  PROVIDER_API_AUTH_URL = 'PROVIDER_API_AUTH_URL',
  PROVIDER_PROGRAM_ID = 'PROVIDER_PROGRAM_ID',
  PROVIDER_CLIENT_ID = 'PROVIDER_CLIENT_ID',
  PROVIDER_CLIENT_SECRET = 'PROVIDER_CLIENT_SECRET',
  GRPC_SERVICE_PORT = 'GRPC_SERVICE_PORT',
  GRPC_PAYMENT_SERVICE_URL = 'GRPC_PAYMENT_SERVICE_URL',
  PAYMENT_SERVICE_GRPC_API_KEY = 'PAYMENT_SERVICE_GRPC_API_KEY',
  PAYMENT_PROCESSOR_CRON_JOB_ENABLED = 'PAYMENT_PROCESSOR_CRON_JOB_ENABLED',
  PAYMENT_PROCESSOR_CRON_JOB_PATTERN = 'PAYMENT_PROCESSOR_CRON_JOB_PATTERN',
  PORT = 'PORT',

  PROVIDER_CREDENTIALS = 'PROVIDER_CREDENTIALS',
  PRIVATE_API_KEY = 'PRIVATE_API_KEY',
  CRON_CONFIGS = 'CRON_CONFIGS',
  HTTP_REQUEST_TIMEOUT_IN_MS = 'HTTP_REQUEST_TIMEOUT_IN_MS',
  API_PATH_PREFIX = 'API_PATH_PREFIX',
  PAYONEER_SERVICE_PG_DB = 'PAYONEER_SERVICE_PG_DB',
  ACCESS_TOKEN = 'ACCESS_TOKEN',
  CHECK_PAYEE_ACCOUNT_EXISTS_ENABLED = 'CHECK_PAYEE_ACCOUNT_EXISTS_ENABLED',
  GATEWAY_URL = 'GATEWAY_URL',
  CONTRACT_SERVICE_URL = 'CONTRACT_SERVICE_URL',
  SKUAD_SINGAPORE_ENTITY_ID = 'SKUAD_SINGAPORE_ENTITY_ID',
  USER_TOKEN = 'USER_TOKEN',

  REDIRECT_URI = 'REDIRECT_URI',
  SSO_PROGRAM_ID = 'SSO_PROGRAM_ID',
  PWP_CLIENT_ID = 'PWP_CLIENT_ID',
  PWP_CLIENT_SECRET = 'PWP_CLIENT_SECRET',
}

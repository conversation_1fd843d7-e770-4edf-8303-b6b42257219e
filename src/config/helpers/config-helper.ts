import { ConfigService } from "@nestjs/config";
import { Injectable } from "@nestjs/common";
import { CronConfigDto } from "../dto/cron-config.dto";
import { decodeFromBase64, toDto } from "../../common/helpers/utils";

@Injectable()
export class ConfigHelper {
  private readonly configService;

  constructor() {
    this.configService = new ConfigService();
  }

  getNumberOrError(key: string): number {
    return parseInt(this.getValOrError(key));
  }

  getConfigMap<T>(key: string): Map<string, T> {
    try {
      const decodedConfig = Buffer.from(key, "base64").toString("utf-8");
      const data = JSON.parse(decodedConfig);
      const newMap = new Map<string, any>();
      const map = this.convertToMap<T>(newMap, data, Object.keys(data));
      if (!map) throw Error("Error converting config to map");
      return map;
    } catch (e) {
      throw new Error(`[config error] Invalid config for key: ${key}`);
    }
  }

  convertToMap = <T>(
    emptyMap: Map<string, T>,
    object: Record<string, any>,
    keys: string[]
  ): Map<string, T> | undefined => {
    if (!keys.length) return emptyMap;

    const key = keys.shift();

    if (key) {
      let value = object[key];

      if (typeof value === "object") {
        value = this.convertToMap(new Map(), value, Object.keys(value));
      }
      emptyMap.set(key, value);
      return this.convertToMap(emptyMap, object, keys);
    }
  };


  getNumberWithDefault(key: string, defaultVal: number): number {
    return parseInt(this.getValWithDefault(key, defaultVal));
  }

  getStringOrError(key: string): string {
    return this.getValOrError(key);
  }

  getStringWithDefault(key: string, defaultVal: string): string {
    return this.getValWithDefault(key, defaultVal);
  }

  getBoolOrError(key: string): boolean {
    return JSON.parse(this.getValOrError(key));
  }

  getBoolWithDefault(key: string, defaultVal: boolean): boolean {
    return JSON.parse(this.getValWithDefault(key, defaultVal));
  }

  private getValOrError(key: string): any {
    const val = this.configService.get(key);
    if (!val) {
      throw new Error(`[config error] ${key} key is missing`);
    }
    return val;
  }

  private getValWithDefault(key: string, defaultVal: any): any {
    return this.configService.get(key, defaultVal);
  }

  getCronConfigs(base64Str: string): CronConfigDto[] {
    try {
      const decodedConfig = decodeFromBase64(base64Str);
      const parsedConfig = JSON.parse(decodedConfig);
      return toDto<CronConfigDto[]>(CronConfigDto, parsedConfig);
    } catch (e) {
      throw new Error(`[config error] cron config should be base64 encoded string`);
    }
  }

}

import { Global, Module } from '@nestjs/common';
import { ConfigModule as Config } from '@nestjs/config';
import { AppConfigService } from './services/app.config.service';
import { ConfigHelper } from './helpers/config-helper';
import { CronConfigGenerator } from './generators/cron.config.generator';

@Global()
@Module({
  imports: [Config.forRoot({})],
  providers: [ConfigHelper, AppConfigService, CronConfigGenerator],
  exports: [AppConfigService],
})
export class ConfigModule { }

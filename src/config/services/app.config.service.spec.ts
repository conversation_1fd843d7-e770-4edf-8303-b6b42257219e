import { Test, TestingModule } from '@nestjs/testing';
import { AppConfigService } from './app.config.service';
import { ConfigHelper } from '../helpers/config-helper';
import { CronConfigGenerator } from '../generators/cron.config.generator';
import { ConfigKeys } from '../constants/config.keys';

describe('AppConfigService', () => {
  let service: AppConfigService;
  let configHelper: ConfigHelper;
  let cronConfig: CronConfigGenerator;

  const mockConfigHelper = {
    getStringOrError: jest.fn(),
    getNumberWithDefault: jest.fn(),
    getBoolWithDefault: jest.fn(),
    getStringWithDefault: jest.fn(),
    getConfigMap: jest.fn(),
  };

  const mockCronConfig = {};

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AppConfigService,
        {
          provide: ConfigHelper,
          useValue: mockConfigHelper,
        },
        {
          provide: CronConfigGenerator,
          useValue: mockCronConfig,
        },
      ],
    }).compile();

    service = module.get<AppConfigService>(AppConfigService);
    configHelper = module.get<ConfigHelper>(ConfigHelper);
    cronConfig = module.get<CronConfigGenerator>(CronConfigGenerator);
  });

  beforeEach(() => {
    jest.resetAllMocks();
    mockConfigHelper.getStringOrError.mockImplementation((key) => `mock_${key}`);
    mockConfigHelper.getNumberWithDefault.mockImplementation((key, defaultValue) => defaultValue);
    mockConfigHelper.getBoolWithDefault.mockImplementation((key, defaultValue) => defaultValue);
    mockConfigHelper.getStringWithDefault.mockImplementation((key, defaultValue) => defaultValue);
    mockConfigHelper.getConfigMap.mockReturnValue(new Map());
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getters', () => {
    it('should return API base URL', () => {
      expect(service.getAPIBaseUrl()).toBe('mock_PROVIDER_API_BASE_URL');
    });

    it('should return API auth URL', () => {
      expect(service.getAPIAuthUrl()).toBe('mock_PROVIDER_API_AUTH_URL');
    });

    it('should return login authorization token', () => {
      const expected = Buffer.from('mock_PROVIDER_CLIENT_ID:mock_PROVIDER_CLIENT_SECRET').toString('base64');
      expect(service.getLoginAuthorizationToken()).toBe(expected);
    });

    it('should return gRPC service port', () => {
      expect(service.getGrpcServicePort()).toBe(5001);
    });

    it('should return service port', () => {
      expect(service.getServicePort()).toBe(3000);
    });

    it('should return HTTP timeout', () => {
      expect(service.getHttpTimeout()).toBe(60000);
    });
  });

  describe('postgres config', () => {
    it('should get postgres config', async () => {
      mockConfigHelper.getStringOrError.mockReturnValue('{}');

      const module: TestingModule = await Test.createTestingModule({
        providers: [
          AppConfigService,
          {
            provide: ConfigHelper,
            useValue: mockConfigHelper,
          },
          {
            provide: CronConfigGenerator,
            useValue: mockCronConfig,
          },
        ],
      }).compile();

      const localService = module.get<AppConfigService>(AppConfigService);
      expect(localService.getPayoneerPostgresConfig()).toEqual({});
    });


    it('should throw error for invalid postgres config', async () => {
      mockConfigHelper.getStringOrError.mockReturnValue('invalid');

      const module: TestingModule = await Test.createTestingModule({
        providers: [
          AppConfigService,
          {
            provide: ConfigHelper,
            useValue: mockConfigHelper,
          },
          {
            provide: CronConfigGenerator,
            useValue: mockCronConfig,
          },
        ],
      }).compile();

      const localService = module.get<AppConfigService>(AppConfigService);

      expect(() => localService.getPayoneerPostgresConfig()).toThrow(
        '[config error] payoneer postgres config could not be set',
      );
    });

  });

  describe('feature flags', () => {
    it('should check if payee account exists is enabled', () => {
      expect(service.isCheckPayeeAccountExistsEnabled()).toBeFalsy();
    });

    it('should check if cron job is enabled', () => {
      expect(service.getCronJobEnabled()).toBeTruthy();
    });
  });

  it('should return cron config', () => {
    expect(service.cron()).toBe(mockCronConfig);
  });

  it('should return private API key', () => {
    expect(service.getPrivateAPIKey()).toBe('mock_PRIVATE_API_KEY');
  });

  it('should set and get provider credentials map', () => {
    service.setProviderCredentialsMap();
    expect(service.getProviderCredentialsMap()).toBeInstanceOf(Map);
  });

  describe('provider credentials', () => {
    it('should check if provider credentials are available for geography', () => {
      const mockMap = new Map();
      mockMap.set('US', 'credentials');
      mockConfigHelper.getConfigMap.mockReturnValue(mockMap);

      service.setProviderCredentialsMap();
      expect(service.isProviderCredentialsAvailable('US')).toBeTruthy();
      expect(service.isProviderCredentialsAvailable('UK')).toBeFalsy();
    });
  });

  describe('grpc config', () => {
    it('should return grpc payment service url', () => {
      expect(service.getGrpcPaymentServiceUrl()).toBe('mock_GRPC_PAYMENT_SERVICE_URL');
    });

    it('should return payment service grpc api key', () => {
      expect(service.getPaymentServiceGrpcApiKey()).toBe('mock_PAYMENT_SERVICE_GRPC_API_KEY');
    });
  });

  describe('api config', () => {
    it('should return api path prefix', () => {
      expect(service.getApiPathPrefix()).toBe('api');
    });
  });
});

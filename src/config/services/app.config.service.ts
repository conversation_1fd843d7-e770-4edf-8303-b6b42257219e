import { Injectable } from '@nestjs/common';
import { ConfigKeys } from '../constants/config.keys';
import { ConfigHelper } from '../helpers/config-helper';
import { CronConfigGenerator } from '../generators/cron.config.generator';

@Injectable()
export class AppConfigService {
  private readonly apiBaseUrl: string;
  private readonly apiAuthUrl: string;

  private readonly providerClientId: string;
  private readonly providerClientSecret: string;
  private readonly grpcServicePort: number;
  private readonly grpcPaymentServiceUrl: string;
  private readonly paymentServiceGrpcApiKey: string;
  private readonly cronJobEnabled: boolean;
  private readonly servicePort: number;

  private providerCredentialsMap = new Map();
  private readonly httpRequestTimeout: number;
  private readonly privateAPIKey: string;
  private readonly apiPathPrefix: string;

  private readonly payoneerPostgresConfig: string;

  private readonly checkPayeeAccountExistsEnabled: boolean;

  private readonly gatewayUrl: string;
  private readonly contractServiceUrl: string;
  private readonly skuadSGId: string;
  private readonly userToken: string;

  private readonly providerProgramId: string;

  private readonly redirectUri: string;

  private readonly ssoProgramID: string;
  private readonly pwpClientId: string;
  private readonly pwpClientSecret: string;

  constructor(
    private readonly cronConfig: CronConfigGenerator,
    private readonly configHelper: ConfigHelper,
  ) {
    this.providerProgramId = this.configHelper.getStringOrError(ConfigKeys.PROVIDER_PROGRAM_ID);
    this.apiBaseUrl = this.configHelper.getStringOrError(ConfigKeys.PROVIDER_API_BASE_URL);
    this.apiAuthUrl = this.configHelper.getStringOrError(ConfigKeys.PROVIDER_API_AUTH_URL);
    this.providerClientId = this.configHelper.getStringOrError(ConfigKeys.PROVIDER_CLIENT_ID);
    this.providerClientSecret = this.configHelper.getStringOrError(ConfigKeys.PROVIDER_CLIENT_SECRET);
    this.grpcServicePort = this.configHelper.getNumberWithDefault(ConfigKeys.GRPC_SERVICE_PORT, 5001);
    this.grpcPaymentServiceUrl = this.configHelper.getStringOrError(ConfigKeys.GRPC_PAYMENT_SERVICE_URL);
    this.paymentServiceGrpcApiKey = this.configHelper.getStringOrError(
      ConfigKeys.PAYMENT_SERVICE_GRPC_API_KEY,
    );
    this.cronJobEnabled = this.configHelper.getBoolWithDefault(
      ConfigKeys.PAYMENT_PROCESSOR_CRON_JOB_ENABLED,
      true,
    );
    this.servicePort = this.configHelper.getNumberWithDefault(ConfigKeys.PORT, 3000);

    this.httpRequestTimeout = this.configHelper.getNumberWithDefault(
      ConfigKeys.HTTP_REQUEST_TIMEOUT_IN_MS,
      1000 * 60,
    );
    this.privateAPIKey = this.configHelper.getStringOrError(ConfigKeys.PRIVATE_API_KEY);

    this.providerCredentialsMap = this.configHelper.getConfigMap<Map<string, string>>(
      this.configHelper.getStringOrError(ConfigKeys.PROVIDER_CREDENTIALS),
    );
    this.apiPathPrefix = this.configHelper.getStringWithDefault(ConfigKeys.API_PATH_PREFIX, 'api');

    this.payoneerPostgresConfig = this.configHelper.getStringOrError(ConfigKeys.PAYONEER_SERVICE_PG_DB);

    this.checkPayeeAccountExistsEnabled = this.configHelper.getBoolWithDefault(
      ConfigKeys.CHECK_PAYEE_ACCOUNT_EXISTS_ENABLED,
      false,
    );

    this.gatewayUrl = this.configHelper.getStringOrError(ConfigKeys.GATEWAY_URL);
    this.contractServiceUrl = this.configHelper.getStringOrError(ConfigKeys.CONTRACT_SERVICE_URL);
    this.skuadSGId = this.configHelper.getStringOrError(ConfigKeys.SKUAD_SINGAPORE_ENTITY_ID);
    this.userToken = this.configHelper.getStringOrError(ConfigKeys.USER_TOKEN);

    this.redirectUri = this.configHelper.getStringOrError(ConfigKeys.REDIRECT_URI);
    this.ssoProgramID = this.configHelper.getStringOrError(ConfigKeys.SSO_PROGRAM_ID);
    this.pwpClientId = this.configHelper.getStringOrError(ConfigKeys.PWP_CLIENT_ID);
    this.pwpClientSecret = this.configHelper.getStringOrError(ConfigKeys.PWP_CLIENT_SECRET);
  }

  getAPIBaseUrl(): string {
    return this.apiBaseUrl;
  }

  getAPIAuthUrl(): string {
    return this.apiAuthUrl;
  }

  getLoginAuthorizationToken(applicationId?: string): string {
    let clientId = this.providerClientId;
    let clientSecret = this.providerClientSecret;

    if (applicationId && applicationId == this.pwpClientId) {
      clientId = this.pwpClientId;
      clientSecret = this.pwpClientSecret;
    }
    const token = `${clientId}:${clientSecret}`;
    const buffer = Buffer.from(token).toString('base64');
    return `${buffer}`;
  }

  getGrpcServicePort(): number {
    return this.grpcServicePort;
  }

  getGrpcPaymentServiceUrl(): string {
    return this.grpcPaymentServiceUrl;
  }

  getPaymentServiceGrpcApiKey(): string {
    return this.paymentServiceGrpcApiKey;
  }

  getCronJobEnabled(): boolean {
    return this.cronJobEnabled;
  }

  getApiPathPrefix(): string {
    return this.apiPathPrefix;
  }

  getServicePort(): number {
    return this.servicePort;
  }

  cron() {
    return this.cronConfig;
  }

  getHttpTimeout(): number {
    return this.httpRequestTimeout;
  }

  getPrivateAPIKey(): string {
    return this.privateAPIKey;
  }

  setProviderCredentialsMap() {
    this.providerCredentialsMap = this.configHelper.getConfigMap<Map<string, string>>(
      ConfigKeys.PROVIDER_CREDENTIALS,
    );
  }

  getProviderCredentialsMap() {
    return this.providerCredentialsMap;
  }

  isProviderCredentialsAvailable(geography: string) {
    return this.providerCredentialsMap.has(geography);
  }

  getPayoneerPostgresConfig(): Record<string, any> {
    try {
      return JSON.parse(this.payoneerPostgresConfig) || {};
    } catch (error) {
      throw new Error(`[config error] payoneer postgres config could not be set`);
    }
  }

  isCheckPayeeAccountExistsEnabled(): boolean {
    return this.checkPayeeAccountExistsEnabled;
  }

  getGatewayUrl(): string {
    return this.gatewayUrl;
  }

  getContractServiceUrl(): string {
    return this.contractServiceUrl;
  }

  getSkuadSGId(): string {
    return this.skuadSGId;
  }

  getUserToken(): string {
    return this.userToken;
  }

  getProviderProgramId(): string {
    return this.providerProgramId;
  }

  getSSOProgramId(): string {
    return this.ssoProgramID;
  }

  getPWPClientId(): string {
    return this.pwpClientId;
  }

  getPWPClientSecret(): string {
    return this.pwpClientSecret;
  }

  getRedirectUri(): string {
    return this.redirectUri;
  }

  getProviderClientId(): string {
    return this.providerClientId;
  }
}

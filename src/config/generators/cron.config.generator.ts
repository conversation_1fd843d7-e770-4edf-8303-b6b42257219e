import { ConfigHelper } from '../helpers/config-helper';
import { ConfigKeys } from '../constants/config.keys';
import { Injectable } from '@nestjs/common';
import { CronConfigDto } from '../dto/cron-config.dto';
import { ICronConfig } from '../../common/interfaces/params.interface';
import { CronJobName } from '../../common/constants/enums';
import { Logger } from '@nestjs/common';

@Injectable()
export class CronConfigGenerator {
  private readonly logger = new Logger(CronConfigGenerator.name);
  private readonly base64CronConfig: string;
  private readonly cronConfigMap = new Map<string, CronConfigDto>();

  constructor(private readonly configHelper: ConfigHelper) {
    this.base64CronConfig = configHelper.getStringWithDefault(
      ConfigKeys.CRON_CONFIGS,
      'W3sibmFtZSI6IkZldGNoUGVuZGluZ1BheW91dHNDcm9uSm9iIiwidGltZVpvbmUiOiJVVEMiLCJwYXR0ZXJuIjoiKi8yICogKiAqICoiLCJvcHRpb25zIjp7ImVuYWJsZWQiOnRydWV9fSx7Im5hbWUiOiJSZWZyZXNoRXhwaXJpbmdUb2tlbiIsInRpbWVab25lIjoiVVRDIiwicGF0dGVybiI6IjAgMCAqICogKiIsIm9wdGlvbnMiOnsiZW5hYmxlZCI6dHJ1ZX19XQ==',
    );
    this.initCronConfigMap();
  }

  fetchPendingPayoutsCronConfig(): ICronConfig | undefined {
    return this.findCronConfigByName(CronJobName.FetchPendingPayouts);
  }

  fetchPendingPayoutsCronConfigForPayoneerGBT(): ICronConfig | undefined {
    return this.findCronConfigByName(CronJobName.FetchPendingPayoutsCronJobForPayoneerGBT);
  }

  refreshTokenCronConfig(): ICronConfig | undefined {
    return this.findCronConfigByName(CronJobName.RefreshExpiringToken);
  }

  populateFundingAccounts(): ICronConfig | undefined {
    return this.findCronConfigByName(CronJobName.PopulateFundingAccounts);
  }

  autoDebitProcessor(): ICronConfig | undefined {
    return this.findCronConfigByName(CronJobName.AutoDebitProcessor);
  }

  private findCronConfigByName(name: string): ICronConfig | undefined {
    const config = this.cronConfigMap.get(name);
    if (!config) {
      this.logger.warn({ message: `cron config for name: ${name} not found` });
      return undefined;
    }
    return config;
  }

  private initCronConfigMap() {
    const cronConfigDtos = this.configHelper.getCronConfigs(this.base64CronConfig);
    for (const configDto of cronConfigDtos) {
      this.cronConfigMap.set(configDto.cronName, configDto);
    }
  }
}

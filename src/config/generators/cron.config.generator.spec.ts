import { Test, TestingModule } from '@nestjs/testing';
import { CronConfigGenerator } from './cron.config.generator';
import { ConfigHelper } from '../helpers/config-helper';
import { CronJobName } from '../../common/constants/enums';
import { Logger } from '@nestjs/common';

describe('CronConfigGenerator', () => {
  let service: CronConfigGenerator;
  let configHelper: ConfigHelper;

  const mockCronConfigs = [
    {
      cronName: CronJobName.FetchPendingPayouts,
      timeZone: 'UTC',
      pattern: '*/2 * * * *',
      options: { enabled: true },
    },
    {
      cronName: CronJobName.RefreshExpiringToken,
      timeZone: 'UTC',
      pattern: '0 0 * * *',
      options: { enabled: true },
    },
    {
      cronName: CronJobName.AutoDebitProcessor,
      timeZone: 'UTC',
      pattern: '15 3 * * *',
      options: { enabled: true },
    },
    {
      cronName: CronJobName.FetchPendingPayoutsCronJobForPayoneerGBT,
      timeZone: 'UTC',
      pattern: '*/10 * * * *',
      options: { enabled: true },
    },
  ];

  const mockConfigHelper = {
    getStringWithDefault: jest.fn(),
    getCronConfigs: jest.fn(),
  };

  beforeEach(async () => {
    mockConfigHelper.getStringWithDefault.mockReturnValue('base64string');
    mockConfigHelper.getCronConfigs.mockReturnValue(mockCronConfigs);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CronConfigGenerator,
        {
          provide: ConfigHelper,
          useValue: mockConfigHelper,
        },
      ],
    }).compile();

    service = module.get<CronConfigGenerator>(CronConfigGenerator);
    configHelper = module.get<ConfigHelper>(ConfigHelper);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('fetchPendingPayoutsCronConfig', () => {
    it('should return config for fetch pending payouts when present', () => {
      const result = service.fetchPendingPayoutsCronConfig();
      expect(result).toEqual(mockCronConfigs[0]);
    });

    it('should return undefined when config is missing', () => {
      mockConfigHelper.getCronConfigs.mockReturnValue([]);
      const newService = new CronConfigGenerator(configHelper);
      const result = newService.fetchPendingPayoutsCronConfig();
      expect(result).toBeUndefined();
    });

    it('should call findCronConfigByName internally', () => {
      const spy = jest.spyOn<any, any>(service, 'findCronConfigByName');
      service.fetchPendingPayoutsCronConfig();
      expect(spy).toHaveBeenCalledWith(CronJobName.FetchPendingPayouts);
    });
  });

  describe('refreshTokenCronConfig', () => {
    it('should return config for refresh token when present', () => {
      const result = service.refreshTokenCronConfig();
      expect(result).toEqual(mockCronConfigs[1]);
    });

    it('should return undefined when config is missing', () => {
      mockConfigHelper.getCronConfigs.mockReturnValue([]);
      const newService = new CronConfigGenerator(configHelper);
      const result = newService.refreshTokenCronConfig();
      expect(result).toBeUndefined();
    });
  });

  describe('autoDebitProcessor', () => {
    it('should return config for auto debit processor when present', () => {
      const result = service.autoDebitProcessor();
      expect(result).toEqual(mockCronConfigs[2]);
    });

    it('should return undefined when config is missing', () => {
      mockConfigHelper.getCronConfigs.mockReturnValue([]);
      const newService = new CronConfigGenerator(configHelper);
      const result = newService.autoDebitProcessor();
      expect(result).toBeUndefined();
    });

    it('should call findCronConfigByName with AutoDebitProcessor', () => {
      const spy = jest.spyOn<any, any>(service, 'findCronConfigByName');
      service.autoDebitProcessor();
      expect(spy).toHaveBeenCalledWith(CronJobName.AutoDebitProcessor);
    });
  });

  describe('fetchPendingPayoutsCronConfigForPayoneerGBT', () => {
    it('should return config when present in cronConfigMap', () => {
      const result = service.fetchPendingPayoutsCronConfigForPayoneerGBT();
      expect(result).toEqual(mockCronConfigs[3]);
    });

    it('should return undefined when config is missing', () => {
      mockConfigHelper.getCronConfigs.mockReturnValue([]);
      const newService = new CronConfigGenerator(configHelper);
      const result = newService.fetchPendingPayoutsCronConfigForPayoneerGBT();
      expect(result).toBeUndefined();
    });

    it('should call findCronConfigByName with FetchPendingPayoutsCronJobForPayoneerGBT', () => {
      const spy = jest.spyOn<any, any>(service, 'findCronConfigByName');
      service.fetchPendingPayoutsCronConfigForPayoneerGBT();
      expect(spy).toHaveBeenCalledWith(CronJobName.FetchPendingPayoutsCronJobForPayoneerGBT);
    });
  });

  describe('populateFundingAccounts', () => {
    it('should return undefined when config not found', () => {
      const result = service.populateFundingAccounts();
      expect(result).toBeUndefined();
    });
  });

  describe('initialization', () => {
    it('should call configHelper methods with correct params', () => {
      expect(mockConfigHelper.getStringWithDefault).toHaveBeenCalledWith('CRON_CONFIGS', expect.any(String));
      expect(mockConfigHelper.getCronConfigs).toHaveBeenCalledWith('base64string');
    });

    it('should initialize cronConfigMap with provided configs', () => {
      const configs = service['cronConfigMap'];
      expect(configs.get(CronJobName.FetchPendingPayouts)).toEqual(mockCronConfigs[0]);
      expect(configs.get(CronJobName.RefreshExpiringToken)).toEqual(mockCronConfigs[1]);
      expect(configs.get(CronJobName.AutoDebitProcessor)).toEqual(mockCronConfigs[2]);
      expect(configs.get(CronJobName.FetchPendingPayoutsCronJobForPayoneerGBT)).toEqual(mockCronConfigs[3]);
    });
  });

  describe('findCronConfigByName', () => {
    it('should return undefined and log a warning when config is not found', () => {
      const loggerSpy = jest.spyOn(Logger.prototype, 'warn').mockImplementation();
      const result = service['findCronConfigByName']('NonExistentJob' as any);
      expect(result).toBeUndefined();
      expect(loggerSpy).toHaveBeenCalledWith({
        message: 'cron config for name: NonExistentJob not found',
      });
    });
  });
});

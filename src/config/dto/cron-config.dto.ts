import { Expose } from 'class-transformer';
import { ICronConfig, ICronConfigOptions } from '../../common/interfaces/params.interface';

export class CronConfigDto implements ICronConfig {
  @Expose({ name: 'pattern' })
  cronPattern?: string;

  @Expose({ name: 'name' })
  cronName?: string;

  @Expose({ name: 'timeZone' })
  cronTimeZone?: string;

  @Expose()
  cronUtcOffset?: string | number;

  @Expose()
  cronUnrefTimeout?: boolean;

  @Expose({ name: 'options' })
  cronOptions?: ICronConfigOptions;
}

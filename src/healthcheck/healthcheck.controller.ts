import { Controller, Get, Logger, Response } from '@nestjs/common';
import {
  HealthCheck,
  HealthCheckService,
  TypeOrmHealthIndicator,
  MicroserviceHealthIndicator,
} from '@nestjs/terminus';
import { Transport } from '@nestjs/microservices';

@Controller('health')
export class HealthController {
  private readonly logger = new Logger(HealthController.name);
  constructor(
    private readonly health: HealthCheckService,
    private readonly postgress: TypeOrmHealthIndicator,
    private readonly grpc: MicroserviceHealthIndicator,
  ) {}

  @Get()
  @HealthCheck()
  async checkConnectionHealth(@Response() res: any) {
    try {
      const healthcheckIndicators = [];

      healthcheckIndicators.push(() =>
        this.grpc.pingCheck('GrpcMicroservice', {
          transport: Transport.TCP,
          options: {
            url: '0.0.0.0'.concat(':').concat(process.env.GRPC_SERVICE_PORT),
          },
        }),
      );

      healthcheckIndicators.push(() => this.postgress.pingCheck('postgress'));

      const result = await this.health.check(healthcheckIndicators);

      if (result.status !== 'ok') {
        this.logger.error({
          message: 'connection(s) down',
          details: result.details,
        });
        return res.status(500).send(result?.details);
      }

      return res.status(200).send(result?.details);
    } catch (error) {
      this.logger.error({
        message: 'Error checking connections',
        error: error?.response?.error,
      });

      return res.status(500).send('connection(s) down');
    }
  }
}

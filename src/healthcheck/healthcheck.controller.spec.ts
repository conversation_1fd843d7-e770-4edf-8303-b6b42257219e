/* eslint-disable @typescript-eslint/no-unused-vars */
import { Test, TestingModule } from '@nestjs/testing';
import { HealthController } from './healthcheck.controller';
import { HealthCheckService, MicroserviceHealthIndicator, TypeOrmHealthIndicator } from '@nestjs/terminus';
import each from 'jest-each';
import { EntityManager } from 'typeorm';

describe(HealthCheckService.name, () => {
  let module: TestingModule;
  let controller: HealthController;
  let healthCheckService: HealthCheckService;
  const originalEnv = process.env;

  const canSkipTest = (condition: boolean) => (condition ? test.skip : test);

  const setupTest = async () => {
    module = await Test.createTestingModule({
      imports: [],
      controllers: [HealthController],
      providers: [
        {
          provide: HealthCheckService,
          useFactory: () => ({
            check: jest.fn(),
          }),
        },
        {
          provide: MicroserviceHealthIndicator,
          useValue: {
            pingCheck: jest.fn(),
          },
        },
        {
          provide: TypeOrmHealthIndicator,
          useValue: {
            pingCheck: jest.fn(),
          },
        },
        {
          provide: EntityManager,
          useValue: {
            getConnection: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<HealthController>(HealthController);
    healthCheckService = module.get<HealthCheckService>(HealthCheckService);
  };

  beforeAll(async () => {
    await setupTest();
    process.env = {
      ...originalEnv,
    };
  });

  beforeEach(() => {
    jest.resetAllMocks();
  });

  const testTable = [
    {
      testName: 'should be defined',
      skipTest: false,
      setupMocks: () => {},
      runAssertions: async () => {
        expect(controller).toBeDefined();
        expect(healthCheckService).toBeDefined();
      },
    },
    {
      testName: 'should return 200 if all health indicators pass',
      skipTest: false,
      setupMocks: () => {
        jest.spyOn(healthCheckService, 'check').mockResolvedValue({ status: 'ok', details: {} });
      },
      runAssertions: async () => {
        const mockResponse = {
          status: jest.fn().mockReturnThis(),
          send: jest.fn(),
        };
        await controller.checkConnectionHealth(mockResponse);
        expect(mockResponse.status).toHaveBeenCalledWith(200);
      },
    },
    {
      testName: 'should return 500 if any health indicator fails',
      skipTest: false,
      setupMocks: () => {
        jest.spyOn(healthCheckService, 'check').mockResolvedValue({ status: 'error', details: {} });
      },
      runAssertions: async () => {
        const mockResponse = {
          status: jest.fn().mockReturnThis(),
          send: jest.fn(),
        };
        await controller.checkConnectionHealth(mockResponse);
        expect(mockResponse.status).toHaveBeenCalledWith(500);
      },
    },
    {
      testName: 'should return 500 if an error occurs during health check',
      skipTest: false,
      setupMocks: () => {
        jest.spyOn(healthCheckService, 'check').mockRejectedValue(new Error('Test error'));
      },
      runAssertions: async () => {
        const mockResponse = {
          status: jest.fn().mockReturnThis(),
          send: jest.fn(),
        };
        await controller.checkConnectionHealth(mockResponse);
        expect(mockResponse.status).toHaveBeenCalledWith(500);
      },
    },
  ];

  each(testTable).describe(HealthCheckService.name, ({ testName, skipTest, setupMocks, runAssertions }) => {
    canSkipTest(skipTest)(testName, async () => {
      setupMocks();
      await runAssertions();
    });
  });
});

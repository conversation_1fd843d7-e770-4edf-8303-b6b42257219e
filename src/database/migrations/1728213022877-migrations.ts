import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrations1728213022877 implements MigrationInterface {
    name = 'Migrations1728213022877'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."program_token_type" AS ENUM('ACCESS_TOKEN', 'APPLICATION_TOKEN')`);
        await queryRunner.query(`CREATE TABLE "program_tokens" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "created_by" character varying, "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_by" character varying, "deleted_at" TIMESTAMP, "deleted_by" character varying, "program_id" character varying NOT NULL, "access_token" character varying NOT NULL, "consented_at" TIMESTAMP NOT NULL, "account_id" character varying, "expires_at" TIMESTAMP NOT NULL, "refresh_token" character varying, "refresh_token_expires_at" TIMESTAMP, "token_type" "public"."program_token_type" NOT NULL, "scope" character varying, "id_token" character varying, CONSTRAINT "PK_86019800898d2d9e17213c497d2" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "program_tokens"`);
        await queryRunner.query(`DROP TYPE "public"."program_token_type"`);
    }

}

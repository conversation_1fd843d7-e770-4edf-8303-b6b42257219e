import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrations1728891543456 implements MigrationInterface {
    name = 'Migrations1728891543456'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_3c1cc03c7bd19f058138947b16" ON "accounts" ("client_id", "currency") WHERE client_id IS NOT NULL and deleted_at ISNULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_3c1cc03c7bd19f058138947b16"`);
    }

}

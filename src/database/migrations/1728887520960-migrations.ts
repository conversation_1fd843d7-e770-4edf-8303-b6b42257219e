import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrations1728887520960 implements MigrationInterface {
    name = 'Migrations1728887520960'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "accounts" ADD "external_id" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "accounts" ADD CONSTRAINT "UQ_ee32a4964f6c324a71536a89159" UNIQUE ("external_id")`);
        await queryRunner.query(`ALTER TABLE "accounts" ADD "customer_id" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "accounts" ADD "account_name" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "accounts" ADD "account_number" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "accounts" ADD "routing_code" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "accounts" ADD "routing_code_type" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "accounts" ADD "bank_name" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "accounts" ADD "bank_address" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "accounts" ADD "bank_country" character varying NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "accounts" DROP COLUMN "bank_country"`);
        await queryRunner.query(`ALTER TABLE "accounts" DROP COLUMN "bank_address"`);
        await queryRunner.query(`ALTER TABLE "accounts" DROP COLUMN "bank_name"`);
        await queryRunner.query(`ALTER TABLE "accounts" DROP COLUMN "routing_code_type"`);
        await queryRunner.query(`ALTER TABLE "accounts" DROP COLUMN "routing_code"`);
        await queryRunner.query(`ALTER TABLE "accounts" DROP COLUMN "account_number"`);
        await queryRunner.query(`ALTER TABLE "accounts" DROP COLUMN "account_name"`);
        await queryRunner.query(`ALTER TABLE "accounts" DROP COLUMN "customer_id"`);
        await queryRunner.query(`ALTER TABLE "accounts" DROP CONSTRAINT "UQ_ee32a4964f6c324a71536a89159"`);
        await queryRunner.query(`ALTER TABLE "accounts" DROP COLUMN "external_id"`);
    }

}

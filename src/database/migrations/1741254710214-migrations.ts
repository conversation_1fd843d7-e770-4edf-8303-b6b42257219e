import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1741254710214 implements MigrationInterface {
  name = 'Migrations1741254710214';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "program_tokens" ADD "client_id" character varying`);
    await queryRunner.query(
      `CREATE UNIQUE INDEX program_tokens_client_id_idx ON public.program_tokens (client_id);`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX public.program_tokens_client_id_idx;`);
    await queryRunner.query(`ALTER TABLE "program_tokens" DROP COLUMN "client_id"`);
  }
}

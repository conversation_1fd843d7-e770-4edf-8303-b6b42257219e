import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1738825564610 implements MigrationInterface {
  name = 'Migrations1738825564610';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE IF NOT EXISTS "programs_currency_mapping" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "created_by" character varying, "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_by" character varying, "deleted_at" TIMESTAMP, "deleted_by" character varying, "program_id" character varying NOT NULL, "currency" character varying NOT NULL, "provider" character varying NOT NULL, CONSTRAINT "UQ_5ca1903edcb333b3de4e2c50f27" UNIQUE ("program_id", "currency"), CONSTRAINT "UQ_5df236b3fa6a9d7a2b13f62d03a" UNIQUE ("provider", "currency"), CONSTRAINT "PK_ebce1970b93a1a327481edee8cc" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`ALTER TABLE IF EXISTS "program_tokens" DROP COLUMN IF EXISTS "program_id"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE IF EXISTS "program_tokens" ADD COLUMN IF NOT EXISTS "program_id" character varying NOT NULL`,
    );
    await queryRunner.query(`DROP TABLE IF EXISTS "programs_currency_mapping"`);
  }
}

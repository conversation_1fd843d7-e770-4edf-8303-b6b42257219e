import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1743920494800 implements MigrationInterface {
  name = 'Migrations1743920494800';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."program_tokens_client_id_idx"`);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "UQ_fc715c787854b89a813dfe42f05" ON "program_tokens" ("client_id") WHERE deleted_at isnull;`,
    );
    await queryRunner.query(
      `ALTER TABLE "programs_currency_mapping" DROP CONSTRAINT "UQ_5ca1903edcb333b3de4e2c50f27"`,
    );
    await queryRunner.query(
      `ALTER TABLE "programs_currency_mapping" ADD CONSTRAINT "UQ_24eb26fc4c6ddd58a66600adbf6" UNIQUE ("program_id", "currency", "provider")`,
    );

    await queryRunner.query(
      `INSERT INTO public.programs_currency_mapping (created_by, updated_by, program_id, currency, provider) VALUES ('MIGRATION','MIGRATION','100236700','USD','payoneer_auto_debit');`,
    );
    await queryRunner.query(
      `INSERT INTO public.programs_currency_mapping (created_by, updated_by, program_id, currency, provider) VALUES ('MIGRATION','MIGRATION','100241760','EUR','payoneer_auto_debit');`,
    );
    await queryRunner.query(
      `INSERT INTO public.programs_currency_mapping (created_by,updated_by, program_id, currency,provider) VALUES ('MIGRATION','MIGRATION','100236700','DEFAULT','payoneer_auto_debit');`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DELETE FROM public.programs_currency_mapping WHERE provider = 'payoneer_auto_debit'`,
    );
    await queryRunner.query(
      `ALTER TABLE "programs_currency_mapping" DROP CONSTRAINT "UQ_24eb26fc4c6ddd58a66600adbf6"`,
    );
    await queryRunner.query(
      `ALTER TABLE "programs_currency_mapping" ADD CONSTRAINT "UQ_5ca1903edcb333b3de4e2c50f27" UNIQUE ("program_id", "currency")`,
    );
    await queryRunner.query(`DROP INDEX public."UQ_fc715c787854b89a813dfe42f05";`);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "program_tokens_client_id_idx" ON "program_tokens" ("client_id") `,
    );
  }
}

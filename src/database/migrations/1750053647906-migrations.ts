import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1750053647906 implements MigrationInterface {
  name = 'Migrations1750053647906';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "program_tokens" ADD IF NOT EXISTS "application_id" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "program_tokens" DROP COLUMN "application_id"`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1739295963589 implements MigrationInterface {
  name = 'Migrations1739295963589';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "program_tokens" ADD CONSTRAINT "UQ_e2b26ec086a4ee1282df5f76f83" UNIQUE ("access_token")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "program_tokens" DROP CONSTRAINT "UQ_e2b26ec086a4ee1282df5f76f83"`);
  }
}

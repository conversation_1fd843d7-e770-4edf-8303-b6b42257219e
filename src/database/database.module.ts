import { DynamicModule, Module } from '@nestjs/common';
import { TypeOrmModule, TypeOrmModuleOptions } from '@nestjs/typeorm';
import databaseConfig from '../common/database/database-config';

@Module({})
export class DatabaseModule {
  static forPGRootAsync(url: any = null): DynamicModule {
    return {
      module: DatabaseModule,
      imports: [
        TypeOrmModule.forRootAsync({
          useFactory: async (): Promise<TypeOrmModuleOptions> => {
            return databaseConfig(url);
          },
          inject: [],
        }),
      ],
      providers: [],
    };
  }

  static forPGFeature(entities?: any[]): DynamicModule {
    return TypeOrmModule.forFeature(entities);
  }
}

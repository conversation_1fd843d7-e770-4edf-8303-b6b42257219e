import { Test, TestingModule } from '@nestjs/testing';
import { ProgramTokenService } from './program-token.service';
import { ProgramTokensRepository } from '../../common/repositories/program-token.repository';
import { PayoneerHttpClient } from '../../common/external-service/payoneer/services/payoneer.http.client.service';
import { AppConfigService } from '../../config/services/app.config.service';
import { ProgramTokenServiceHelper } from './helper/program-token.service.helper';
import { PayoneerAccountRepository } from '../../common/repositories/account.repository';
import { BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';
import { TokenType } from '../../common/constants/enums';
import { ProgramTokensEntity } from '../../common/entities/program-token.entity';
import {
  ClientErrorV2,
  GetFundingsAccountsResponse,
  ReceivingAccount,
} from '../../common/data-types/payoneer-v4.data.types';
import { BeneficiaryRegistrationPurpose } from '../../beneficiary/dtos/inputs/beneficiary.graphql.input';
import { PayoneerAccountEntity } from '../../common/entities/account.entitiy';
import * as countries from 'i18n-iso-countries';
import * as utils from '../../common/helpers/utils';
import { SaveOptions, RemoveOptions } from 'typeorm';

describe('ProgramTokenService', () => {
  let service: ProgramTokenService;
  let programTokenRepo: jest.Mocked<ProgramTokensRepository>;
  let accountRepo: jest.Mocked<PayoneerAccountRepository>;
  let payoneerClient: jest.Mocked<PayoneerHttpClient>;
  let appConfigService: jest.Mocked<AppConfigService>;
  let helper: jest.Mocked<ProgramTokenServiceHelper>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProgramTokenService,
        {
          provide: ProgramTokensRepository,
          useValue: {
            findOne: jest.fn(),
            getTokens: jest.fn(),
            update: jest.fn(),
            updateToDatabase: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
          },
        },
        {
          provide: PayoneerAccountRepository,
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: PayoneerHttpClient,
          useValue: {
            refreshAccessToken: jest.fn(),
            generateAccessToken: jest.fn(),
            revokeClientAccessToken: jest.fn(),
            getFundingAccounts: jest.fn(),
            getClientAccountCodeUrl: jest.fn(),
          },
        },
        {
          provide: AppConfigService,
          useValue: {
            getSkuadSGId: jest.fn(),
            getProviderProgramId: jest.fn(),
          },
        },
        {
          provide: ProgramTokenServiceHelper,
          useValue: {
            getTokenExpirationDateTime: jest.fn(),
            getAccountIdFromIdToken: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ProgramTokenService>(ProgramTokenService);
    programTokenRepo = module.get(ProgramTokensRepository);
    accountRepo = module.get(PayoneerAccountRepository);
    payoneerClient = module.get(PayoneerHttpClient);
    appConfigService = module.get(AppConfigService);
    helper = module.get(ProgramTokenServiceHelper);
  });

  describe('getAccount', () => {
    it('should return token if found', async () => {
      const mockToken = new ProgramTokensEntity();
      programTokenRepo.findOne.mockResolvedValue(mockToken);

      const result = await service.getAccount('test-client');
      expect(result).toBe(mockToken);
    });

    it('should throw BadRequestException if no token found', async () => {
      programTokenRepo.findOne.mockResolvedValue(null);

      await expect(service.getAccount('test-client')).rejects.toThrow(BadRequestException);
    });
  });

  describe('refreshTokenToBeExpired', () => {
    const mockToken = {
      id: '1',
      clientId: 'client',
      accountId: 'acc',
      refreshToken: 'r',
      tokenType: TokenType.AccessToken,
      accessToken: 'a',
      consentedAt: new Date(),
      expiresAt: new Date(),
      refreshTokenExpiresAt: new Date(),
      scope: 'scope',
      idToken: 'id',
      createdBy: 'user',
      updatedBy: 'user',
      deletedAt: null,
      deletedBy: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      applicationId: 'app',
      hasId: () => true,
      save: jest.fn(),
      remove: jest.fn(),
      updateToDatabase: jest.fn(),
      softRemove: jest.fn(),
      recover: jest.fn(),
      reload: jest.fn(),
    };
    it('should log and exit if no tokens to refresh', async () => {
      programTokenRepo.getTokens.mockResolvedValue([]);
      const loggerSpy = jest.spyOn(service['logger'], 'log');

      await service.refreshTokenToBeExpired();

      expect(loggerSpy).toHaveBeenCalledWith('No tokens to refresh');
    });

    it('should refresh tokens and update repository', async () => {
      programTokenRepo.getTokens.mockResolvedValue([mockToken]);
      appConfigService.getSkuadSGId.mockReturnValue('other-client');
      payoneerClient.refreshAccessToken.mockResolvedValue([
        {
          access_token: 'a',
          consented_on: 1,
          refresh_token: 'r',
          scope: 'scope',
          token_type: 'Bearer',
          expires_in: 3600,
          refresh_token_expires_in: 3600,
          id_token: 'id',
          error: null,
          error_description: null,
        },
        null,
      ]);
      helper.getTokenExpirationDateTime.mockReturnValue(new Date());

      await service.refreshTokenToBeExpired();

      expect(programTokenRepo.update).toHaveBeenCalledWith(mockToken.id, expect.any(Object));
    });

    it('should set purpose to SSO when clientId matches SkuadSGId', async () => {
      const skuadSGId = 'skuad-sg-id';
      appConfigService.getSkuadSGId.mockReturnValue(skuadSGId);
      const skuadToken = {
        ...mockToken,
        clientId: skuadSGId,
      };
      programTokenRepo.getTokens.mockResolvedValue([skuadToken]);
      payoneerClient.refreshAccessToken.mockResolvedValue([
        {
          access_token: 'new-token',
          consented_on: 1,
          refresh_token: 'new-refresh',
          scope: 'scope',
          token_type: 'Bearer',
          expires_in: 3600,
          refresh_token_expires_in: 3600,
          id_token: 'id',
          error: '',
          error_description: '',
        },
        null,
      ]);
      helper.getTokenExpirationDateTime.mockReturnValue(new Date());

      await service.refreshTokenToBeExpired();

      expect(payoneerClient.refreshAccessToken).toHaveBeenCalledWith(
        skuadToken.refreshToken,
        skuadToken.clientId,
        BeneficiaryRegistrationPurpose.SSO,
      );
    });

    it('should log error and continue when refresh fails', async () => {
      programTokenRepo.getTokens.mockResolvedValue([mockToken]);
      const refreshError = { error: 'Refresh failed', errors: [] };
      payoneerClient.refreshAccessToken.mockResolvedValue([null, refreshError]);
      const loggerSpy = jest.spyOn(service['logger'], 'error');

      await service.refreshTokenToBeExpired();

      expect(loggerSpy).toHaveBeenCalledWith(expect.stringContaining('Got error while refreshing tokens'));
      expect(programTokenRepo.update).not.toHaveBeenCalled();
    });

    it('should log when starting to refresh a token', async () => {
      programTokenRepo.getTokens.mockResolvedValue([mockToken]);
      payoneerClient.refreshAccessToken.mockResolvedValue([
        {
          access_token: 'new-token',
          consented_on: 1,
          refresh_token: 'new-refresh',
          scope: 'scope',
          token_type: 'Bearer',
          expires_in: 3600,
          refresh_token_expires_in: 3600,
          id_token: 'id',
          error: '',
          error_description: '',
        },
        null,
      ]);
      helper.getTokenExpirationDateTime.mockReturnValue(new Date());
      const loggerSpy = jest.spyOn(service['logger'], 'error');

      await service.refreshTokenToBeExpired();

      expect(loggerSpy).toHaveBeenCalledWith({
        message: 'refrshing token for',
        data: { id: mockToken.id, accountId: mockToken.accountId },
      });
    });
  });

  describe('saveAccessToken', () => {
    it('should save a new access token', async () => {
      payoneerClient.generateAccessToken.mockResolvedValue([
        {
          access_token: 'token',
          consented_on: 1,
          refresh_token: 'r',
          scope: 'scope',
          token_type: 'Bearer',
          expires_in: 3600,
          refresh_token_expires_in: 3600,
          id_token: 'id',
          error: null,
          error_description: null,
        },
        null,
      ]);
      helper.getAccountIdFromIdToken.mockReturnValue('acc');
      helper.getTokenExpirationDateTime.mockReturnValue(new Date());
      programTokenRepo.findOne.mockResolvedValue(null);

      const result = await service.saveAccessToken('c', 'code', 'url', 'user');
      expect(programTokenRepo.save).toHaveBeenCalled();
      expect(result.accountId).toBe('acc');
    });

    it('should update an existing access token', async () => {
      payoneerClient.generateAccessToken.mockResolvedValue([
        {
          access_token: 'token',
          consented_on: 1,
          refresh_token: 'r',
          scope: 'scope',
          token_type: 'Bearer',
          expires_in: 3600,
          refresh_token_expires_in: 3600,
          id_token: 'id',
          error: null,
          error_description: null,
        },
        null,
      ]);
      helper.getAccountIdFromIdToken.mockReturnValue('acc');
      helper.getTokenExpirationDateTime.mockReturnValue(new Date());
      programTokenRepo.findOne.mockResolvedValue(new ProgramTokensEntity());

      await service.saveAccessToken('c', 'code', 'url', 'user');
      expect(programTokenRepo.updateToDatabase).toHaveBeenCalled();
    });

    it('should throw RpcException on error', async () => {
      payoneerClient.generateAccessToken.mockResolvedValue([null, { error: 'Test error' } as ClientErrorV2]);

      await expect(service.saveAccessToken('c', 'code', 'url', 'user')).rejects.toThrow(RpcException);
    });
  });

  describe('revokeClientAccessToken', () => {
    const clientId = 'test-client';
    const userId = 'user-1';
    const mockToken: ProgramTokensEntity = {
      id: 'token-1',
      clientId,
      tokenType: TokenType.AccessToken,
      accessToken: 'test-token',
      consentedAt: undefined,
      accountId: '',
      expiresAt: undefined,
      refreshToken: '',
      refreshTokenExpiresAt: undefined,
      scope: '',
      idToken: '',
      applicationId: '',
      createdAt: undefined,
      createdBy: '',
      updatedAt: undefined,
      updatedBy: '',
      deletedAt: undefined,
      deletedBy: '',
      hasId: function (): boolean {
        throw new Error('Function not implemented.');
      },
      save: function (options?: SaveOptions): Promise<ProgramTokensEntity> {
        throw new Error('Function not implemented.');
      },
      remove: function (options?: RemoveOptions): Promise<ProgramTokensEntity> {
        throw new Error('Function not implemented.');
      },
      softRemove: function (options?: SaveOptions): Promise<ProgramTokensEntity> {
        throw new Error('Function not implemented.');
      },
      recover: function (options?: SaveOptions): Promise<ProgramTokensEntity> {
        throw new Error('Function not implemented.');
      },
      reload: function (): Promise<void> {
        throw new Error('Function not implemented.');
      },
    };

    let loggerErrorSpy: jest.SpyInstance;

    beforeEach(() => {
      jest.clearAllMocks();
      loggerErrorSpy = jest.spyOn(service['logger'], 'error');
    });

    afterEach(() => {
      loggerErrorSpy.mockRestore();
    });

    it('should throw if no token found', async () => {
      programTokenRepo.findOne.mockResolvedValue(null);

      await expect(service.revokeClientAccessToken('c', 'u')).rejects.toThrow(RpcException);
    });

    it('should throw RpcException on revoke failure', async () => {
      const mockToken = { id: '1', clientId: 'c', accessToken: 'a', tokenType: TokenType.AccessToken };
      programTokenRepo.findOne.mockResolvedValue(mockToken as any);
      payoneerClient.revokeClientAccessToken.mockResolvedValue([
        null,
        { error: 'Test error' } as ClientErrorV2,
      ]);

      await expect(service.revokeClientAccessToken('c', 'u')).rejects.toThrow(RpcException);
    });

    it('should handle general errors and throw RpcException', async () => {
      programTokenRepo.findOne.mockResolvedValue(mockToken);
      const mockError = new Error('Test error');
      payoneerClient.revokeClientAccessToken.mockRejectedValue(mockError);
      const loggerSpy = jest.spyOn(service['logger'], 'error');

      await expect(service.revokeClientAccessToken(clientId, userId)).rejects.toThrow(RpcException);

      expect(loggerSpy).toHaveBeenCalledWith({
        message: 'Got error while revoking token',
        error: mockError,
        data: { clientId, userId },
      });
    });

    it('should handle error with undefined message', async () => {
      programTokenRepo.findOne.mockResolvedValue(mockToken);
      const mockError = {}; // Error with no message
      payoneerClient.revokeClientAccessToken.mockRejectedValue(mockError);

      await expect(service.revokeClientAccessToken(clientId, userId)).rejects.toThrow(RpcException);
    });

    it('should throw RpcException when no token found', async () => {
      programTokenRepo.findOne.mockResolvedValue(null);

      await expect(service.revokeClientAccessToken(clientId, userId)).rejects.toThrow(RpcException);
      expect(loggerErrorSpy).toHaveBeenCalled();
    });
  });

  describe('checkConsentStatus', () => {
    it('should return true when token exists for the client', async () => {
      // Arrange
      const clientId = 'test-client';
      const mockToken = new ProgramTokensEntity();
      programTokenRepo.findOne.mockResolvedValue(mockToken);

      // Act
      const result = await service.checkConsentStatus(clientId);

      // Assert
      expect(result).toBe(true);
      expect(programTokenRepo.findOne).toHaveBeenCalledWith({
        where: { clientId, deletedAt: null },
      });
    });

    it('should return false when no token exists for the client', async () => {
      // Arrange
      const clientId = 'test-client';
      programTokenRepo.findOne.mockResolvedValue(null);

      // Act
      const result = await service.checkConsentStatus(clientId);

      // Assert
      expect(result).toBe(false);
      expect(programTokenRepo.findOne).toHaveBeenCalledWith({
        where: { clientId, deletedAt: null },
      });
    });

    it('should handle empty string clientId', async () => {
      // Arrange
      const clientId = '';
      programTokenRepo.findOne.mockResolvedValue(null);

      // Act
      const result = await service.checkConsentStatus(clientId);

      // Assert
      expect(result).toBe(false);
      expect(programTokenRepo.findOne).toHaveBeenCalledWith({
        where: { clientId, deletedAt: null },
      });
    });

    it('should propagate repository errors', async () => {
      // Arrange
      const clientId = 'test-client';
      const dbError = new Error('Database connection error');
      programTokenRepo.findOne.mockRejectedValue(dbError);

      // Act & Assert
      await expect(service.checkConsentStatus(clientId)).rejects.toThrow(dbError);
    });

    it('should handle undefined clientId gracefully', async () => {
      // Arrange
      const clientId = undefined;
      programTokenRepo.findOne.mockResolvedValue(null);

      // Act
      const result = await service.checkConsentStatus(clientId);

      // Assert
      expect(result).toBe(false);
    });

    it('should handle null clientId gracefully', async () => {
      // Arrange
      const clientId = null;
      programTokenRepo.findOne.mockResolvedValue(null);

      // Act
      const result = await service.checkConsentStatus(clientId);

      // Assert
      expect(result).toBe(false);
    });

    it('should return true for soft-deleted tokens if they exist', async () => {
      // Arrange
      const clientId = 'test-client';
      const mockToken = new ProgramTokensEntity();
      mockToken.deletedAt = new Date(); // This token is soft-deleted

      // First call returns null because we're looking for non-deleted tokens
      // Second call returns the token when we check for any token including deleted ones
      programTokenRepo.findOne
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce(mockToken);

      // Act
      const result = await service.checkConsentStatus(clientId);

      // Assert
      expect(result).toBe(false); // Should be false because we only count non-deleted tokens
      expect(programTokenRepo.findOne).toHaveBeenCalledWith({
        where: { clientId, deletedAt: null },
      });
    });
  });

  describe('createAuthorizationLink', () => {
    it('should return link from client', () => {
      payoneerClient.getClientAccountCodeUrl.mockReturnValue('link');

      const result = service.createAuthorizationLink('url', BeneficiaryRegistrationPurpose.PAYIN, 'state');
      expect(result).toBe('link');
    });
  });

  it('should map fields correctly to PayoneerAccountEntity', () => {
    appConfigService.getProviderProgramId.mockReturnValue('12345');

    const mockAccount = {
      id: 'acc-123',
      currency: 'USD',
      details: {
        items: [
          { name: 'BeneficiaryName', value: 'John Doe' },
          { name: 'BankName', value: 'Chase' },
          { name: 'IBAN', value: '**********************' },
          { name: 'BankAddress', value: '123 Bank St' },
          { name: 'BankCountry', value: 'Germany' },
          { name: 'BIC', value: 'COBADEFF' },
        ],
      },
    };

    jest.spyOn(countries, 'getAlpha2Code').mockReturnValue('DE');
    jest.spyOn(global, 'Date').mockImplementation(() => new Date('2023-01-01T00:00:00Z') as any);

    const entity = service.getAccountEntityObjectFromPayoneerAccountResponse(
      mockAccount as any,
      'provider-acc-id',
      'TEST_ACTOR',
    );

    expect(entity).toBeInstanceOf(PayoneerAccountEntity);
    expect(entity.providerAccountId).toBe('provider-acc-id');
    expect(entity.externalId).toBe('acc-123');
    expect(entity.currency).toBe('USD');
    expect(entity.createdBy).toBe('TEST_ACTOR');
    expect(entity.updatedBy).toBe('TEST_ACTOR');
    expect(entity.programId).toBe('12345');
    expect(entity.accountName).toBe('John Doe');
    expect(entity.bankName).toBe('Chase');
    expect(entity.accountNumber).toBe('**********************');
    expect(entity.bankAddress).toBe('123 Bank St');
    expect(entity.bankCountry).toBe('DE');
    expect(entity.routingCode).toBe('COBADEFF');
    expect(entity.routingCodeType).toBeDefined(); // Tum getRoutingCodeType ki value verify karna chaho toh kar sakte ho
  });

  it('should default programId if getProviderProgramId returns undefined', () => {
    appConfigService.getProviderProgramId.mockReturnValue(undefined);

    const mockAccount = {
      id: 'acc-123',
      currency: 'USD',
      details: { items: [] },
    };

    const entity = service.getAccountEntityObjectFromPayoneerAccountResponse(
      mockAccount as any,
      'provider-acc-id',
    );

    expect(entity.programId).toBe('*********');
  });

  describe('populateFundingAccounts', () => {
    const RealDate = Date;

    beforeEach(() => {
      jest.spyOn(global, 'Date').mockImplementation(() => new RealDate('2023-01-01T00:00:00Z'));
      (global.Date as any).now = RealDate.now;
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });
    it('should fetch tokens and populate funding accounts', async () => {
      const mockTokens = [
        {
          accountId: 'acc1',
          clientId: 'client1',
          tokenType: TokenType.AccessToken,
          accessToken: 'a',
          refreshToken: 'r',
          consentedAt: new Date(),
          expiresAt: new Date(),
          refreshTokenExpiresAt: new Date(),
          scope: 'scope',
          idToken: 'id',
          createdBy: 'user',
          updatedBy: 'user',
          deletedAt: null,
          deletedBy: null,
          createdAt: new Date(),
          updatedAt: new Date(),
          applicationId: 'app',
          id: '1',
          hasId: () => true,
          save: jest.fn(),
          remove: jest.fn(),
          updateToDatabase: jest.fn(),
          update: jest.fn(),
          softRemove: jest.fn(),
          recover: jest.fn(),
          reload: jest.fn(),
        },
        {
          accountId: 'acc2',
          clientId: 'client2',
          tokenType: TokenType.AccessToken,
          accessToken: 'a',
          refreshToken: 'r',
          consentedAt: new Date(),
          expiresAt: new Date(),
          refreshTokenExpiresAt: new Date(),
          scope: 'scope',
          idToken: 'id',
          createdBy: 'user',
          updatedBy: 'user',
          deletedAt: null,
          deletedBy: null,
          createdAt: new Date(),
          updatedAt: new Date(),
          applicationId: 'app',
          id: '1',
          hasId: () => true,
          save: jest.fn(),
          remove: jest.fn(),
          updateToDatabase: jest.fn(),
          update: jest.fn(),
          softRemove: jest.fn(),
          recover: jest.fn(),
          reload: jest.fn(),
        },
      ];
      appConfigService.getSkuadSGId.mockReturnValue('skuad-sg-id');
      programTokenRepo.find.mockResolvedValue(mockTokens);

      payoneerClient.getFundingAccounts
        .mockResolvedValueOnce([{ result: { receiving_accounts: { items: [], total: 0 } } }, null])
        .mockResolvedValueOnce([{ result: { receiving_accounts: { items: [], total: 0 } } }, null]);

      service.transformAndInsertFundingAccounts = jest.fn().mockResolvedValue(undefined);

      await service.populateFundingAccounts();

      expect(programTokenRepo.find).toHaveBeenCalledWith({
        select: ['accountId', 'clientId'],
        where: {
          tokenType: TokenType.AccessToken,
          clientId: 'skuad-sg-id',
        },
      });

      expect(payoneerClient.getFundingAccounts).toHaveBeenCalledTimes(2);
      expect(service.transformAndInsertFundingAccounts).toHaveBeenCalledTimes(2);
    });

    it('should skip processing if getFundingAccounts returns error or no data', async () => {
      const mockTokens = [
        {
          accountId: 'acc1',
          clientId: 'client1',
          tokenType: TokenType.AccessToken,
          accessToken: 'a',
          refreshToken: 'r',
          consentedAt: new Date(),
          expiresAt: new Date(),
          refreshTokenExpiresAt: new Date(),
          scope: 'scope',
          idToken: 'id',
          createdBy: 'user',
          updatedBy: 'user',
          deletedAt: null,
          deletedBy: null,
          createdAt: new Date(),
          updatedAt: new Date(),
          applicationId: 'app',
          id: '1',
          hasId: () => true,
          save: jest.fn(),
          remove: jest.fn(),
          updateToDatabase: jest.fn(),
          update: jest.fn(),
          softRemove: jest.fn(),
          recover: jest.fn(),
          reload: jest.fn(),
        },
      ];
      appConfigService.getSkuadSGId.mockReturnValue('skuad-sg-id');
      programTokenRepo.find.mockResolvedValue(mockTokens);

      payoneerClient.getFundingAccounts.mockResolvedValue([null, { error: 'Some error' } as ClientErrorV2]);

      service.transformAndInsertFundingAccounts = jest.fn();

      await service.populateFundingAccounts();

      expect(service.transformAndInsertFundingAccounts).not.toHaveBeenCalled();
    });

    it('should log and continue on exception inside loop', async () => {
      const mockTokens = [
        {
          accountId: 'acc1',
          clientId: 'client1',
          tokenType: TokenType.AccessToken,
          accessToken: 'a',
          refreshToken: 'r',
          consentedAt: new Date(),
          expiresAt: new Date(),
          refreshTokenExpiresAt: new Date(),
          scope: 'scope',
          idToken: 'id',
          createdBy: 'user',
          updatedBy: 'user',
          deletedAt: null,
          deletedBy: null,
          createdAt: new Date(),
          updatedAt: new Date(),
          applicationId: 'app',
          id: '1',
          hasId: () => true,
          save: jest.fn(),
          remove: jest.fn(),
          updateToDatabase: jest.fn(),
          update: jest.fn(),
          softRemove: jest.fn(),
          recover: jest.fn(),
          reload: jest.fn(),
        },
      ];
      appConfigService.getSkuadSGId.mockReturnValue('skuad-sg-id');
      programTokenRepo.find.mockResolvedValue(mockTokens);

      payoneerClient.getFundingAccounts.mockImplementation(() => {
        throw new Error('Test exception');
      });

      const loggerSpy = jest.spyOn(service['logger'], 'error');

      await service.populateFundingAccounts();

      expect(loggerSpy).toHaveBeenCalledWith(expect.stringContaining('Got error while populating accounts'));
    });
  });

  describe('transformAndInsertFundingAccounts', () => {
    const mockAccountResponse: GetFundingsAccountsResponse = {
      result: {
        receiving_accounts: {
          items: [
            {
              id: 'acc1',
              currency: 'USD',
              details: {
                items: [
                  { name: 'BeneficiaryName', value: 'Test Account 2' },
                  { name: 'BankName', value: 'Test Bank 2' },
                  { name: 'IBAN', value: '**********************' },
                ],
                total: 0,
              },
              creation_date: '',
              nickname: '',
              status: '',
              status_description: '',
            },
            {
              id: 'acc2',
              currency: 'EUR',
              details: {
                items: [
                  { name: 'BeneficiaryName', value: 'Test Account 2' },
                  { name: 'BankName', value: 'Test Bank 2' },
                  { name: 'IBAN', value: '**********************' },
                ],
                total: 0,
              },
              creation_date: '',
              nickname: '',
              status: '',
              status_description: '',
            },
          ],
          total: 2,
        },
      },
    };

    const emptyResponse: GetFundingsAccountsResponse = {
      result: {
        receiving_accounts: {
          items: [],
          total: 0,
        },
      },
    };

    beforeEach(() => {
      jest
        .spyOn(service as any, 'getAccountEntityObjectFromPayoneerAccountResponse')
        .mockImplementation((account: ReceivingAccount) => {
          const entity = new PayoneerAccountEntity();
          entity.externalId = account.id;
          entity.currency = account.currency;
          return entity;
        });
    });

    it('should process and save new accounts', async () => {
      accountRepo.findOne.mockResolvedValueOnce(null).mockResolvedValueOnce(null);

      await service.transformAndInsertFundingAccounts(mockAccountResponse, 'provider-acc-id');

      expect(accountRepo.findOne).toHaveBeenCalledTimes(2);
      expect(accountRepo.save).toHaveBeenCalledTimes(2);
    });

    it('should skip existing accounts', async () => {
      const existingAccount = new PayoneerAccountEntity();
      existingAccount.externalId = 'acc1';

      accountRepo.findOne
        .mockResolvedValueOnce(existingAccount) // First account exists
        .mockResolvedValueOnce(null); // Second account doesn't exist

      await service.transformAndInsertFundingAccounts(mockAccountResponse, 'provider-acc-id');

      expect(accountRepo.findOne).toHaveBeenCalledTimes(2);
      expect(accountRepo.save).toHaveBeenCalledTimes(1); // Only second account saved
    });

    it('should handle errors for individual accounts and continue', async () => {
      accountRepo.findOne.mockRejectedValueOnce(new Error('DB Error')); // First account fails
      accountRepo.findOne.mockResolvedValueOnce(null); // Second account succeeds

      const loggerSpy = jest.spyOn(service['logger'], 'error');

      await service.transformAndInsertFundingAccounts(mockAccountResponse, 'provider-acc-id');

      expect(loggerSpy).toHaveBeenCalledWith({
        message: 'Got error while populating accounts',
        error: expect.any(Error),
        data: {
          accountId: 'provider-acc-id',
          account: mockAccountResponse.result.receiving_accounts.items[0],
        },
      });
      expect(accountRepo.save).toHaveBeenCalledTimes(1); // Second account still saved
    });

    it('should handle empty account list', async () => {
      await service.transformAndInsertFundingAccounts(emptyResponse, 'provider-acc-id');

      expect(accountRepo.findOne).not.toHaveBeenCalled();
      expect(accountRepo.save).not.toHaveBeenCalled();
    });

    it('should call getAccountEntityObjectFromPayoneerAccountResponse with correct parameters', async () => {
      accountRepo.findOne.mockResolvedValue(null);

      const getAccountEntitySpy = jest.spyOn(
        service as any,
        'getAccountEntityObjectFromPayoneerAccountResponse',
      );

      await service.transformAndInsertFundingAccounts(mockAccountResponse, 'provider-acc-id');

      expect(getAccountEntitySpy).toHaveBeenCalledTimes(2);
      expect(getAccountEntitySpy).toHaveBeenCalledWith(
        mockAccountResponse.result.receiving_accounts.items[0],
        'provider-acc-id',
      );
      expect(getAccountEntitySpy).toHaveBeenCalledWith(
        mockAccountResponse.result.receiving_accounts.items[1],
        'provider-acc-id',
      );
    });
  });

  // At the top of your test file (with other imports)
  describe('getAccountEntityObjectFromPayoneerAccountResponse', () => {
    beforeEach(() => {
      jest.clearAllMocks();
      // Mock the helper functions
      jest.spyOn(utils, 'extractCountryFromAddress').mockImplementation();
      jest.spyOn(utils, 'getRoutingCodeType').mockImplementation();
    });

    it('should fall back to extractCountryFromAddress when bankCountry is not provided', () => {
      const mockAccount = {
        id: 'acc1',
        currency: 'USD',
        details: {
          items: [
            { name: 'BeneficiaryName', value: 'Test Account' },
            { name: 'BankAddress', value: '123 Main St, London, UK' },
          ],
        },
      };

      // Mock the helper function
      (utils.extractCountryFromAddress as jest.Mock).mockReturnValue('GB');

      const entity = service.getAccountEntityObjectFromPayoneerAccountResponse(
        mockAccount as any,
        'provider-acc-id',
      );

      expect(entity.bankCountry).toBe('GB');
      expect(utils.extractCountryFromAddress).toHaveBeenCalledWith('123 Main St, London, UK');
    });

    it('should default to US when no country can be determined', () => {
      const mockAccount = {
        id: 'acc1',
        currency: 'USD',
        details: {
          items: [{ name: 'BeneficiaryName', value: 'Test Account' }],
        },
      };

      // Mock the helper to return undefined
      (utils.extractCountryFromAddress as jest.Mock).mockReturnValue(undefined);

      const entity = service.getAccountEntityObjectFromPayoneerAccountResponse(
        mockAccount as any,
        'provider-acc-id',
      );

      expect(entity.bankCountry).toBe('US');
      expect(utils.extractCountryFromAddress).not.toHaveBeenCalled();
    });

    it('should process AccountNumber field', () => {
      const mockAccount = {
        id: 'acc1',
        currency: 'USD',
        details: {
          items: [{ name: 'AccountNumber', value: '*********' }],
        },
      };

      const entity = service.getAccountEntityObjectFromPayoneerAccountResponse(
        mockAccount as any,
        'provider-acc-id',
      );

      expect(entity.accountNumber).toBe('*********');
    });

    it('should process RoutingABA field', () => {
      const mockAccount = {
        id: 'acc1',
        currency: 'USD',
        details: {
          items: [{ name: 'RoutingABA', value: '*********' }],
        },
      };

      (utils.getRoutingCodeType as jest.Mock).mockReturnValue('ABA');

      const entity = service.getAccountEntityObjectFromPayoneerAccountResponse(
        mockAccount as any,
        'provider-acc-id',
      );

      expect(entity.routingCode).toBe('*********');
      expect(entity.routingCodeType).toBe('ABA');
    });

    it('should handle empty BankAddress', () => {
      const mockAccount = {
        id: 'acc1',
        currency: 'USD',
        details: {
          items: [{ name: 'BankAddress', value: null }],
        },
      };

      const entity = service.getAccountEntityObjectFromPayoneerAccountResponse(
        mockAccount as any,
        'provider-acc-id',
      );

      expect(entity.bankAddress).toBe('');
    });

    it('should handle empty details.items array', () => {
      const mockAccount = {
        id: 'acc1',
        currency: 'USD',
        details: {
          items: [],
        },
      };

      const entity = service.getAccountEntityObjectFromPayoneerAccountResponse(
        mockAccount as any,
        'provider-acc-id',
      );

      // Verify default values
      expect(entity.bankAddress).toBe('');
      expect(entity.bankCountry).toBe('US');
    });
  });

  describe('getPayoneerCustomerId', () => {
    it('should return accountNumber as customerId for USD currency', () => {
      const accountEntity = new PayoneerAccountEntity();
      accountEntity.currency = 'USD';
      accountEntity.accountNumber = '*********';

      const result = service.getPayoneerCustomerId(accountEntity);
      expect(result).toBe('*********');
    });

    it('should return accountNumber as customerId for non-USD currency', () => {
      const accountEntity = new PayoneerAccountEntity();
      accountEntity.currency = 'EUR';
      accountEntity.accountNumber = '**********************';

      const result = service.getPayoneerCustomerId(accountEntity);
      expect(result).toBe('**********************');
    });
  });
});

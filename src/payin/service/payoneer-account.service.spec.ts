jest.mock('../../common/database/database-config', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    // Return your actual database config structure here
    type: 'postgres',
    host: 'localhost',
    port: 5432,
    username: 'test',
    password: 'test',
    database: 'test',
    entities: [],
    synchronize: false,
    logging: false,
    namingStrategy: new (require('typeorm-naming-strategies').SnakeNamingStrategy)(),
  })),
}));

jest.mock('typeorm', () => {
  const actualTypeorm = jest.requireActual('typeorm');
  return {
    ...actualTypeorm,
    DataSource: jest.fn().mockImplementation(() => ({
      initialize: jest.fn().mockResolvedValue(null),
      getRepository: jest.fn(),
    })),
  };
});

import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';
import { PayoneerAccountService } from './payoneer-account.service';
import { ProgramTokensRepository } from '../../common/repositories/program-token.repository';
import { PayoneerHttpClient } from '../../common/external-service/payoneer/services/payoneer.http.client.service';
import { ProgramTokenService } from './program-token.service';
import {
  PaymentCategory,
  PaymentCategoryMap,
  PaymentStatus,
  PaymentStatusMap,
  TokenType,
} from '../../common/constants/enums';
import { payoneer } from '@skuad/proto-utils/dist/interface/proto/payoneer/payoneer';
import { ChargeAccountByClientDebitRequest } from 'src/common/data-types/payoneer-v4.data.types';
import * as utils from '../../common/helpers/utils';
import { ProgramTokensEntity } from 'src/common/entities/program-token.entity';

// Mock the utils module
jest.mock('../../common/helpers/utils');

describe('PayoneerAccountService', () => {
  let service: PayoneerAccountService;
  let programTokenRepository: jest.Mocked<ProgramTokensRepository>;
  let payoneerClient: jest.Mocked<PayoneerHttpClient>;
  let programTokenService: jest.Mocked<ProgramTokenService>;

  const mockClientId = 'test-client-id';
  const mockAccountId = 'test-account-id';
  const mockAccessToken = 'test-access-token';
  const mockProgramId = 'test-program-id';
  const mockPartnerClientId = 'test-partner-client-id';
  const mockPartnerProgramId = 'test-partner-program-id';
  const mockPartnerPayerId = 'test-partner-payer-id';
  const mockPartnerPayeeId = 'test-partner-payee-id';
  const mockPartnerMerchantAccountId = 'test-partner-merchant-account-id';
  const mockPartnerMerchantPayerId = 'test-partner-merchant-payer-id';
  const mockPartnerMerchantPayeeId = 'test-partner-merchant-payee-id';
  const mockPartnerMerchantProgramId = 'test-partner-merchant-program-id';
  const mockRedirectUri = 'test-redirect-uri';
  const mockState = 'test-state';
  const mockCodeVerifier = 'test-code-verifier';
  const mockCodeChallenge = 'test-code-challenge';
  const mockCodeChallengeMethod = 'test-code-challenge-method';
  const mockAuthorizationCode = 'test-authorization-code';
  const mockCreatedAt = new Date();
  const mockUpdatedAt = new Date();
  const mockCreatedBy = 'test-created-by';
  const mockUpdatedBy = 'test-updated-by';
  const mockDeletedAt = new Date();
  const mockDeletedBy = 'test-deleted-by';

  const mockAccount = {
    accountId: mockAccountId,
    accessToken: mockAccessToken,
    clientId: mockClientId,
    programId: mockProgramId,
    partnerClientId: mockPartnerClientId,
    partnerProgramId: mockPartnerProgramId,
    partnerPayerId: mockPartnerPayerId,
    partnerPayeeId: mockPartnerPayeeId,
    partnerMerchantAccountId: mockPartnerMerchantAccountId,
    partnerMerchantPayerId: mockPartnerMerchantPayerId,
    partnerMerchantPayeeId: mockPartnerMerchantPayeeId,
    partnerMerchantProgramId: mockPartnerMerchantProgramId,
    redirectUri: mockRedirectUri,
    state: mockState,
    codeVerifier: mockCodeVerifier,
    codeChallenge: mockCodeChallenge,
    codeChallengeMethod: mockCodeChallengeMethod,
    authorizationCode: mockAuthorizationCode,
    createdAt: mockCreatedAt,
    updatedAt: mockUpdatedAt,
    createdBy: mockCreatedBy,
    updatedBy: mockUpdatedBy,
    deletedAt: mockDeletedAt,
    deletedBy: mockDeletedBy,
    consentedAt: new Date(),
    expiresAt: new Date(),
    refreshToken: 'refresh-token',
    refreshTokenExpiresAt: new Date(),
    tokenType: TokenType.AccessToken,
    scope: 'scope',
    idToken: 'id-token',
    applicationId: 'application-id',
    id: 'id',
    hasId: () => true,
    save: jest.fn(),
    remove: jest.fn(),
    softRemove: jest.fn(),
    recover: jest.fn(),
    reload: jest.fn(),
  };

  beforeEach(async () => {
    const mockProgramTokenRepository = {
      findOne: jest.fn(),
    };

    const mockPayoneerClient = {
      getTransactions: jest.fn(),
      getAccountBalances: jest.fn(),
      chargeAccountByClientPartnerDebit: jest.fn(),
      chargeAccountByClientDebit: jest.fn(),
      getPaymentStatus: jest.fn(),
      cancelCharge: jest.fn(),
      getMerchantChargeStatus: jest.fn(),
    };

    const mockProgramTokenService = {
      getAccount: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PayoneerAccountService,
        {
          provide: ProgramTokensRepository,
          useValue: mockProgramTokenRepository,
        },
        {
          provide: PayoneerHttpClient,
          useValue: mockPayoneerClient,
        },
        {
          provide: ProgramTokenService,
          useValue: mockProgramTokenService,
        },
      ],
    }).compile();

    service = module.get<PayoneerAccountService>(PayoneerAccountService);
    programTokenRepository = module.get(ProgramTokensRepository);
    payoneerClient = module.get(PayoneerHttpClient);
    programTokenService = module.get(ProgramTokenService);

    // Mock convertToCamelCase
    (utils.convertToCamelCase as jest.Mock) = jest.fn().mockImplementation((data) => data);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getTransactions', () => {
    const mockGetTransactionsInput: payoneer.GetPayInTransactionQueryInput = {
      clientId: mockClientId,
      fromTransactionDate: '2023-01-01',
      toTransactionDate: '2023-12-31',
      pageSize: 10,
      category: PaymentCategoryMap[PaymentCategory.Fees],
      lastTransactionId: 'last-tx-id',
      lastTransactionDate: '2023-12-30',
    };

    const mockClientAccount = {
      id: 'mock-id',
      clientId: mockClientId,
      accountId: mockAccountId,
      tokenType: TokenType.AccessToken,
      accessToken: mockAccessToken,
      refreshToken: 'mock-refresh-token',
      refreshTokenExpiresAt: new Date(Date.now() + 7200000),
      scope: 'read write',
      idToken: 'mock-id-token',
      applicationId: 'mock-application-id',
      consentedAt: new Date(),
      expiresAt: new Date(Date.now() + 3600000),
      programId: mockProgramId,
      payeeId: 'mock-payee-id',
      payerId: 'mock-payer-id',
      merchantAccountId: 'mock-merchant-account-id',
      merchantPayerId: 'mock-merchant-payer-id',
      merchantPayeeId: 'mock-merchant-payee-id',
      merchantProgramId: 'mock-merchant-program-id',
      partnerClientId: 'mock-partner-client-id',
      partnerProgramId: 'mock-partner-program-id',
      partnerPayerId: 'mock-partner-payer-id',
      partnerPayeeId: 'mock-partner-payee-id',
      partnerMerchantAccountId: 'mock-partner-merchant-account-id',
      partnerMerchantPayerId: 'mock-partner-merchant-payer-id',
      partnerMerchantPayeeId: 'mock-partner-merchant-payee-id',
      partnerMerchantProgramId: 'mock-partner-merchant-program-id',
      redirectUri: 'https://mock-redirect.com',
      state: 'mock-state',
      codeVerifier: 'mock-code-verifier',
      codeChallenge: 'mock-code-challenge',
      codeChallengeMethod: 'S256',
      authorizationCode: 'mock-auth-code',
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'human',
      updatedBy: 'human',
      deletedAt: null,
      deletedBy: null,
      hasId: () => true,
      save: () => Promise.resolve(mockClientAccount),
      remove: () => Promise.resolve(mockClientAccount),
      softRemove: () => Promise.resolve(mockClientAccount),
      recover: () => Promise.resolve(mockClientAccount),
      reload: () => Promise.resolve(mockClientAccount),
    } as unknown as ProgramTokensEntity;

    const mockTransactionsResponse = {
      result: {
        transactions: {
          items: [
            {
              id: 'tx1',
              amount: 100,
              balance_id: 'mock-balance-id',
              amount_currency: 'USD',
              date: '2023-01-01',
              description: 'mock-description',
              status_id: 1232,
              status_name: 'mock-status-name',
              balance_name: 'mock-balance-name',
              category_id: 1232,
              category_name: 'mock-category-name',
              category_description: 'mock-category-description',
              category_icon: 'mock-category-icon',
              category_color: 'mock-category-color',
              category_order: 1,
              category_active: true,
              category_deleted: false,
              category_deleted_at: null,
              category_deleted_by: null,
              category_created_at: new Date(),
              category_updated_at: new Date(),
              source: 'mock-source',
              target: 'mock-target',
              transactionDetails: {},
            },
          ],
          next: 'next-token',
          total: 1,
          balance: {
            balance_id: 'mock-balance-id',
            balance_name: 'mock-balance-name',
            balance_currency: 'USD',
            balance_amount: 100,
          },
        },
      },
      error: null,
    };

    it('should throw BadRequestException when client account not found', async () => {
      programTokenRepository.findOne.mockResolvedValue(null);

      await expect(service.getTransactions(mockGetTransactionsInput)).rejects.toThrow(
        new BadRequestException('Client account not found'),
      );
    });

    it('should throw error when transactions are undefined', async () => {
      programTokenRepository.findOne.mockResolvedValue(mockClientAccount);
      payoneerClient.getTransactions.mockResolvedValue([
        {
          result: {
            transactions: undefined,
          },
          error: {
            code: 0,
            message: '',
            description: '',
            target: '',
            errors: [],
          },
        },
        null,
      ]);

      await expect(service.getTransactions(mockGetTransactionsInput)).rejects.toThrow(
        'Transactions are undefined',
      );
    });

    it('should handle optional parameters correctly', async () => {
      const minimalInput: payoneer.GetPayInTransactionQueryInput = {
        clientId: mockClientId,
      };

      programTokenRepository.findOne.mockResolvedValue(mockClientAccount);
      payoneerClient.getTransactions.mockResolvedValue([mockTransactionsResponse, null]);

      await service.getTransactions(minimalInput);

      expect(payoneerClient.getTransactions).toHaveBeenCalledWith(
        mockClientId,
        mockAccountId,
        null, // fromTransactionDate
        null, // toTransactionDate
        undefined, // pageSize
        null, // category
        null, // status
        undefined, // lastTransactionId
        null, // lastTransactionDate
      );
    });
  });

  describe('getAccountBalance', () => {
    const mockAccount = {
      accountId: mockAccountId,
      accessToken: mockAccessToken,
      clientId: mockClientId,
      programId: mockProgramId,
      partnerClientId: mockPartnerClientId,
      partnerProgramId: mockPartnerProgramId,
      partnerPayerId: mockPartnerPayerId,
      partnerPayeeId: mockPartnerPayeeId,
      partnerMerchantAccountId: mockPartnerMerchantAccountId,
      partnerMerchantPayerId: mockPartnerMerchantPayerId,
      partnerMerchantPayeeId: mockPartnerMerchantPayeeId,
      partnerMerchantProgramId: mockPartnerMerchantProgramId,
      redirectUri: mockRedirectUri,
      state: mockState,
      codeVerifier: mockCodeVerifier,
      codeChallenge: mockCodeChallenge,
      codeChallengeMethod: mockCodeChallengeMethod,
      authorizationCode: mockAuthorizationCode,
      createdAt: mockCreatedAt,
      updatedAt: mockUpdatedAt,
      createdBy: mockCreatedBy,
      updatedBy: mockUpdatedBy,
      deletedAt: mockDeletedAt,
      deletedBy: mockDeletedBy,
      hasId: () => true,
      save: () => Promise.resolve(mockAccount),
      remove: () => Promise.resolve(mockAccount),
      softRemove: () => Promise.resolve(mockAccount),
      recover: () => Promise.resolve(mockAccount),
      reload: () => Promise.resolve(mockAccount),
    } as unknown as ProgramTokensEntity;

    const mockBalanceResponse = {
      result: {
        balance: 1000,
        currency: 'USD',
        balances: {
          items: [
            {
              id: 'balance-id-123',
              type: 'balance',
              currency: 'USD',
              status: 'active',
              status_name: 'Active',
              available_balance: '1000',
              update_time: '2023-01-01T00:00:00Z',
            },
          ],
        },
      },
    };

    it('should successfully get account balance', async () => {
      programTokenService.getAccount.mockResolvedValue(mockAccount);
      payoneerClient.getAccountBalances.mockResolvedValue([mockBalanceResponse, null]);

      const result = await service.getAccountBalance(mockClientId);

      expect(programTokenService.getAccount).toHaveBeenCalledWith(mockClientId);
      expect(payoneerClient.getAccountBalances).toHaveBeenCalledWith(mockClientId, mockAccountId);
      expect(result).toEqual(mockBalanceResponse);
    });

    it('should throw BadRequestException when no mandate found', async () => {
      programTokenService.getAccount.mockResolvedValue(null);

      await expect(service.getAccountBalance(mockClientId)).rejects.toThrow(
        new RpcException(new BadRequestException('No mandate found for client')),
      );
    });

    it('should throw InternalServerErrorException when payoneer client returns error', async () => {
      const mockError = {
        error: 'Payoneer API error',
        errors: [],
        status: 500,
        message: 'Payoneer API error',
      };
      programTokenService.getAccount.mockResolvedValue(mockAccount);
      payoneerClient.getAccountBalances.mockResolvedValue([null, mockError]);

      await expect(service.getAccountBalance(mockClientId)).rejects.toThrow(
        new RpcException(new InternalServerErrorException(mockError)),
      );
    });

    it('should handle unexpected errors and wrap in RpcException', async () => {
      const unexpectedError = new Error('Unexpected error');
      programTokenService.getAccount.mockRejectedValue(unexpectedError);

      await expect(service.getAccountBalance(mockClientId)).rejects.toThrow(
        new RpcException(unexpectedError),
      );
    });
  });

  describe('chargeAccountByClientPartnerDebit', () => {
    const mockAccount = {
      accountId: mockAccountId,
      accessToken: mockAccessToken,
      clientId: mockClientId,
      programId: mockProgramId,
      partnerClientId: mockPartnerClientId,
      partnerProgramId: mockPartnerProgramId,
      partnerPayerId: mockPartnerPayerId,
      partnerPayeeId: mockPartnerPayeeId,
      partnerMerchantAccountId: mockPartnerMerchantAccountId,
      partnerMerchantPayerId: mockPartnerMerchantPayerId,
      partnerMerchantPayeeId: mockPartnerMerchantPayeeId,
      partnerMerchantProgramId: mockPartnerMerchantProgramId,
      redirectUri: mockRedirectUri,
      state: mockState,
      codeVerifier: mockCodeVerifier,
      codeChallenge: mockCodeChallenge,
      codeChallengeMethod: mockCodeChallengeMethod,
      authorizationCode: mockAuthorizationCode,
      createdAt: mockCreatedAt,
      updatedAt: mockUpdatedAt,
      createdBy: mockCreatedBy,
      updatedBy: mockUpdatedBy,
      deletedAt: mockDeletedAt,
      deletedBy: mockDeletedBy,
      hasId: () => true,
      save: () => Promise.resolve(mockAccount),
      remove: () => Promise.resolve(mockAccount),
      softRemove: () => Promise.resolve(mockAccount),
      recover: () => Promise.resolve(mockAccount),
      reload: () => Promise.resolve(mockAccount),
    } as unknown as ProgramTokensEntity;

    const mockClientState = {
      currencyBalanceId: 'balance-id-123',
    };

    const mockInput: ChargeAccountByClientDebitRequest = {
      amount: 100,
      currency: 'USD',
      description: 'Test charge',
      client_reference_id: '',
      to: {
        type: '',
        id: '',
      },
    };

    const mockResponse = {
      paymentId: 'payment-123',
      status: 'success',
      result: {
        type: '',
        commit_id: '',
        client_reference_id: '',
        last_status: '',
        created_at: '',
        request_details: {
          client_reference_id: '',
          amount: 0,
          description: '',
          currency: '',
          to: {
            type: '',
            id: '',
          },
        },
        fx: {
          amount: 0,
          currency: '',
          quote: '',
          rate: 0,
          base_rate: 0,
          source_currency: '',
          target_currency: '',
        },
        fees: [],
        amounts: {
          charged: {
            amount: 0,
            currency: '',
          },
          target: {
            amount: 0,
            currency: '',
          },
        },
        expires_at: '',
      },
    };

    it('should successfully charge account by client partner debit', async () => {
      programTokenService.getAccount.mockResolvedValue(mockAccount);
      payoneerClient.chargeAccountByClientPartnerDebit.mockResolvedValue([mockResponse, null]);

      const result = await service.chargeAccountByClientPartnerDebit(
        mockClientId,
        mockClientState,
        mockInput,
      );

      expect(programTokenService.getAccount).toHaveBeenCalledWith(mockClientId);
      expect(payoneerClient.chargeAccountByClientPartnerDebit).toHaveBeenCalledWith(
        mockInput,
        mockClientId,
        mockAccessToken,
        mockClientState.currencyBalanceId,
      );
      expect(result).toEqual(mockResponse);
    });

    it('should throw BadRequestException when no mandate found', async () => {
      programTokenService.getAccount.mockResolvedValue(null);

      await expect(
        service.chargeAccountByClientPartnerDebit(mockClientId, mockClientState, mockInput),
      ).rejects.toThrow(new RpcException(new BadRequestException('No mandate found for client')));
    });

    it('should throw InternalServerErrorException when payoneer client returns error', async () => {
      const mockError = {
        error: 'Payoneer API error',
        errors: [],
        status: 500,
        message: 'Payoneer API error',
      };
      programTokenService.getAccount.mockResolvedValue(mockAccount);
      payoneerClient.chargeAccountByClientPartnerDebit.mockResolvedValue([null, mockError]);

      await expect(
        service.chargeAccountByClientPartnerDebit(mockClientId, mockClientState, mockInput),
      ).rejects.toThrow(new RpcException(new InternalServerErrorException(mockError)));
    });
  });

  describe('chargeAccountByClientDebit', () => {
    const mockAccount = {
      accountId: mockAccountId,
      accessToken: mockAccessToken,
      clientId: mockClientId,
      programId: mockProgramId,
      partnerClientId: mockPartnerClientId,
      partnerProgramId: mockPartnerProgramId,
      partnerPayerId: mockPartnerPayerId,
      partnerPayeeId: mockPartnerPayeeId,
      partnerMerchantAccountId: mockPartnerMerchantAccountId,
      partnerMerchantPayerId: mockPartnerMerchantPayerId,
      partnerMerchantPayeeId: mockPartnerMerchantPayeeId,
      partnerMerchantProgramId: mockPartnerMerchantProgramId,
      redirectUri: mockRedirectUri,
      state: mockState,
      codeVerifier: mockCodeVerifier,
      codeChallenge: mockCodeChallenge,
      codeChallengeMethod: mockCodeChallengeMethod,
      authorizationCode: mockAuthorizationCode,
      createdAt: mockCreatedAt,
      updatedAt: mockUpdatedAt,
      createdBy: mockCreatedBy,
      updatedBy: mockUpdatedBy,
      deletedAt: mockDeletedAt,
      deletedBy: mockDeletedBy,
      hasId: () => true,
      save: () => Promise.resolve(mockAccount),
      remove: () => Promise.resolve(mockAccount),
      softRemove: () => Promise.resolve(mockAccount),
      recover: () => Promise.resolve(mockAccount),
      reload: () => Promise.resolve(mockAccount),
    } as unknown as ProgramTokensEntity;

    const mockClientState = {
      currencyBalanceId: 'balance-id-123',
    };

    const mockInput: ChargeAccountByClientDebitRequest = {
      amount: 100,
      currency: 'USD',
      description: 'Test charge',
      client_reference_id: '',
      to: {
        type: '',
        id: '',
      },
    };

    const mockResponse = {
      paymentId: 'payment-123',
      status: 'success',
      result: {
        type: '',
        commit_id: '',
        client_reference_id: '',
        last_status: '',
        created_at: '',
        request_details: {
          client_reference_id: '',
          amount: 0,
          description: '',
          currency: '',
          to: {
            type: '',
            id: '',
          },
        },
        fx: {
          amount: 0,
          currency: '',
          quote: '',
          rate: 0,
          base_rate: 0,
          source_currency: '',
          target_currency: '',
        },
        fees: [],
        amounts: {
          amount: 0,
          currency: '',
          charged: {
            amount: 0,
            currency: '',
          },
          target: {
            amount: 0,
            currency: '',
          },
        },
        expires_at: '',
      },
    };

    it('should successfully charge account by client debit', async () => {
      programTokenService.getAccount.mockResolvedValue(mockAccount);
      payoneerClient.chargeAccountByClientDebit.mockResolvedValue([mockResponse, null]);

      const result = await service.chargeAccountByClientDebit(mockClientId, mockClientState, mockInput);

      expect(programTokenService.getAccount).toHaveBeenCalledWith(mockClientId);
      expect(payoneerClient.chargeAccountByClientDebit).toHaveBeenCalledWith(
        mockInput,
        mockClientId,
        mockAccountId,
        mockClientState.currencyBalanceId,
      );
      expect(result).toEqual(mockResponse);
    });

    it('should throw InternalServerErrorException when payoneer client returns error', async () => {
      const mockError = {
        error: 'API error',
        errors: [],
        status: 500,
        message: 'API error',
      };
      programTokenService.getAccount.mockResolvedValue(mockAccount);
      payoneerClient.chargeAccountByClientDebit.mockResolvedValue([null, mockError]);

      await expect(
        service.chargeAccountByClientDebit(mockClientId, mockClientState, mockInput),
      ).rejects.toThrow(new RpcException(new InternalServerErrorException(mockError)));
    });
  });

  describe('getPaymentStatus', () => {
    const mockInput: payoneer.GetPaymentStatusInput = {
      clientId: mockClientId,
      paymentId: 'payment-123',
      clientReferenceId: 'ref-123',
    };

    const mockResponse = {
      status: 'completed',
      amount: 100,
      result: {
        cancel_date: new Date(),
        cancelPaymentId: 'cancel-123',
        status: 1,
        status_description: PaymentStatus.Completed,
        payment_id: 'payment-123',
      },
    };

    it('should successfully get payment status', async () => {
      programTokenService.getAccount.mockResolvedValue(mockAccount);
      payoneerClient.getPaymentStatus.mockResolvedValue([mockResponse, null]);

      const result = await service.getPaymentStatus(mockInput);

      expect(programTokenService.getAccount).toHaveBeenCalledWith(mockClientId);
      expect(payoneerClient.getPaymentStatus).toHaveBeenCalledWith(
        mockClientId,
        mockAccountId,
        mockInput.paymentId,
        mockInput.clientReferenceId,
      );
      expect(result).toEqual(mockResponse);
    });

    it('should throw InternalServerErrorException when payoneer client returns error', async () => {
      const mockError = {
        error: 'API error',
        errors: [],
        status: 500,
        message: 'API error',
      };
      programTokenService.getAccount.mockResolvedValue(mockAccount);
      payoneerClient.getPaymentStatus.mockResolvedValue([null, mockError]);

      await expect(service.getPaymentStatus(mockInput)).rejects.toThrow(
        new RpcException(new InternalServerErrorException(mockError)),
      );
    });
  });

  describe('cancelPayment', () => {
    const mockInput: payoneer.CancelPaymentInput = {
      clientId: mockClientId,
      paymentId: 'payment-123',
    };

    const mockResponse = {
      cancelled: true,
      paymentId: 'payment-123',
      result: {
        cancel_date: new Date(),
        cancelPaymentId: 'cancel-123',
      },
    };

    it('should successfully cancel payment', async () => {
      programTokenService.getAccount.mockResolvedValue(mockAccount);
      payoneerClient.cancelCharge.mockResolvedValue([mockResponse, null]);

      const result = await service.cancelPayment(mockInput);

      expect(programTokenService.getAccount).toHaveBeenCalledWith(mockClientId);
      expect(payoneerClient.cancelCharge).toHaveBeenCalledWith(
        mockClientId,
        mockAccountId,
        mockInput.paymentId,
      );
      expect(result).toEqual(mockResponse);
    });

    it('should throw InternalServerErrorException when payoneer client returns error', async () => {
      const mockError = new Error('API error');
      programTokenService.getAccount.mockResolvedValue(mockAccount);
      payoneerClient.cancelCharge.mockResolvedValue([null, mockError]);

      await expect(service.cancelPayment(mockInput)).rejects.toThrow(
        new RpcException(new InternalServerErrorException(mockError)),
      );
    });
  });

  describe('getMerchantChargeStatus', () => {
    const mockInput: payoneer.GetMerchantChargeStatusInput = {
      clientId: mockClientId,
      programId: mockProgramId,
      clientReferenceId: 'ref-123',
    };

    const mockResponse = {
      status: 'pending',
      chargeId: 'charge-123',
      result: {
        cancel_date: new Date(),
        cancelPaymentId: 'cancel-123',
        status: 1,
        status_description: PaymentStatus.Completed,
        fees: [],
        fx: {
          amount: 100,
          currency: 'USD',
          quote: '1.00',
          rate: 1.0,
          base_rate: 1.0,
          source_currency: 'USD',
          target_currency: 'USD',
        },
        amounts: {
          amount: 100,
          currency: 'USD',
          charged: {
            amount: 100,
            currency: 'USD',
          },
          target: {
            amount: 100,
            currency: 'USD',
          },
        },
        payment_id: 'payment-123',
        last_status: PaymentStatus.Completed,
        request_details: {
          client_reference_id: 'ref-123',
          amount: 100,
          description: 'Test charge',
          currency: 'USD',
          to: {
            type: 'merchant',
            id: 123,
          },
        },
      },
    };

    it('should successfully get merchant charge status', async () => {
      programTokenService.getAccount.mockResolvedValue(mockAccount);
      payoneerClient.getMerchantChargeStatus.mockResolvedValue([mockResponse, null]);

      const result = await service.getMerchantChargeStatus(mockInput);

      expect(programTokenService.getAccount).toHaveBeenCalledWith(mockClientId);
      expect(payoneerClient.getMerchantChargeStatus).toHaveBeenCalledWith(
        mockClientId,
        mockProgramId,
        mockInput.clientReferenceId,
        mockAccountId,
      );
      expect(result).toEqual(mockResponse);
    });

    it('should throw InternalServerErrorException when payoneer client returns error', async () => {
      const mockError = {
        error: 'API error',
        errors: [],
        status: 500,
        message: 'API error',
      };
      programTokenService.getAccount.mockResolvedValue(mockAccount);
      payoneerClient.getMerchantChargeStatus.mockResolvedValue([null, mockError]);

      await expect(service.getMerchantChargeStatus(mockInput)).rejects.toThrow(
        new RpcException(new InternalServerErrorException(mockError)),
      );
    });

    it('should successfully get merchant charge status', async () => {
      programTokenService.getAccount.mockResolvedValue(mockAccount);
      payoneerClient.getMerchantChargeStatus.mockResolvedValue([mockResponse, null]);

      const result = await service.getMerchantChargeStatus(mockInput);

      expect(programTokenService.getAccount).toHaveBeenCalledWith(mockClientId);
      expect(payoneerClient.getMerchantChargeStatus).toHaveBeenCalledWith(
        mockClientId,
        mockProgramId,
        mockInput.clientReferenceId,
        mockAccountId,
      );
      expect(result).toEqual(mockResponse);
    });

    it('should throw InternalServerErrorException when payoneer client returns error', async () => {
      const mockError = {
        error: 'API error',
        errors: [],
        status: 500,
        message: 'API error',
      };
      programTokenService.getAccount.mockResolvedValue(mockAccount);
      payoneerClient.getMerchantChargeStatus.mockResolvedValue([null, mockError]);

      await expect(service.getMerchantChargeStatus(mockInput)).rejects.toThrow(
        new RpcException(new InternalServerErrorException(mockError)),
      );
    });
  });
});

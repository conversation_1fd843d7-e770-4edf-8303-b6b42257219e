import { LessThanOrEqual } from 'typeorm';
import { ProgramTokensRepository } from '../../common/repositories/program-token.repository';
import { TokenType } from '../../common/constants/enums';
import {
  GetFundingsAccountsResponse,
  ReceivingAccount,
} from '../../common/data-types/payoneer-v4.data.types';
import { PayoneerHttpClient } from '../../common/external-service/payoneer/services/payoneer.http.client.service';
import { BadRequestException, Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { ProgramTokensEntity } from '../../common/entities/program-token.entity';
import { PayoneerAccountEntity } from '../../common/entities/account.entitiy';
import * as countries from 'i18n-iso-countries';
import { extractCountryFromAddress, getRoutingCodeType } from '../../common/helpers/utils';
import { PayoneerAccountRepository } from '../../common/repositories/account.repository';
import { AppConfigService } from '../../config/services/app.config.service';
import { RpcException } from '@nestjs/microservices';
import { ProgramTokenServiceHelper } from './helper/program-token.service.helper';
import {
  AccessScope,
  BeneficiaryRegistrationPurpose,
} from '../../beneficiary/dtos/inputs/beneficiary.graphql.input';

@Injectable()
export class ProgramTokenService {
  private readonly logger = new Logger(ProgramTokenService.name);

  constructor(
    private readonly programTokenRepository: ProgramTokensRepository,
    private readonly accountRepository: PayoneerAccountRepository,
    private readonly payoneerClient: PayoneerHttpClient,
    private readonly appConfigService: AppConfigService,
    private readonly programTokenServiceHelper: ProgramTokenServiceHelper,
  ) {}

  async getAccount(clientId: string): Promise<ProgramTokensEntity> {
    const token = await this.programTokenRepository.findOne({
      where: { clientId, tokenType: TokenType.AccessToken },
    });

    if (!token) {
      throw new BadRequestException('No token found for client');
    }

    return token;
  }

  /**
   * This function will find out the tokens which are going to be expired in 7 days and refreshed them
   */
  async refreshTokenToBeExpired(): Promise<void> {
    let sevenDaysAfter = new Date();
    sevenDaysAfter.setDate(sevenDaysAfter.getDate() + 7);

    // Get all tokens which are going to expire in 7 days
    const programTokens = await this.programTokenRepository.getTokens({
      expiresAt: LessThanOrEqual(sevenDaysAfter),
      tokenType: TokenType.AccessToken,
    });

    if (!programTokens.length) {
      this.logger.log('No tokens to refresh');
      return;
    }

    for (const item of programTokens) {
      const logData = { id: item.id, accountId: item.accountId };
      this.logger.error({ message: 'refrshing token for', data: logData });
      try {
        let purpose = BeneficiaryRegistrationPurpose.PAYIN;
        if (item.clientId == this.appConfigService.getSkuadSGId()) {
          purpose = BeneficiaryRegistrationPurpose.SSO;
        }

        const [data, error] = await this.payoneerClient.refreshAccessToken(
          item.refreshToken,
          item.clientId,
          purpose,
        );

        if (error || !data) {
          this.logger.error(
            JSON.stringify({
              message: 'Got error while refreshing tokens',
              error,
              data: logData,
            }),
          );
          continue;
        }

        const updateObj: Partial<ProgramTokensEntity> = {
          accessToken: data.access_token,
          expiresAt: this.programTokenServiceHelper.getTokenExpirationDateTime(data, 'expires_in'),
          consentedAt: new Date(data.consented_on * 1000),
          scope: data.scope?.split(' ').join(','),
          refreshToken: data.refresh_token,
          refreshTokenExpiresAt: this.programTokenServiceHelper.getTokenExpirationDateTime(
            data,
            'refresh_token_expires_in',
          ),
          updatedBy: 'CRON',
        };

        await this.programTokenRepository.update(item.id, updateObj);
      } catch (error) {
        this.logger.error(
          JSON.stringify({
            message: 'Got error while refreshing token',
            error,
            data: logData,
          }),
        );
      }
    }
  }

  /**
   * This function will find out the receiving accounts and populate them in db
   */
  async populateFundingAccounts(): Promise<void> {
    // find out distinct provider account ids from program token repo
    const programTokens = await this.programTokenRepository.find({
      select: ['accountId', 'clientId'],
      where: {
        tokenType: TokenType.AccessToken,
        clientId: this.appConfigService.getSkuadSGId(),
      },
    });

    for (const item of programTokens) {
      const logData = { id: item.accountId };
      this.logger.error({ message: 'populating accounts for ', data: logData });
      try {
        const [data, error] = await this.payoneerClient.getFundingAccounts(item.clientId, item.accountId);
        if (error || !data) {
          this.logger.error(
            JSON.stringify({
              message: 'Got error while populate Funding accounts',
              error,
              data: logData,
            }),
          );
          continue;
        }
        await this.transformAndInsertFundingAccounts(data, item.accountId);
      } catch (error) {
        this.logger.error(
          JSON.stringify({
            message: 'Got error while populating accounts',
            error,
            data: logData,
          }),
        );
      }
    }
  }

  async transformAndInsertFundingAccounts(account: GetFundingsAccountsResponse, accountId: string) {
    const accounts = account?.result?.receiving_accounts?.items;
    for (const account of accounts) {
      try {
        const accountEntityObj = this.getAccountEntityObjectFromPayoneerAccountResponse(account, accountId);
        const existingAccount = await this.accountRepository.findOne({
          where: { externalId: accountEntityObj.externalId },
        });
        if (existingAccount) {
          continue;
        }
        await this.accountRepository.save(accountEntityObj);
      } catch (error) {
        this.logger.error({
          message: 'Got error while populating accounts',
          error,
          data: { accountId, account },
        });
      }
    }
  }

  getAccountEntityObjectFromPayoneerAccountResponse(
    account: ReceivingAccount,
    accountId: string,
    actor = 'CRON',
  ): PayoneerAccountEntity {
    const accountsEntityObj = new PayoneerAccountEntity();
    accountsEntityObj.providerAccountId = accountId;
    accountsEntityObj.externalId = account.id;
    accountsEntityObj.currency = account.currency;
    accountsEntityObj.createdBy = actor;
    accountsEntityObj.updatedBy = actor;
    accountsEntityObj.programId = this.appConfigService.getProviderProgramId() ?? '*********';

    for (const field of account?.details?.items || []) {
      switch (field.name) {
        case 'BeneficiaryName':
          accountsEntityObj.accountName = field.value;
          break;
        case 'BankName':
          accountsEntityObj.bankName = field.value;
          break;
        case 'AccountNumber':
        case 'IBAN':
          accountsEntityObj.accountNumber = field.value;
          break;
        case 'BankAddress':
          accountsEntityObj.bankAddress = field.value || '';
          break;
        case 'BankCountry':
          accountsEntityObj.bankCountry = countries.getAlpha2Code(field.value.trim(), 'en');
          break;
        case 'RoutingABA':
        case 'BIC':
        case 'SortCode': {
          accountsEntityObj.routingCode = field.value;
          accountsEntityObj.routingCodeType = getRoutingCodeType(field.value);
          break;
        }
      }
    }

    if (!accountsEntityObj.bankCountry && accountsEntityObj.bankAddress) {
      accountsEntityObj.bankCountry = extractCountryFromAddress(accountsEntityObj.bankAddress);
    }
    accountsEntityObj.bankCountry = accountsEntityObj.bankCountry || 'US';
    accountsEntityObj.bankAddress = accountsEntityObj.bankAddress || '';
    accountsEntityObj.customerId = this.getPayoneerCustomerId(accountsEntityObj);
    return accountsEntityObj;
  }

  getPayoneerCustomerId(accountsEntityObj: PayoneerAccountEntity): string {
    if (accountsEntityObj.currency == 'USD') {
      return accountsEntityObj.accountNumber;
    }
    return accountsEntityObj.accountNumber;
  }

  async generateAccessToken(code: string, redirectUrl: string, purpose: BeneficiaryRegistrationPurpose) {
    return await this.payoneerClient.generateAccessToken(code, redirectUrl, purpose);
  }

  async saveAccessToken(
    clientId: string,
    code: string,
    redirectUrl: string,
    userId: string,
  ): Promise<{
    accountId: string;
  }> {
    try {
      const [data, error] = await this.generateAccessToken(
        code,
        redirectUrl,
        BeneficiaryRegistrationPurpose.PAYIN,
      );

      if (error || !data) {
        this.logger.error({
          message: 'Got error while generating access token',
          error,
          data: { clientId, code },
        });
        throw new RpcException(error);
      }

      const existingAccessToken = await this.programTokenRepository.findOne({
        where: { clientId, tokenType: TokenType.AccessToken },
      });

      const inputObj: Partial<ProgramTokensEntity> = {
        accessToken: data.access_token,
        consentedAt: new Date(data.consented_on * 1000),
        accountId: this.programTokenServiceHelper.getAccountIdFromIdToken(data?.id_token),
        expiresAt: this.programTokenServiceHelper.getTokenExpirationDateTime(data, 'expires_in'),
        refreshToken: data.refresh_token,
        refreshTokenExpiresAt: this.programTokenServiceHelper.getTokenExpirationDateTime(
          data,
          'refresh_token_expires_in',
        ),
        tokenType: TokenType.AccessToken,
        scope: data.scope?.split(' ').join(','),
        idToken: data?.id_token,
        clientId: clientId,
        createdBy: userId,
        updatedBy: userId,
      };

      if (existingAccessToken) {
        await this.programTokenRepository.updateToDatabase({ id: existingAccessToken.id }, inputObj);
      } else {
        await this.programTokenRepository.save(inputObj);
      }

      return {
        accountId: inputObj.accountId,
      };
    } catch (error) {
      this.logger.error({
        message: 'Got error while saving access token',
        error,
        data: { clientId, code },
      });
      throw new RpcException(error?.message);
    }
  }

  async revokeClientAccessToken(clientId: string, userId: string): Promise<{ status: string }> {
    try {
      const programToken = await this.programTokenRepository.findOne({
        where: { clientId, tokenType: TokenType.AccessToken },
      });

      if (!programToken) {
        throw new BadRequestException('No mandate found for client');
      }

      const [data, error] = await this.payoneerClient.revokeClientAccessToken(
        programToken.accessToken,
        programToken.tokenType,
        clientId,
        BeneficiaryRegistrationPurpose.PAYIN,
      );

      if (error || !data) {
        this.logger.error({
          message: 'Got error while revoking token',
          error,
          data: { clientId, userId },
        });
        throw new InternalServerErrorException(error);
      }

      await this.programTokenRepository.update(
        {
          id: programToken.id,
        },
        {
          deletedAt: new Date(),
          deletedBy: userId,
        },
      );

      return {
        status: 'success',
      };
    } catch (error) {
      this.logger.error({
        message: 'Got error while revoking token',
        error,
        data: { clientId, userId },
      });
      throw new RpcException(error?.message);
    }
  }

  createAuthorizationLink(
    redirectUrl: string,
    purpose: BeneficiaryRegistrationPurpose,
    state?: string,
  ): string {
    const scope = [AccessScope.Read, AccessScope.PersonalDetails, AccessScope.OpenId];
    if ([BeneficiaryRegistrationPurpose.PAYIN, BeneficiaryRegistrationPurpose.PAYOUT].includes(purpose)) {
      scope.push(AccessScope.Write);
    }

    return this.payoneerClient.getClientAccountCodeUrl({
      redirectUrl,
      scope: encodeURIComponent(scope.join(' ')),
      state,
      purpose,
    });
  }

  async checkConsentStatus(clientId: string): Promise<boolean> {
    const programToken = await this.programTokenRepository.findOne({
      where: { clientId, deletedAt: null },
    });
    return !!programToken;
  }
}

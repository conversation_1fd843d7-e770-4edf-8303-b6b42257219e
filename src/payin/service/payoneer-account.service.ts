import { ProgramTokensRepository } from '../../common/repositories/program-token.repository';
import {
  PaymentCategory,
  PaymentCategoryMap,
  PaymentStatusMap,
  TokenType,
} from '../../common/constants/enums';
import { PayoneerHttpClient } from '../../common/external-service/payoneer/services/payoneer.http.client.service';
import { BadRequestException, Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { convertToCamelCase } from '../../common/helpers/utils';
import { payoneer } from '@skuad/proto-utils/dist/interface/proto/payoneer/payoneer';
import { ProgramTokenService } from './program-token.service';
import { RpcException } from '@nestjs/microservices';
import { ChargeAccountByClientDebitRequest } from 'src/common/data-types/payoneer-v4.data.types';

@Injectable()
export class PayoneerAccountService {
  private readonly logger = new Logger(PayoneerAccountService.name);

  constructor(
    private readonly programTokenRepository: ProgramTokensRepository,
    private readonly payoneerClient: PayoneerHttpClient,
    private readonly programTokenService: ProgramTokenService,
  ) {}

  async getTransactions(getTransactionsInput: payoneer.GetPayInTransactionQueryInput) {
    const clientAccount = await this.programTokenRepository.findOne({
      where: { clientId: getTransactionsInput.clientId, tokenType: TokenType.AccessToken },
    });

    if (!clientAccount) {
      throw new BadRequestException('Client account not found');
    }

    const [result, error] = await this.payoneerClient.getTransactions(
      getTransactionsInput.clientId,
      clientAccount.accountId,
      getTransactionsInput.fromTransactionDate ? new Date(getTransactionsInput.fromTransactionDate) : null,
      getTransactionsInput.toTransactionDate ? new Date(getTransactionsInput.toTransactionDate) : null,
      getTransactionsInput?.pageSize,
      getTransactionsInput?.category
        ? PaymentCategoryMap[getTransactionsInput?.category as unknown as PaymentCategory]
        : null,
      getTransactionsInput?.status
        ? PaymentStatusMap[getTransactionsInput?.status as unknown as keyof typeof PaymentStatusMap]
        : null,
      getTransactionsInput?.lastTransactionId,
      getTransactionsInput?.lastTransactionDate ? new Date(getTransactionsInput?.lastTransactionDate) : null,
    );

    if (error) {
      throw error;
    }

    const transactions = result?.result?.transactions;

    if (!transactions) {
      throw new Error('Transactions are undefined');
    }

    const responseData = convertToCamelCase(transactions);

    const response: payoneer.TransactionResult = {
      transactions: {
        items: responseData?.items as payoneer.TransactionItem[],
        next: responseData?.next,
        total: responseData?.total,
      },
    };

    return response;
  }

  async getAccountBalance(clientId: string): Promise<payoneer.GetAccountBalanceOutput> {
    try {
      const account = await this.programTokenService.getAccount(clientId);
      if (!account) {
        throw new BadRequestException('No mandate found for client');
      }
      const [balance, error] = await this.payoneerClient.getAccountBalances(clientId, account.accountId);

      if (error) {
        throw error;
      }

      return convertToCamelCase(balance) as payoneer.GetAccountBalanceOutput;
    } catch (error) {
      this.logger.error(
        JSON.stringify({ errorMessage: 'Error while fetching account balance', error, data: { clientId } }),
      );

      throw error;
    }
  }

  async chargeAccountByClientPartnerDebit(
    clientId: string,
    clientState: Record<string, any>,
    input: ChargeAccountByClientDebitRequest,
  ): Promise<payoneer.ChargeAccountByClientPartnerDebitResponse> {
    try {
      const account = await this.programTokenService.getAccount(clientId);
      if (!account) {
        throw new BadRequestException('No mandate found for client');
      }

      const [response, error] = await this.payoneerClient.chargeAccountByClientPartnerDebit(
        input,
        clientId,
        account.accessToken,
        clientState.currencyBalanceId,
      );

      if (error) {
        throw new InternalServerErrorException(error);
      }

      return convertToCamelCase(response);
    } catch (error) {
      this.logger.error(
        JSON.stringify({
          errorMessage: 'Error while charging account using partner debit',
          error,
          data: { clientId },
        }),
      );

      throw new RpcException(error?.message);
    }
  }

  async chargeAccountByClientDebit(
    clientId: string,
    clientState: Record<string, any>,
    input: ChargeAccountByClientDebitRequest,
  ): Promise<payoneer.ChargeAccountByClientDebitResponse> {
    try {
      const account = await this.programTokenService.getAccount(clientId);

      const [response, error] = await this.payoneerClient.chargeAccountByClientDebit(
        input,
        clientId,
        account.accountId,
        clientState.currencyBalanceId,
      );

      if (error) {
        throw new InternalServerErrorException(error);
      }

      return convertToCamelCase(response);
    } catch (error) {
      this.logger.error(
        JSON.stringify({
          errorMessage: 'Error while charging account using client debit',
          error,
          data: { clientId, clientState, input },
        }),
      );

      throw new RpcException(error?.message);
    }
  }

  async getPaymentStatus(input: payoneer.GetPaymentStatusInput): Promise<payoneer.GetPaymentStatusResponse> {
    try {
      const account = await this.programTokenService.getAccount(input.clientId);
      const [response, error] = await this.payoneerClient.getPaymentStatus(
        input.clientId,
        account.accountId,
        input.paymentId,
        input.clientReferenceId,
      );

      if (error) {
        throw error;
      }

      return convertToCamelCase(response);
    } catch (error) {
      this.logger.error(
        JSON.stringify({
          errorMessage: 'Error while fetching payment status',
          error,
          data: input,
        }),
      );

      throw error;
    }
  }

  async cancelPayment(input: payoneer.CancelPaymentInput): Promise<payoneer.CancelPaymentOutput> {
    try {
      const account = await this.programTokenService.getAccount(input.clientId);
      const [response, error] = await this.payoneerClient.cancelCharge(
        input.clientId,
        account.accountId,
        input.paymentId,
      );

      if (error) {
        throw error;
      }

      return convertToCamelCase(response);
    } catch (error) {
      this.logger.error(
        JSON.stringify({
          errorMessage: 'Error while cacnelling payment',
          error,
          data: input,
        }),
      );

      throw error?.message;
    }
  }

  async getMerchantChargeStatus(
    input: payoneer.GetMerchantChargeStatusInput,
  ): Promise<payoneer.GetMerchantChargeStatusOutput> {
    try {
      const account = await this.programTokenService.getAccount(input.clientId);
      const [response, error] = await this.payoneerClient.getMerchantChargeStatus(
        input.clientId,
        input.programId,
        input.clientReferenceId,
        account.accountId,
      );

      if (error) {
        throw error;
      }

      return convertToCamelCase(response);
    } catch (error) {
      this.logger.error(
        JSON.stringify({
          errorMessage: 'Error in getMerchantChargeStatus',
          error,
          data: input,
        }),
      );

      throw error?.message;
    }
  }
}

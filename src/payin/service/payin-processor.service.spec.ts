import { Test, TestingModule } from '@nestjs/testing';
import { PayInProcessorService } from './payin-processor.service';
import { PayoneerAccountRepository } from '../../common/repositories/account.repository';
import { Currency } from '../../common/constants/enums';
import { PayoneerAccountEntity } from '../../common/entities/account.entitiy';
import { ProgramTokenService } from './program-token.service';
import { PayoneerHttpClient } from '../../common/external-service/payoneer/services/payoneer.http.client.service';

describe('PayInProcessorService', () => {
  let service: PayInProcessorService;
  let repository: PayoneerAccountRepository;

  const mockAccountRepository = {
    getSubAccountByClientLegalEntityId: jest.fn(),
    fetchSubAccountWhichIsNotLinkedToClient: jest.fn(),
    updateToDatabase: jest.fn(),
    findOne: jest.fn(),
  };

  const mockProgramTokenService = {}; // No specific use in this test, empty mock is fine
  const mockPayoneerHttpClient = {}; // No specific use in this test, empty mock is fine

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PayInProcessorService,
        {
          provide: PayoneerAccountRepository,
          useValue: mockAccountRepository,
        },
        {
          provide: ProgramTokenService,
          useValue: mockProgramTokenService,
        },
        {
          provide: PayoneerHttpClient,
          useValue: mockPayoneerHttpClient,
        },
      ],
    }).compile();

    service = module.get<PayInProcessorService>(PayInProcessorService);
    repository = module.get<PayoneerAccountRepository>(PayoneerAccountRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createSubAccount', () => {
    it('should return error when mandatory parameters are not provided', async () => {
      const [result, error] = await service.createSubAccount('', Currency.USD);
      expect(result).toBeNull();
      expect(error).toBeInstanceOf(Error);
      expect(error.message).toBe('Mandatory parameters not provided');
    });

    it('should return existing account if already linked', async () => {
      const mockAccount = { customerId: 'existing-id' };
      mockAccountRepository.getSubAccountByClientLegalEntityId.mockResolvedValue(mockAccount);

      const [result, error] = await service.createSubAccount('client-id', Currency.USD);
      expect(result).toBe('existing-id');
      expect(error).toBeNull();
    });

    it('should return error when no sub-account is available', async () => {
      mockAccountRepository.getSubAccountByClientLegalEntityId.mockResolvedValue(null);
      mockAccountRepository.fetchSubAccountWhichIsNotLinkedToClient.mockResolvedValue(null);

      const [result, error] = await service.createSubAccount('client-id', Currency.USD);
      expect(result).toBeNull();
      expect(error).toBeInstanceOf(Error);
      expect(error.message).toBe('No sub-account available');
    });

    it('should create new sub-account successfully', async () => {
      const mockNewAccount = {
        id: 'acc-id',
        customerId: 'new-id',
        providerAccountId: 'provider-id',
      };
      mockAccountRepository.getSubAccountByClientLegalEntityId.mockResolvedValue(null);
      mockAccountRepository.fetchSubAccountWhichIsNotLinkedToClient.mockResolvedValue(mockNewAccount);
      mockAccountRepository.updateToDatabase.mockResolvedValue({ affected: 1 });

      const [result, error] = await service.createSubAccount('client-id', Currency.USD);
      expect(result).toBe('new-id');
      expect(error).toBeNull();
    });

    it('should retry if updateToDatabase returns 0 affected', async () => {
      const mockNewAccount = {
        id: 'acc-id',
        customerId: 'new-id',
        providerAccountId: 'provider-id',
      };
      mockAccountRepository.getSubAccountByClientLegalEntityId.mockResolvedValue(null);
      mockAccountRepository.fetchSubAccountWhichIsNotLinkedToClient.mockResolvedValue(mockNewAccount);
      mockAccountRepository.updateToDatabase
        .mockResolvedValueOnce({ affected: 0 })
        .mockResolvedValueOnce({ affected: 1 });

      const [result, error] = await service.createSubAccount('client-id', Currency.USD);
      expect(mockAccountRepository.updateToDatabase).toHaveBeenCalledTimes(2);
      expect(result).toBe('new-id');
      expect(error).toBeNull();
    });

    it('should handle unexpected exception', async () => {
      mockAccountRepository.getSubAccountByClientLegalEntityId.mockRejectedValue(new Error('DB failure'));

      const [result, error] = await service.createSubAccount('client-id', Currency.USD);
      expect(result).toBeNull();
      expect(error).toBeInstanceOf(Error);
      expect(error.message).toBe('DB failure');
    });
  });

  describe('fetchFundingAccount', () => {
    it('should return account when found', async () => {
      const mockAccount = new PayoneerAccountEntity();
      mockAccountRepository.findOne.mockResolvedValue(mockAccount);

      const result = await service.fetchFundingAccount('external-id');
      expect(result).toBe(mockAccount);
    });

    it('should return null when account not found', async () => {
      mockAccountRepository.findOne.mockResolvedValue(null);

      const result = await service.fetchFundingAccount('external-id');
      expect(result).toBeNull();
    });

    it('should throw error when findOne fails', async () => {
      mockAccountRepository.findOne.mockRejectedValue(new Error('DB error'));
      await expect(service.fetchFundingAccount('external-id')).rejects.toThrow('DB error');
    });
  });
});

import { Test, TestingModule } from '@nestjs/testing';
import { PayoneerHttpClient } from '../../../common/external-service/payoneer/services/payoneer.http.client.service';
import { RpcException } from '@nestjs/microservices';
import { NotFoundException } from '@nestjs/common';
import { ProgramTokenServiceHelper } from './program-token.service.helper';

describe('ProgramTokenServiceHelper', () => {
  let service: ProgramTokenServiceHelper;
  let mockPayoneerClient: jest.Mocked<PayoneerHttpClient>;

  beforeEach(async () => {
    mockPayoneerClient = {
      // Add methods as needed; example:
      someMethod: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProgramTokenServiceHelper,
        {
          provide: PayoneerHttpClient,
          useValue: mockPayoneerClient,
        },
      ],
    }).compile();

    service = module.get(ProgramTokenServiceHelper);
  });

  describe('getTokenExpirationDateTime', () => {
    it('should compute expiration datetime correctly', () => {
      const token = {
        consented_on: **********, // some epoch
        expires_in: 3600,
        refresh_token_expires_in: 7200,
        access_token: 'some_token',
        refresh_token: 'some_refresh_token',
        scope: 'some_scope',
        token_type: 'some_token_type',
        id_token: 'some_id_token',
        error: null,
        error_description: null,
      };

      const result = service.getTokenExpirationDateTime(token, 'expires_in');

      expect(result).toBeInstanceOf(Date);
      // You can add more specific checks e.g. valueOf(), toISOString(), etc
    });
  });

  describe('getAccountIdFromIdToken', () => {
    it('should extract account_id when token is valid', () => {
      const payload = { account_id: '12345' };
      const base64Payload = Buffer.from(JSON.stringify(payload)).toString('base64url');
      const idToken = `header.${base64Payload}.signature`;

      const accountId = service.getAccountIdFromIdToken(idToken);

      expect(accountId).toBe('12345');
    });

    it('should throw RpcException when account_id is missing', () => {
      const payload = {};
      const base64Payload = Buffer.from(JSON.stringify(payload)).toString('base64url');
      const idToken = `header.${base64Payload}.signature`;

      expect(() => service.getAccountIdFromIdToken(idToken)).toThrow(RpcException);
    });

    it('should throw RpcException on malformed token', () => {
      const badToken = 'bad.token';

      expect(() => service.getAccountIdFromIdToken(badToken)).toThrow(RpcException);
    });
  });
});

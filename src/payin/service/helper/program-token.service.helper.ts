import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { AccessTokenDto } from '../../../common/data-types/payoneer-v4.data.types';
import { RpcException } from '@nestjs/microservices';

@Injectable()
export class ProgramTokenServiceHelper {
  private readonly logger = new Logger(ProgramTokenServiceHelper.name);

  constructor() {}

  getTokenExpirationDateTime(token: AccessTokenDto, key: 'refresh_token_expires_in' | 'expires_in'): Date {
    // Convert consented_on timestamp to milliseconds (JavaScript uses ms)
    const consentedDate = new Date();
    // Calculate expiration date by adding the expires_in (in ms)
    const expirationDate = new Date(consentedDate.getTime() + token[key] * 1000);
    return expirationDate;
  }

  /**
   * Extract the `account_id` from the payload of a JWT idToken string.
   * @param idToken A string in the format `header.payload.signature`
   * @returns The `account_id` from the decoded payload, or `undefined` if not found
   */
  getAccountIdFromIdToken(idToken: string): string | undefined {
    try {
      const [, payload] = idToken.split('.');

      // Decode the Base64URL-encoded payload.
      const decodedPayload = atob(payload);

      // Parse the decoded string as JSON.
      const payloadObject = JSON.parse(decodedPayload);

      // Return the account_id property, if it exists.
      if (!payloadObject?.account_id) {
        throw new NotFoundException('account_id not found in idToken');
      }

      return payloadObject?.account_id;
    } catch (error) {
      this.logger.error('Error decoding idToken', error);
      throw new RpcException('Error decoding idToken');
    }
  }
}

import { Injectable, Logger } from '@nestjs/common';
import { Currency } from '../../common/constants/enums';
import { PayoneerAccountRepository } from '../../common/repositories/account.repository';
import { PayoneerAccountEntity } from '../../common/entities/account.entitiy';
import { IsNull } from 'typeorm';
import { ProgramTokenService } from './program-token.service';
import { PayoneerHttpClient } from '../../common/external-service/payoneer/services/payoneer.http.client.service';

@Injectable()
export class PayInProcessorService {
  private readonly logger = new Logger(PayInProcessorService.name);

  constructor(
    private readonly payoneerAccountRepository: PayoneerAccountRepository,
    private readonly programTokenService: ProgramTokenService,
    private readonly payoneerClient: PayoneerHttpClient,
  ) {}

  async createSubAccount(clientLegalEntityId: string, currency: Currency): Promise<[string, Error]> {
    try {
      if (!clientLegalEntityId || !currency) {
        return [null, new Error('Mandatory parameters not provided')];
      }
      // Check if clientLegalEntityId is linked or not.
      const account = await this.payoneerAccountRepository.getSubAccountByClientLegalEntityId(
        clientLegalEntityId,
        currency,
      );

      if (account) {
        return [account.customerId, null];
      }

      // If not linked, create a new sub-account.
      const newAccount =
        await this.payoneerAccountRepository.fetchSubAccountWhichIsNotLinkedToClient(currency);

      if (!newAccount) {
        return [null, new Error('No sub-account available')];
      }

      const result = await this.payoneerAccountRepository.updateToDatabase(
        {
          providerAccountId: newAccount?.providerAccountId,
          id: newAccount?.id,
          clientId: IsNull(),
        },
        {
          clientId: clientLegalEntityId,
          updatedAt: new Date(),
        },
        null,
      );

      if (!result.affected) {
        return this.createSubAccount(clientLegalEntityId, currency);
      }
      return [newAccount?.customerId, null];
    } catch (error) {
      return [null, error];
    }
  }

  async fetchFundingAccount(customerId: string): Promise<PayoneerAccountEntity> {
    return await this.payoneerAccountRepository.findOne({
      where: {
        customerId,
      },
    });
  }
}

import { MiddlewareConsumer, Module, NestModule, RequestMethod } from '@nestjs/common';
import { ReqAuthMiddleware } from '../common/middlewares/req-auth.middleware';
import { PayoneerClientModule } from '../common/external-service/payoneer/payoneer.module';
import { PayInProcessorService } from './service/payin-processor.service';
import { PayoneerPayInController } from './controller/private-payin.grpc.controller';
import { PayoneerAccountRepository } from '../common/repositories/account.repository';
import { ProgramTokensRepository } from '../common/repositories/program-token.repository';
import { ProgramTokenService } from './service/program-token.service';
import { PrivatePayInTriggerV1Controller } from './controller/private.payin.trigger.v1.controller';
import { ProgramTokensEntity } from '../common/entities/program-token.entity';
import { DatabaseModule } from '../database/database.module';
import { PayoneerAccountEntity } from '../common/entities/account.entitiy';
import { ProgramTokenServiceHelper } from './service/helper/program-token.service.helper';
import { ProgramTokenGrpcController } from './controller/private-program-token.controller';
import { ProgramTokenMutationResolver } from 'src/programs/resolver/program-token.resolver';
import { ProgramCurrencyMappingRepository } from 'src/common/repositories/program-currency-mapping.repository';
import { PayoneerAccountService } from './service/payoneer-account.service';

@Module({
  imports: [PayoneerClientModule, DatabaseModule.forPGFeature([ProgramTokensEntity, PayoneerAccountEntity])],
  providers: [
    PayInProcessorService,
    PayoneerAccountService,
    PayoneerAccountRepository,
    ProgramTokensRepository,
    ProgramCurrencyMappingRepository,
    ProgramTokenService,
    ProgramTokenServiceHelper,
    ProgramTokenMutationResolver,
  ],
  controllers: [PayoneerPayInController, PrivatePayInTriggerV1Controller, ProgramTokenGrpcController],
  exports: [],
})
export class PayInModule implements NestModule {
  configure(consumer: MiddlewareConsumer): MiddlewareConsumer | void {
    consumer.apply(ReqAuthMiddleware).forRoutes({ path: '/v1/payout/*', method: RequestMethod.ALL });
  }
}

import { Test, TestingModule } from '@nestjs/testing';
import { ProgramTokenService } from '../service/program-token.service';
import { ProgramCurrencyMappingRepository } from '../../common/repositories/program-currency-mapping.repository';
import { RpcException } from '@nestjs/microservices';
import { BadRequestException } from '@nestjs/common';
import { payoneer } from '@skuad/proto-utils/dist/interface/proto/payoneer/payoneer';
import { ProgramTokenGrpcController } from './private-program-token.controller';
import { BeneficiaryRegistrationPurpose } from 'src/beneficiary/dtos/inputs/beneficiary.graphql.input';
import { Status } from '@grpc/grpc-js/build/src/constants';

describe('ProgramTokenGrpcController', () => {
  let controller: ProgramTokenGrpcController;
  let programTokenService: jest.Mocked<ProgramTokenService>;
  let programsRepository: jest.Mocked<ProgramCurrencyMappingRepository>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ProgramTokenGrpcController],
      providers: [
        {
          provide: ProgramTokenService,
          useValue: {
            createAuthorizationLink: jest.fn(),
            saveAccessToken: jest.fn(),
            revokeClientAccessToken: jest.fn(),
            checkConsentStatus: jest.fn(),
          },
        },
        {
          provide: ProgramCurrencyMappingRepository,
          useValue: {
            getProgramIdUsingCurrency: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get(ProgramTokenGrpcController);
    programTokenService = module.get(ProgramTokenService);
    programsRepository = module.get(ProgramCurrencyMappingRepository);
  });

  describe('getClientAccountCodeUrl', () => {
    it('should return url', async () => {
      programTokenService.createAuthorizationLink.mockReturnValue('http://example.com');

      const input: payoneer.GetClientAccountCodeUrlInput = {
        redirectUrl: 'http://redirect.com',
        state: 'abc123',
      };

      const result = await controller.getClientAccountCodeUrl(input);
      expect(result).toEqual({ url: 'http://example.com' });
      expect(programTokenService.createAuthorizationLink).toHaveBeenCalled();
    });

    it('should throw RpcException on error', async () => {
      programTokenService.createAuthorizationLink.mockImplementation(() => {
        throw new Error('Create link failed');
      });

      await expect(
        controller.getClientAccountCodeUrl({
          redirectUrl: '',
          state: '',
        }),
      ).rejects.toThrow(RpcException);
    });
  });

  describe('generateAccessToken', () => {
    it('should throw RpcException on error', async () => {
      programTokenService.saveAccessToken.mockRejectedValue(new Error('Save failed'));

      await expect(
        controller.generateAccessToken({
          clientId: '',
          code: '',
          redirectUrl: '',
          userId: '',
        }),
      ).rejects.toThrow(RpcException);
    });
  });

  describe('revokeAccessToken', () => {
    it('should return success status', async () => {
      programTokenService.revokeClientAccessToken.mockResolvedValue(undefined);

      const result = await controller.revokeAccessToken({
        clientId: 'client',
        userId: 'user',
      });

      expect(result).toEqual({ status: 'success' });
      expect(programTokenService.revokeClientAccessToken).toHaveBeenCalled();
    });

    it('should throw RpcException on error', async () => {
      programTokenService.revokeClientAccessToken.mockRejectedValue(new Error('Revoke failed'));

      await expect(
        controller.revokeAccessToken({
          clientId: '',
          userId: '',
        }),
      ).rejects.toThrow(RpcException);
    });
  });

  describe('getSkuadProgramIdOnTheBasisOfCurrencyAndProvider', () => {
    it('should return programId', async () => {
      programsRepository.getProgramIdUsingCurrency.mockResolvedValue('program123');

      const result = await controller.getSkuadProgramIdOnTheBasisOfCurrencyAndProvider({
        currency: 'USD',
        provider: 'PAYONEER',
      });

      expect(result).toEqual({ programId: 'program123' });
      expect(programsRepository.getProgramIdUsingCurrency).toHaveBeenCalled();
    });

    it('should throw BadRequestException if input is invalid', async () => {
      await expect(
        controller.getSkuadProgramIdOnTheBasisOfCurrencyAndProvider({
          currency: '',
          provider: '',
        }),
      ).rejects.toThrow(RpcException); // RpcException because controller catches BadRequestException
    });

    it('should throw RpcException on repository error', async () => {
      programsRepository.getProgramIdUsingCurrency.mockRejectedValue(new Error('DB failed'));

      await expect(
        controller.getSkuadProgramIdOnTheBasisOfCurrencyAndProvider({
          currency: 'USD',
          provider: 'PAYONEER',
        }),
      ).rejects.toThrow(RpcException);
    });
  });

  describe('checkConsentStatus', () => {
    it('should return consent status when client has given consent', async () => {
      // Arrange
      programTokenService.checkConsentStatus.mockResolvedValue(true);
      const input: payoneer.CheckConsentStatusInput = {
        clientId: 'client123',
      };

      // Act
      const result = await controller.checkConsentStatus(input);

      // Assert
      expect(result).toEqual({ status: true });
      expect(programTokenService.checkConsentStatus).toHaveBeenCalledWith('client123');
    });

    it('should return false status when client has not given consent', async () => {
      // Arrange
      programTokenService.checkConsentStatus.mockResolvedValue(false);
      const input: payoneer.CheckConsentStatusInput = {
        clientId: 'client123',
      };

      // Act
      const result = await controller.checkConsentStatus(input);

      // Assert
      expect(result).toEqual({ status: false });
      expect(programTokenService.checkConsentStatus).toHaveBeenCalledWith('client123');
    });

    it('should throw BadRequestException when clientId is missing', async () => {
      // Arrange
      const input: payoneer.CheckConsentStatusInput = {
        clientId: '',
      };

      // Act & Assert
      await expect(controller.checkConsentStatus(input)).rejects.toThrow(RpcException);
    });

    it('should throw RpcException with INTERNAL status when service throws an error', async () => {
      // Arrange
      const serviceError = new Error('Service error');
      programTokenService.checkConsentStatus.mockRejectedValue(serviceError);
      const input: payoneer.CheckConsentStatusInput = {
        clientId: 'client123',
      };

      // Act & Assert
      try {
        await controller.checkConsentStatus(input);
        fail('Expected RpcException to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(RpcException);
        expect(error.getError().code).toBe(Status.INTERNAL);
        expect(error.getError().message).toContain('Error while checking consent status');
      }
    });

    it('should include error details in RpcException when service throws an error with details', async () => {
      // Arrange
      const detailedError = {
        status: 400,
        errors: {
          error_description: 'Invalid client',
          error_details: {
            code: 'INVALID_CLIENT'
          }
        },
        message: 'Client validation failed'
      };
      programTokenService.checkConsentStatus.mockRejectedValue(detailedError);
      const input: payoneer.CheckConsentStatusInput = {
        clientId: 'client123',
      };

      // Act & Assert
      try {
        await controller.checkConsentStatus(input);
        fail('Expected RpcException to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(RpcException);
        const errorObj = JSON.parse(error.getError().message);
        expect(errorObj.code).toBe('INVALID_CLIENT');
        expect(errorObj.description).toBe('Error while checking consent status');
        expect(errorObj.message).toBe('Invalid client');
      }
    });

    it('should handle null input gracefully', async () => {
      // Act & Assert
      await expect(controller.checkConsentStatus(null)).rejects.toThrow(RpcException);
    });

    it('should handle undefined clientId gracefully', async () => {
      // Arrange
      const input: payoneer.CheckConsentStatusInput = {
        clientId: undefined,
      };

      // Act & Assert
      await expect(controller.checkConsentStatus(input)).rejects.toThrow(RpcException);
    });
  });
});

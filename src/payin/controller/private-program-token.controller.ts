import { BadRequestException, Controller, Injectable, UseInterceptors } from '@nestjs/common';
import { ProgramTokenService } from '../service/program-token.service';
import { GrpcMethod, RpcException } from '@nestjs/microservices';
import { payoneer } from '@skuad/proto-utils/dist/interface/proto/payoneer/payoneer';
import { ProgramCurrencyMappingRepository } from '../../common/repositories/program-currency-mapping.repository';
import { PayoneerProvider } from 'src/common/constants/enums';
import { BeneficiaryRegistrationPurpose } from 'src/beneficiary/dtos/inputs/beneficiary.graphql.input';
import { RpcLoggingInterceptor } from '@skuad/proto-utils/dist/common/interceptor/grpc.interceptor';
import { Status } from '@grpc/grpc-js/build/src/constants';
@Controller()
@Injectable()
@UseInterceptors(RpcLoggingInterceptor)
export class ProgramTokenGrpcController {
  constructor(
    private readonly programTokenService: ProgramTokenService,
    private readonly programsRepository: ProgramCurrencyMappingRepository,
  ) {}

  @GrpcMethod('PayoneerRpcService', 'getClientAccountCodeUrl')
  async getClientAccountCodeUrl(
    getClientAccountCodeUrlInput: payoneer.GetClientAccountCodeUrlInput,
  ): Promise<payoneer.GetClientAccountCodeUrlOutput> {
    try {
      const url = this.programTokenService.createAuthorizationLink(
        getClientAccountCodeUrlInput.redirectUrl,
        getClientAccountCodeUrlInput.purpose as unknown as BeneficiaryRegistrationPurpose,
        getClientAccountCodeUrlInput.state,
      );
      return { url };
    } catch (error) {
      throw new RpcException(error?.message);
    }
  }

  @GrpcMethod('PayoneerRpcService', 'generateAccessToken')
  async generateAccessToken(
    generateAccessTokenInput: payoneer.GenerateAccessTokenInput,
  ): Promise<payoneer.GenerateAccessTokenOutput> {
    try {
      const accessTokenData = await this.programTokenService.saveAccessToken(
        generateAccessTokenInput.clientId,
        generateAccessTokenInput.code,
        generateAccessTokenInput.redirectUrl,
        generateAccessTokenInput.userId,
      );

      return accessTokenData;
    } catch (error) {
      throw new RpcException(error?.message);
    }
  }

  @GrpcMethod('PayoneerRpcService', 'revokeAccessToken')
  async revokeAccessToken(
    generateAccessTokenInput: payoneer.RevokeAccessTokenInput,
  ): Promise<payoneer.RevokeAccessTokenOutput> {
    try {
      await this.programTokenService.revokeClientAccessToken(
        generateAccessTokenInput.clientId,
        generateAccessTokenInput.userId,
      );

      return {
        status: 'success',
      };
    } catch (error) {
      throw new RpcException(error?.message);
    }
  }

  @GrpcMethod('PayoneerRpcService', 'getSkuadProgramIdOnTheBasisOfCurrencyAndProvider')
  async getSkuadProgramIdOnTheBasisOfCurrencyAndProvider(
    getSkuadProgramIdInput: payoneer.GetSkuadProgramIdInput,
  ): Promise<payoneer.GetSkuadProgramIdOutput> {
    try {
      if (!getSkuadProgramIdInput.currency || !getSkuadProgramIdInput.provider) {
        throw new BadRequestException('Currency and Provider are required');
      }

      const programId = await this.programsRepository.getProgramIdUsingCurrency(
        getSkuadProgramIdInput.currency,
        getSkuadProgramIdInput.provider as PayoneerProvider,
      );

      return { programId };
    } catch (error) {
      throw new RpcException(error?.message);
    }
  }

  @GrpcMethod('PayoneerRpcService', 'checkConsentStatus')
  async checkConsentStatus(
    checkConsentStatusInput: payoneer.CheckConsentStatusInput,
  ): Promise<payoneer.CheckConsentStatusOutput> {
    try {
      if (!checkConsentStatusInput.clientId) {
        throw new BadRequestException('Client id is required');
      }

      const status = await this.programTokenService.checkConsentStatus(checkConsentStatusInput.clientId);

      return { status };
    } catch (error) {
      throw new RpcException({
        code: Status.INTERNAL,
        message: JSON.stringify({
          code: error?.errors?.error_details?.code || error?.status,
          description: 'Error while checking consent status',
          message: error?.errors?.error_description || error?.message,
          error: error,
        }),
      });
    }
  }
}

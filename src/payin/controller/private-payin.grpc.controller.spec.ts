import { Test, TestingModule } from '@nestjs/testing';
import { PayInProcessorService } from '../service/payin-processor.service';
import { PayoneerAccountService } from '../service/payoneer-account.service';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { Currency } from '../../common/constants/enums';
import { PayoneerAccountEntity } from '../../common/entities/account.entitiy';
import { PayoneerPayInController } from './private-payin.grpc.controller';
import { RpcException } from '@nestjs/microservices';

describe('PayoneerPayInController', () => {
  let controller: PayoneerPayInController;
  let payInProcessorService: jest.Mocked<PayInProcessorService>;
  let payoneerAccountService: jest.Mocked<PayoneerAccountService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PayoneerPayInController],
      providers: [
        {
          provide: PayInProcessorService,
          useValue: {
            createSubAccount: jest.fn(),
            fetchFundingAccount: jest.fn(),
          },
        },
        {
          provide: PayoneerAccountService,
          useValue: {
            getTransactions: jest.fn(),
            getAccountBalance: jest.fn(),
            getPaymentStatus: jest.fn(),
            cancelPayment: jest.fn(),
            getMerchantChargeStatus: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get(PayoneerPayInController);
    payInProcessorService = module.get(PayInProcessorService);
    payoneerAccountService = module.get(PayoneerAccountService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getTransactions', () => {
    it('should call service and return transactions', async () => {
      const mockInput = { clientId: 'c' };
      const mockResult = { transactions: [] };
      payoneerAccountService.getTransactions.mockResolvedValue(mockResult as any);

      const result = await controller.getTransactions(mockInput as any);
      expect(result).toBe(mockResult);
      expect(payoneerAccountService.getTransactions).toHaveBeenCalledWith(mockInput);
    });
  });

  describe('createSubAccount', () => {
    it('should throw RpcException on service error', async () => {
      payInProcessorService.createSubAccount.mockResolvedValue([null, { message: 'error', name: 'error' }]);

      const input = { clientId: 'c', currency: 'USD' };

      await expect(controller.createSubAccount(input as any)).rejects.toThrow(RpcException);
    });
  });

  describe('getFundingAccounts', () => {
    it('should return funding account', async () => {
      const account: PayoneerAccountEntity = {
        id: '1',
        providerAccountId: 'pa',
        accountNumber: '123',
        accountName: 'Test',
        bankName: 'Bank',
        bankAddress: 'Address',
        bankCountry: 'US',
        currency: 'USD',
        routingCode: 'RC',
        routingCodeType: 'ABA',
      } as any;

      payInProcessorService.fetchFundingAccount.mockResolvedValue(account);

      const input = { customerId: 'cust' };
      const result = await controller.getFundingAccounts(input as any);

      expect(result.fundingAccounts.length).toBe(1);
      expect(result.fundingAccounts[0].id).toBe(account.id);
    });

    it('should throw BadRequestException if no customerId', async () => {
      await expect(controller.getFundingAccounts({} as any)).rejects.toThrow(RpcException);
    });

    it('should throw NotFoundException if no account', async () => {
      payInProcessorService.fetchFundingAccount.mockResolvedValue(null);

      await expect(controller.getFundingAccounts({ customerId: 'cust' } as any)).rejects.toThrow(
        RpcException,
      );
    });
  });

  describe('getAccountBalance', () => {
    it('should return balance', async () => {
      payoneerAccountService.getAccountBalance.mockResolvedValue({ balance: 100 } as any);

      const result = await controller.getAccountBalance({ clientId: 'c' } as any);
      expect(result).toEqual({ balance: 100 });
    });

    it('should throw RpcException if no clientId', async () => {
      await expect(controller.getAccountBalance({} as any)).rejects.toThrow(RpcException);
    });
  });

  describe('getPaymentStatus', () => {
    it('should throw RpcException if input invalid', async () => {
      await expect(controller.getPaymentStatus({} as any)).rejects.toThrow(RpcException);
    });
  });

  describe('cancelPayment', () => {
    it('should throw RpcException if input invalid', async () => {
      await expect(controller.cancelPayment({ clientId: 'c' } as any)).rejects.toThrow(RpcException);
    });
  });

  describe('getMerchantChargeStatus', () => {
    it('should throw RpcException if input invalid', async () => {
      await expect(controller.getMerchantChargeStatus({ clientId: 'c' } as any)).rejects.toThrow(
        RpcException,
      );
    });
  });

  describe('chargeAccountByClientDebit', () => {
    it('should throw service not implemented', async () => {
      await expect(controller.chargeAccountByClientDebit({} as any)).rejects.toThrow(RpcException);
    });
  });

  describe('chargeAccountByClientPartnerDebit', () => {
    it('should throw service not implemented', async () => {
      await expect(controller.chargeAccountByClientPartnerDebit({} as any)).rejects.toThrow(RpcException);
    });
  });
});

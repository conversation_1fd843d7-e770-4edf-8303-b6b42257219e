import { Controller, Post, Version } from '@nestjs/common';
import { ProgramTokenService } from '../service/program-token.service';
import { Messages } from '../../common/messages/messages';

@Controller('payin')
export class PrivatePayInTriggerV1Controller {
  constructor(private readonly programTokenService: ProgramTokenService) {}

  @Post('trigger/refresh-expiring-tokens')
  @Version('1')
  async triggerRefreshExpiringTokens() {
    await this.programTokenService.refreshTokenToBeExpired();
    return { message: Messages.REFRESH_TOKENS_PROCESS_STARTED };
  }

  @Post('trigger/populate-funding-accounts')
  @Version('1')
  async populateFundingAccounts() {
    await this.programTokenService.populateFundingAccounts();
    return { message: Messages.REFRESH_TOKENS_PROCESS_STARTED };
  }
}

import { Test, TestingModule } from '@nestjs/testing';
import { PrivatePayInTriggerV1Controller } from './private.payin.trigger.v1.controller';
import { ProgramTokenService } from '../service/program-token.service';
import { Messages } from '../../common/messages/messages';

describe('PrivatePayInTriggerV1Controller', () => {
  let controller: PrivatePayInTriggerV1Controller;
  let programTokenService: ProgramTokenService;

  // Mock ProgramTokenService
  const mockProgramTokenService = {
    refreshTokenToBeExpired: jest.fn(),
    populateFundingAccounts: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PrivatePayInTriggerV1Controller],
      providers: [
        {
          provide: ProgramTokenService,
          useValue: mockProgramTokenService,
        },
      ],
    }).compile();

    controller = module.get<PrivatePayInTriggerV1Controller>(PrivatePayInTriggerV1Controller);
    programTokenService = module.get<ProgramTokenService>(ProgramTokenService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should be defined', () => {
      expect(controller).toBeDefined();
    });

    it('should have ProgramTokenService injected', () => {
      expect(programTokenService).toBeDefined();
    });
  });

  describe('triggerRefreshExpiringTokens', () => {
    it('should call refreshTokenToBeExpired and return success message', async () => {
      // Arrange
      mockProgramTokenService.refreshTokenToBeExpired.mockResolvedValue(undefined);

      // Act
      const result = await controller.triggerRefreshExpiringTokens();

      // Assert
      expect(programTokenService.refreshTokenToBeExpired).toHaveBeenCalledTimes(1);
      expect(result).toEqual({
        message: Messages.REFRESH_TOKENS_PROCESS_STARTED,
      });
    });

    it('should throw error if refreshTokenToBeExpired fails', async () => {
      // Arrange
      const error = new Error('Service error');
      mockProgramTokenService.refreshTokenToBeExpired.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.triggerRefreshExpiringTokens()).rejects.toThrow(error);
    });
  });

  describe('populateFundingAccounts', () => {
    it('should call populateFundingAccounts and return success message', async () => {
      // Arrange
      mockProgramTokenService.populateFundingAccounts.mockResolvedValue(undefined);

      // Act
      const result = await controller.populateFundingAccounts();

      // Assert
      expect(programTokenService.populateFundingAccounts).toHaveBeenCalledTimes(1);
      expect(result).toEqual({
        message: Messages.REFRESH_TOKENS_PROCESS_STARTED,
      });
    });

    it('should throw error if populateFundingAccounts fails', async () => {
      // Arrange
      const error = new Error('Service error');
      mockProgramTokenService.populateFundingAccounts.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.populateFundingAccounts()).rejects.toThrow(error);
    });
  });
});

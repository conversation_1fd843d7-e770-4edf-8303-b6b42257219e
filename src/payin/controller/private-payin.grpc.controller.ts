import { BadRequestException, Controller, Injectable, Logger, NotFoundException, UseInterceptors } from '@nestjs/common';
import { payoneer } from '@skuad/proto-utils/dist/interface/proto/payoneer/payoneer';
import { GrpcMethod, RpcException } from '@nestjs/microservices';
import { PayInProcessorService } from '../service/payin-processor.service';
import { Currency } from '../../common/constants/enums';
import { PayoneerAccountEntity } from '../../common/entities/account.entitiy';
import { ServiceType } from 'skuad-utils-ts/dist/common/constants/enums';
import { LogAfter } from 'skuad-utils-ts/dist/common';
import { PayoneerAccountService } from '../service/payoneer-account.service';
import { RpcLoggingInterceptor } from '@skuad/proto-utils/dist/common/interceptor/grpc.interceptor';
@Controller()
@Injectable()
@UseInterceptors(RpcLoggingInterceptor)
export class PayoneerPayInController {
  private readonly logger = new Logger(PayoneerPayInController.name);

  constructor(
    private readonly payInProcessorService: PayInProcessorService,
    private readonly payoneerAccountService: PayoneerAccountService,
  ) {}

  @GrpcMethod('PayoneerRpcService', 'getTransactions')
  @LogAfter('logger', ServiceType.Grpc)
  async getTransactions(
    getTransactionsInput: payoneer.GetPayInTransactionQueryInput,
  ): Promise<payoneer.TransactionResult> {
    return await this.payoneerAccountService.getTransactions(getTransactionsInput);
  }

  @GrpcMethod('PayoneerRpcService', 'createSubAccount')
  @LogAfter('logger', ServiceType.Grpc)
  async createSubAccount(
    getCreateSubAccountInput: payoneer.CreateSubAccountInput,
  ): Promise<payoneer.CreateBeneficiaryOutput> {
    try {
      const [providerAccountId, error] = await this.payInProcessorService.createSubAccount(
        getCreateSubAccountInput?.clientId,
        getCreateSubAccountInput?.currency as unknown as Currency,
      );

      if (error) {
        throw new RpcException(error?.message);
      }
      const response: payoneer.CreateBeneficiaryOutput = {
        id: providerAccountId,
      } as payoneer.CreateBeneficiaryOutput;
      return response;
    } catch (error) {
      throw new RpcException(error?.message);
    }
  }

  @GrpcMethod('PayoneerRpcService', 'getFundingAccounts')
  @LogAfter('logger', ServiceType.Grpc)
  async getFundingAccounts(
    getFundingAccountsInput: payoneer.GetFundingAccountsInput,
  ): Promise<payoneer.GetFundingAccountsOutput> {
    try {
      if (!getFundingAccountsInput.customerId) {
        throw new BadRequestException('Customer id is required');
      }

      const account = await this.payInProcessorService.fetchFundingAccount(
        getFundingAccountsInput.customerId,
      );
      if (!account) {
        throw new NotFoundException('Account not found');
      }

      const response: payoneer.GetFundingAccountsOutput = {
        fundingAccounts: [],
      };
      const accountResponse = await this.convertFundingAccount(account);
      response.fundingAccounts.push(accountResponse);
      return response;
    } catch (error) {
      throw new RpcException(error?.message);
    }
  }

  async convertFundingAccount(account: PayoneerAccountEntity): Promise<payoneer.ReceivingAccount> {
    const response: payoneer.ReceivingAccount = {};
    response.id = account.id;
    response.accountId = account.providerAccountId;
    response.accountNumber = account.accountNumber;
    response.accountHolderName = account.accountName;
    response.bankName = account.bankName;
    response.bankAddress = account.bankAddress;
    response.bankCountry = account.bankCountry;
    response.currency = account.currency;
    response.routingCode = account.routingCode;
    response.paymentType = 'regular';
    response.routingCodeType = account.routingCodeType;
    return response;
  }

  @GrpcMethod('PayoneerRpcService', 'getAccountBalance')
  @LogAfter('logger', ServiceType.Grpc)
  async getAccountBalance(
    getAccountBalanceInput: payoneer.GetAccountBalanceRequest,
  ): Promise<payoneer.GetAccountBalanceOutput> {
    try {
      if (!getAccountBalanceInput.clientId) {
        throw new BadRequestException('ClientId is required');
      }

      const balance = await this.payoneerAccountService.getAccountBalance(getAccountBalanceInput.clientId);

      return balance;
    } catch (error) {
      this.logger.error(
        JSON.stringify({
          errorMessage: 'Error while fetching account balance',
          error,
          data: { ...(getAccountBalanceInput ?? {}) },
        }),
      );
      throw new RpcException(error?.message);
    }
  }

  @GrpcMethod('PayoneerRpcService', 'chargeAccountByClientPartnerDebit')
  @LogAfter('logger', ServiceType.Grpc)
  async chargeAccountByClientPartnerDebit(
    _chargeAccountByClientPartnerDebitInput: payoneer.ChargeAccountByClientDebitRequestExtended,
  ): Promise<payoneer.ChargeAccountByClientPartnerDebitResponse> {
    try {
      throw new Error('Service not implemented yet');
    } catch (error) {
      throw new RpcException(error?.message);
    }
  }

  @GrpcMethod('PayoneerRpcService', 'chargeAccountByClientDebit')
  @LogAfter('logger', ServiceType.Grpc)
  async chargeAccountByClientDebit(
    _chargeAccountByClientDebitInput: payoneer.ChargeAccountByClientDebitRequestExtended,
  ): Promise<payoneer.ChargeAccountByClientDebitResponse> {
    try {
      throw new Error('Service not implemented yet');
    } catch (error) {
      throw new RpcException(error?.message);
    }
  }

  @GrpcMethod('PayoneerRpcService', 'getPaymentStatus')
  @LogAfter('logger', ServiceType.Grpc)
  async getPaymentStatus(input: payoneer.GetPaymentStatusInput): Promise<payoneer.GetPaymentStatusResponse> {
    try {
      if ((!input.paymentId && !input.clientReferenceId) || !input.clientId) {
        throw new BadRequestException('Payment id or client reference id is required');
      }

      const response = await this.payoneerAccountService.getPaymentStatus(input);
      return response;
    } catch (error) {
      this.logger.error(
        JSON.stringify({
          errorMessage: 'Error while fetching payment status',
          error,
          data: { ...(input ?? {}) },
        }),
      );
      throw new RpcException(error?.message);
    }
  }

  @GrpcMethod('PayoneerRpcService', 'cancelPayment')
  @LogAfter('logger', ServiceType.Grpc)
  async cancelPayment(input: payoneer.CancelPaymentInput): Promise<payoneer.CancelPaymentOutput> {
    try {
      if (!input.clientId || !input.paymentId) {
        throw new BadRequestException('Payment id and client id is required');
      }

      return await this.payoneerAccountService.cancelPayment(input);
    } catch (error) {
      this.logger.error(
        JSON.stringify({
          errorMessage: 'Error while fetching payment status',
          error,
          data: { ...(input ?? {}) },
        }),
      );
      throw new RpcException(error?.message);
    }
  }

  @GrpcMethod('PayoneerRpcService', 'getMerchantChargeStatus')
  @LogAfter('logger', ServiceType.Grpc)
  async getMerchantChargeStatus(
    input: payoneer.GetMerchantChargeStatusInput,
  ): Promise<payoneer.GetMerchantChargeStatusOutput> {
    try {
      if (!input.clientId || !input.programId || !input.clientReferenceId) {
        throw new BadRequestException('Required input missing');
      }

      return await this.payoneerAccountService.getMerchantChargeStatus(input);
    } catch (error) {
      this.logger.error(
        JSON.stringify({
          errorMessage: 'Error while fetching payment status',
          error,
          data: { ...(input ?? {}) },
        }),
      );
      throw new RpcException(error?.message);
    }
  }
}

import { Injectable } from '@nestjs/common';
import { AppDataSource } from '../../common/database/data-source';
import { BaseRepository } from '../../common/repositories/base-repository';
import { EntityManager, FindOptionsWhere } from 'typeorm';
import { ProgramTokensEntity } from '../entities/program-token.entity';

@Injectable()
export class ProgramTokensRepository extends BaseRepository<ProgramTokensEntity> {
  constructor() {
    const target = AppDataSource.manager.getRepository(ProgramTokensEntity).target;
    super(target, AppDataSource.manager, AppDataSource.manager.queryRunner);
  }

  async updateToDatabase(
    queryArg: Record<string, any>,
    updateArg: Partial<ProgramTokensEntity>,
    session?: EntityManager,
  ) {
    if (!session) {
      return this.update(queryArg, updateArg);
    }

    return session.getRepository(ProgramTokensEntity).update(queryArg, updateArg);
  }

  async saveToDatabase(args: any, session: EntityManager = null) {
    if (session) return session.getRepository(ProgramTokensEntity).save(args);
    return this.save(args);
  }

  async getToken(where: FindOptionsWhere<ProgramTokensEntity>, session?: EntityManager) {
    if (!session) {
      return this.findOne({ where });
    }
    return session.getRepository(ProgramTokensEntity).findOne({ where });
  }

  async getTokens(where: FindOptionsWhere<ProgramTokensEntity>, session?: EntityManager) {
    if (!session) {
      return this.find({ where });
    }
    return session.getRepository(ProgramTokensEntity).find({ where });
  }
}

import { Injectable } from '@nestjs/common';
import { AppDataSource } from '../../common/database/data-source';
import { BaseRepository } from '../../common/repositories/base-repository';
import { PayoneerAccountEntity } from '../entities/account.entitiy';
import { EntityManager, IsNull, Repository } from 'typeorm';

@Injectable()
export class PayoneerAccountRepository extends BaseRepository<PayoneerAccountEntity> {
  private readonly payoneerAccountRepo: Repository<PayoneerAccountEntity>;
  constructor() {
    // Use AppDataSource to get the repository for PayoneerAccountEntity
    const payoneerAccountRepo = AppDataSource.getRepository(PayoneerAccountEntity);
    super(payoneerAccountRepo.target, payoneerAccountRepo.manager, payoneerAccountRepo.queryRunner);
    this.payoneerAccountRepo = payoneerAccountRepo;
  }

  async getSubAccountByAccountId(providerAccountId: string): Promise<PayoneerAccountEntity> {
    return this.findOne({ where: { providerAccountId } });
  }

  async getSubAccountByClientLegalEntityId(
    clientLegalEntityId: string,
    currency: string,
  ): Promise<PayoneerAccountEntity> {
    return this.findOne({ where: { clientId: clientLegalEntityId, currency } });
  }

  async fetchSubAccountWhichIsNotLinkedToClient(currency: string): Promise<PayoneerAccountEntity> {
    return this.findOne({ where: { currency, clientId: IsNull() } });
  }

  async updateToDatabase(
    queryArg: Record<string, any>,
    updateArg: Partial<PayoneerAccountEntity>,
    session: EntityManager,
  ) {
    if (!session) {
      return this.update(queryArg, updateArg);
    }

    return session.getRepository(PayoneerAccountEntity).update(queryArg, updateArg);
  }

  async saveToDatabase(args: any, session: EntityManager = null) {
    if (session) return session.getRepository(PayoneerAccountEntity).save(args);
    return this.save(args);
  }
}

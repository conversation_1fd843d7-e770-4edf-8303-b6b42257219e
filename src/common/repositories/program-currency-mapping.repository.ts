import { Injectable } from '@nestjs/common';
import { AppDataSource } from '../database/data-source';
import { BaseRepository } from './base-repository';
import { ProgramCurrencyMappingEntity } from '../entities/program.entity';
import { IsNull } from 'typeorm';
import { PayoneerProvider } from '../constants/enums';

@Injectable()
export class ProgramCurrencyMappingRepository extends BaseRepository<ProgramCurrencyMappingEntity> {
  constructor() {
    const target = AppDataSource.manager.getRepository(ProgramCurrencyMappingEntity).target;
    super(target, AppDataSource.manager, AppDataSource.manager.queryRunner);
  }

  async getProgramsOnTheBasisOfProvider(provider: PayoneerProvider) {
    return this.find({
      where: {
        provider,
        deletedAt: IsNull(),
      },
    });
  }

  async getProgramIdUsingCurrency(currency: string, provider: PayoneerProvider): Promise<string> {
    const program = await this.createQueryBuilder('pcm')
      .where('pcm.provider = :provider', { provider })
      .andWhere('pcm.currency IN (:...currencies)', { currencies: [currency, 'DEFAULT'] })
      .andWhere('pcm.deletedAt IS NULL')
      .orderBy("CASE WHEN pcm.currency = 'DEFAULT' THEN 1 ELSE 0 END", 'ASC')
      .addOrderBy('pcm.currency', 'ASC')
      .getOne();

    return program?.programId || null;
  }
}

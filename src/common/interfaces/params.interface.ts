import { BeneficiaryRegistrationPurpose } from 'src/beneficiary/dtos/inputs/beneficiary.graphql.input';

export enum PayeeBankAccountType {
  PERSONAL = 'Personal',
  COMPANY = 'Company',
}

export interface IParams {
  currency?: string;
  country?: string;
  payee_id?: string;
  program_id?: string;
  payeeEmail?: string;
  bank_account_type?: PayeeBankAccountType;
  client_reference_id?: string;
  payment_id?: string;
  payeeType?: string;
  start_date?: string;
  end_date?: string;
  method_id?: string;
  account_id?: string;
  page_size?: number;
  from_transaction_date?: string;
  to_transaction_date?: string;
  last_transaction_id?: string;
  category_id?: number;
  status_id?: number;
  last_transaction_date?: string;
  refresh_token?: string;
  grant_type?: string;
  include_details?: boolean;
  payee_type?: string;
  balance_id?: string;
  commit_id?: string;
  response_path?: string;
  redirectUrl?: string;
  scope?: string;
  state?: string;
  purpose?: BeneficiaryRegistrationPurpose;
}
export interface ICronConfigOptions {
  startDate?: string;
  endDate?: string;
  enabled?: boolean;
}

export interface ICronConfig {
  cronPattern?: string;
  cronName?: string;
  cronTimeZone?: string;
  cronUtcOffset?: string | number;
  cronUnrefTimeout?: boolean;
  cronOptions?: ICronConfigOptions;
}

export interface IRefreshTokenParams {
  grant_type: string;
  refresh_token: string;
}

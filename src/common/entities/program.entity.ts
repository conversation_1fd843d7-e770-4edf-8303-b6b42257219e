import { ObjectType, Field } from '@nestjs/graphql';
import { Entity, Column, Unique } from 'typeorm';
import { CommonEntity } from './common.entity';
import { DbTableName, PayoneerProvider, TokenType } from '../../common/constants/enums';

@Entity(DbTableName.ProgramCurrencyMapping)
@Unique(['provider', 'currency'])
@Unique(['programId', 'provider'])
@ObjectType('Programs')
export class ProgramCurrencyMappingEntity extends CommonEntity {
  @Field(() => String)
  @Column()
  programId: string;

  @Field(() => String)
  @Column()
  currency: string;

  @Field(() => PayoneerProvider)
  @Column({
    nullable: false,
    enum: PayoneerProvider,
    enumName: 'payoneer_provider',
  })
  provider: PayoneerProvider;
}

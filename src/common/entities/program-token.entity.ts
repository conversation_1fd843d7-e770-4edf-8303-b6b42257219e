import { ObjectType, Field } from '@nestjs/graphql';
import { Entity, Column, Unique } from 'typeorm';
import { CommonEntity } from './common.entity';
import { DbTableName, TokenType } from '../../common/constants/enums';

@Entity(DbTableName.ProgramTokens)
@ObjectType('ProgramTokens')
@Unique('unique_access_token', ['accessToken'])
@Unique('unique_client_id', ['clientId'])
export class ProgramTokensEntity extends CommonEntity {
  @Field()
  @Column()
  accessToken: string;

  @Field()
  @Column()
  consentedAt: Date;

  @Field({ nullable: true })
  @Column({ nullable: true })
  accountId: string;

  @Field()
  @Column()
  expiresAt: Date;

  @Field({ nullable: true })
  @Column({ nullable: true })
  refreshToken: string;

  @Field({ nullable: true })
  @Column({ nullable: true })
  refreshTokenExpiresAt: Date;

  @Field(() => TokenType)
  @Column({ type: 'enum', enumName: 'program_token_type', enum: TokenType })
  tokenType: TokenType;

  @Field({ nullable: true })
  @Column({ nullable: true })
  scope: string;

  @Field({ nullable: true })
  @Column({ nullable: true })
  idToken: string;

  @Field({ nullable: true })
  @Column({ nullable: true })
  clientId: string;

  @Field({ nullable: true })
  @Column({ nullable: true })
  applicationId: string;
}

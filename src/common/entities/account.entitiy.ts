import { ObjectType, Field } from '@nestjs/graphql';
import { Entity, Column, Unique, Index } from 'typeorm';
import { CommonEntity } from './common.entity';
import { DbTableName } from '../../common/constants/enums';

@Entity(DbTableName.Accounts)
@Unique('unique_external_id', ['externalId'])
@Index(['clientId', 'currency'], {
  unique: true,
  where: `client_id IS NOT NULL and deleted_at ISNULL`,
})
@ObjectType('Accounts')
export class PayoneerAccountEntity extends CommonEntity {
  @Field(() => String, { nullable: true })
  @Column({ nullable: true })
  clientId: string;

  @Field(() => String, { nullable: false })
  @Column()
  providerAccountId: string;

  @Field(() => String, { nullable: false })
  @Column()
  externalId: string;

  @Field(() => String, { nullable: false })
  @Column()
  customerId: string;

  @Field(() => String, { nullable: false })
  @Column()
  programId: string;

  @Field(() => String, { nullable: false })
  @Column()
  currency: string;

  @Field(() => String, { nullable: false })
  @Column()
  accountName: string;

  @Field(() => String, { nullable: false })
  @Column()
  accountNumber: string;

  @Field(() => String, { nullable: false })
  @Column()
  routingCode: string;

  @Field(() => String, { nullable: false })
  @Column()
  routingCodeType: string;

  @Field(() => String, { nullable: false })
  @Column()
  bankName: string;

  @Field(() => String, { nullable: false })
  @Column()
  bankAddress: string;

  @Field(() => String, { nullable: false })
  @Column()
  bankCountry: string;
}

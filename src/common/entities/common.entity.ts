import { ModelEntity } from '../../common/entity/model.entity';
import { Column, Generated, CreateDateColumn, UpdateDateColumn, DeleteDateColumn } from 'typeorm';

export class CommonEntity extends ModelEntity {
  @Column('uuid', { primary: true })
  @Generated('uuid')
  id: string;

  @CreateDateColumn()
  createdAt: Date;

  @Column({ nullable: true })
  createdBy: string;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ nullable: true })
  updatedBy: string;

  @DeleteDateColumn()
  deletedAt: Date;

  @Column({ nullable: true })
  deletedBy: string;
}

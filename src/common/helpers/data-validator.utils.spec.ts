import * as classValidator from 'class-validator';
import { ValidationError } from 'class-validator/types/validation/ValidationError';
import { DataValidatorUtils } from './data-validator.utils';

jest.mock('class-validator', () => ({
  ...jest.requireActual('class-validator'),
  validateSync: jest.fn(),
}));

const mockValidateSync = classValidator.validateSync as jest.Mock;

describe('DataValidatorUtils', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return empty array if no validation errors', () => {
    mockValidateSync.mockReturnValueOnce([]);
    const result = DataValidatorUtils.validate({} as any);
    expect(result).toEqual([]);
    expect(mockValidateSync).toHaveBeenCalled();
  });

  it('should return parent-level validation errors', () => {
    const error: ValidationError = {
      property: 'name',
      value: '',
      constraints: { isNotEmpty: 'name should not be empty' },
      children: [],
    };

    mockValidateSync.mockReturnValueOnce([error]);

    const result = DataValidatorUtils.validate({} as any);
    expect(result).toHaveLength(1);
    expect(result[0]).toEqual({
      key: 'name',
      val: '',
      errors: ['name should not be empty'],
    });
  });

  it('should return child-level validation errors', () => {
    const childError: ValidationError = {
      property: 'city',
      value: '',
      constraints: { isNotEmpty: 'city should not be empty' },
      children: [],
    };

    const parentError: ValidationError = {
      property: 'address',
      value: {},
      constraints: undefined,
      children: [childError],
    };

    mockValidateSync.mockReturnValueOnce([parentError]);

    const result = DataValidatorUtils.validate({} as any);
    expect(result).toHaveLength(1);
    expect(result[0]).toEqual({
      key: 'address.city',
      val: '',
      errors: ['city should not be empty'],
    });
  });

  it('should return both parent and child validation errors', () => {
    const parentError: ValidationError = {
      property: 'email',
      value: '',
      constraints: { isEmail: 'email must be a valid email' },
      children: [],
    };

    const childError: ValidationError = {
      property: 'zip',
      value: '',
      constraints: { isPostalCode: 'zip must be postal code' },
      children: [],
    };

    const addressError: ValidationError = {
      property: 'address',
      value: {},
      constraints: undefined,
      children: [childError],
    };

    mockValidateSync.mockReturnValueOnce([parentError, addressError]);

    const result = DataValidatorUtils.validate({} as any);
    expect(result).toHaveLength(2);
    expect(result).toEqual([
      {
        key: 'email',
        val: '',
        errors: ['email must be a valid email'],
      },
      {
        key: 'address.zip',
        val: '',
        errors: ['zip must be postal code'],
      },
    ]);
  });

  it('makeError should format key and extract errors', () => {
    const error: ValidationError = {
      property: 'country',
      value: 'India',
      constraints: {
        isAlpha: 'country must only contain letters',
        minLength: 'country must be at least 3 characters',
      },
      children: [],
    };

    const result = (DataValidatorUtils as any).makeError(error);
    expect(result).toEqual({
      key: 'country',
      val: 'India',
      errors: ['country must only contain letters', 'country must be at least 3 characters'],
    });

    const nested = (DataValidatorUtils as any).makeError(error, 'address');
    expect(nested.key).toBe('address.country');
  });

  it('isParentErrorExists should return true if constraints exist', () => {
    const result = (DataValidatorUtils as any).isParentErrorExists({
      constraints: { isNotEmpty: 'field required' },
    });
    expect(result).toBe(true);
  });

  it('isChildErrorExists should return true if children are not empty', () => {
    const result = (DataValidatorUtils as any).isChildErrorExists({
      children: [{ property: 'zip' }],
    });
    expect(result).toBe(true);
  });
});

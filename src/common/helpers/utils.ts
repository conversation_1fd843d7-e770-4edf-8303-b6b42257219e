import * as _ from 'underscore';
import { plainToClass } from 'class-transformer';
import { AppEnv, PaymentProvider } from '../constants/enums';
const { execSync } = require('child_process');

export const removeEmptyFromJSON = (jsonObj: Record<string, any>) => {
  Object.entries(jsonObj).forEach(
    ([key, val]) =>
      (val && typeof val === 'object' && removeEmptyFromJSON(val)) ||
      ((val === null || val === undefined || val === '') && delete jsonObj[key]),
  );
  return jsonObj;
};

export const getPaymentProvider = () => {
  return PaymentProvider.Payoneer;
};

export const toDto = <T>(classConstructor: any, obj: Record<string, any>) => {
  return plainToClass<T, typeof obj>(classConstructor, obj, {
    excludeExtraneousValues: true,
  });
};

export function decodeFromBase64(base64Str: string) {
  return Buffer.from(base64Str, 'base64').toString('utf-8');
}

export const isObjectEmpty = (obj: Record<string, any>): boolean => {
  return Object.entries(obj).length === 0;
};

export const isArrayEmpty = (arr: any[]): boolean => {
  return arr.length === 0;
};

export const isEmpty = (obj: any): boolean => {
  return _.isEmpty(obj);
};

export const canSkipTest = (condition: any) => (condition ? test.skip : test);

export const getError = (e: any) => {
  // Define dynamic error message transformations
  const errorTransformations: { pattern: RegExp; replacement: string }[] = [
    {
      pattern: /payee\s+is\s+not\s+active/i,
      replacement:
        "Your KYC verification was unsuccessful. A help request has already been created for this issue. You can view the details by clicking on 'View Help Request'.",
    },
    {
      pattern: /payee\s+is\s+inactive/i,
      replacement:
        "Your KYC verification was unsuccessful. A help request has already been created for this issue. You can view the details by clicking on 'View Help Request'.",
    },
  ];

  if (Array.isArray(e?.errors?.error_details?.errors)) {
    const errors = e.errors.error_details.errors;
    let message = errors[0]?.message ?? errors[0] ?? 'Something went wrong';
    const messageStr = String(message).toLowerCase();

    // Check for missing fields dynamically and improve message
    if (messageStr.includes('missing') || messageStr.includes('required')) {
      const parts = errors[0]?.target?.split('.');
      if (parts?.[2]) {
        const missingField = parts[2].trim();
        message = `The field '${missingField}' is required but missing. Please provide it to proceed.`;
      }
    } else {
      message = messageStr?.split('.')?.[2] ?? messageStr;
    }

    // Apply transformations dynamically
    for (const { pattern, replacement } of errorTransformations) {
      if (pattern.test(messageStr)) {
        return replacement;
      }
    }

    return message;
  }

  const message = e?.error?.error_description ?? e?.message ?? e;

  // Apply transformations dynamically
  for (const { pattern, replacement } of errorTransformations) {
    if (pattern.test(message)) {
      return replacement;
    }
  }

  return message;
};

export const convertToMap = <T>(
  emptyMap: Map<string, T>,
  object: Record<string, any>,
  keys: string[],
): Map<string, T> | undefined => {
  if (!keys.length) return emptyMap;

  const key = keys.shift();

  if (key) {
    let value = object[key];

    if (typeof value === 'object') {
      value = convertToMap(new Map(), value, Object.keys(value));
    }
    emptyMap.set(key, value);
    return convertToMap(emptyMap, object, keys);
  }
};

export const verifyPostalCode = (regex: string[] | string, postalCode: string): boolean => {
  let isPostalCodeValid = false;
  const convertedPostalCode = postalCode.toString();
  if (Array.isArray(<string[]>regex)) {
    for (const rx of regex) {
      if (RegExp(rx).exec(convertedPostalCode) !== null) {
        isPostalCodeValid = true;
        break;
      }
    }
  } else {
    isPostalCodeValid = RegExp(<string>regex).exec(convertedPostalCode) !== null;
  }
  return isPostalCodeValid;
};

export function toCamelCase(str: string): string {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
}

// Recursive function to convert all keys of an object from snake_case to camelCase
export function convertToCamelCase(obj: any): any {
  if (Array.isArray(obj)) {
    return obj.map((item) => convertToCamelCase(item));
  } else if (obj !== null && typeof obj === 'object') {
    return Object.keys(obj).reduce((acc, key) => {
      const camelCaseKey = toCamelCase(key);
      acc[camelCaseKey] = convertToCamelCase(obj[key]);
      return acc;
    }, {} as any);
  } else {
    return obj;
  }
}

export function getRoutingCodeType(routingNumber: string): string | undefined {
  // Remove spaces and any non-alphanumeric characters for validation
  const cleanedRoutingNumber = routingNumber.replace(/[^a-zA-Z0-9]/g, '').trim();

  // Check for SWIFT/BIC codes: Typically 8 or 11 characters long
  if (/^[A-Za-z]{6}[0-9A-Za-z]{2}([0-9A-Za-z]{3})?$/.test(cleanedRoutingNumber)) {
    return 'bic_swift';
  }

  if (cleanedRoutingNumber.length === 9) {
    return 'ach_routing_number'; // For ACH routing numbers (US)
  } else if (cleanedRoutingNumber.length === 6) {
    return 'sort_code'; // For UK sort codes
  } else if (cleanedRoutingNumber.length === 8) {
    return 'wire_routing_number'; // Example for wire routing numbers
  } else {
    return undefined; // Unknown routing code type
  }
}

export function extractCountryFromAddress(address: string): string | null {
  // Define patterns or keywords to detect countries in bank addresses
  const addressCountryMap = [
    { keyword: 'GA', country: 'US' }, // Georgia, USA
    { keyword: 'Commerce', country: 'US' }, // Commerce city in the USA
    { keyword: 'UK', country: 'GB' }, // UK abbreviation
    { keyword: 'United Kingdom', country: 'GB' },
    { keyword: 'England', country: 'GB' },
    // Add more address patterns as needed
  ];

  // Loop through known address patterns to detect the country
  for (const mapping of addressCountryMap) {
    if (address.includes(mapping.keyword)) {
      return mapping.country;
    }
  }

  return null;
}
export const runMigrations = () => {
  try {
    let appEnv = process.env.ENV as AppEnv;
    if (AppEnv.Local !== appEnv) {
      execSync('npm run run:migration');
      console.log('Migrations ran successfully');
    }
  } catch (error) {
    console.error(`Migrations Error: ${error}`);
    process.exit(1);
  }
};

export function validateAreObjectsEqual(
  obj1: Record<string, any> | null | undefined,
  obj2: Record<string, any> | null | undefined,
): boolean | null {
  if (!obj1 && !obj2) {
    return true;
  }

  if (!obj1 || !obj2) {
    const nonNullObj = obj1 ?? obj2;
    const allValuesNull = Object.values(nonNullObj).every((value) => !value);
    return allValuesNull;
  }

  return null;
}

export function getFilteredKeys(obj: Record<string, any> | null | undefined): string[] {
  const response = Object.keys(obj)?.filter((key) => obj[key] !== undefined && obj[key] !== null);

  return response ?? [];
}

export function validateFilteredKeys(
  filteredKeys1: string[],
  filteredKeys2: string[],
  obj2: Record<string, any> | null | undefined,
): boolean | null {
  if (filteredKeys1.length !== filteredKeys2.length) {
    return false;
  }

  for (const key of filteredKeys1) {
    if (!(key in obj2) || obj2[key] === undefined || obj2[key] === null) {
      return false;
    }
  }

  return null;
}

export function validateObjectValues(
  filteredKeys1: string[],
  obj1: Record<string, any> | null | undefined,
  obj2: Record<string, any> | null | undefined,
): boolean {
  for (const key of filteredKeys1) {
    const value1 = obj1[key];
    const value2 = obj2[key];

    if (value1 !== value2) {
      if (typeof value1 === 'object' && typeof value2 === 'object') {
        if (!areObjectsEqual(value1, value2)) {
          return false;
        }
      } else {
        return false;
      }
    }
  }
}

/**
 * Compares two objects recursively to check if they are equal.
 * @param obj1 The first object to compare
 * @param obj2 The second object to compare
 * @returns True if the objects are equal, otherwise false
 */

export function areObjectsEqual(
  obj1: Record<string, any> | null | undefined,
  obj2: Record<string, any> | null | undefined,
): boolean {
  let response = validateAreObjectsEqual(obj1, obj2);

  if (typeof response === 'boolean') {
    return response;
  }

  const filteredKeys1 = getFilteredKeys(obj1);
  const filteredKeys2 = getFilteredKeys(obj2);

  response = validateFilteredKeys(filteredKeys1, filteredKeys2, obj2);

  if (typeof response === 'boolean') {
    return response;
  }

  response = validateObjectValues(filteredKeys1, obj1, obj2);

  if (typeof response === 'boolean') {
    return response;
  }

  return true;
}

/**
 * Recursively searches for a key in a nested object.
 * @param obj The object to search
 * @param key The key to find
 * @returns The value if found, otherwise undefined
 */
export function findNestedKey(obj: any, key: string): any {
  if (!obj || typeof obj !== 'object') return undefined;

  if (key in obj) {
    return obj[key];
  }

  for (const k of Object.keys(obj)) {
    const result = findNestedKey(obj[k], key);
    if (result !== undefined) {
      return result;
    }
  }

  return undefined;
}

function toSnakeCase(s: string): string {
  return s.replace(/([A-Z])/g, '_$1').toLowerCase();
}

/**
 * Recursively converts all object keys from camelCase to snake_case.
 * @param obj - The input value, which may be an object, array, or primitive.
 */
export function convertObjectKeysToSnake(obj: any): any {
  if (Array.isArray(obj)) {
    return obj.map((item) => convertObjectKeysToSnake(item));
  } else if (obj !== null && typeof obj === 'object') {
    const newObj: any = {};
    Object.keys(obj).forEach((key) => {
      const snakeKey = toSnakeCase(key);
      newObj[snakeKey] = convertObjectKeysToSnake(obj[key]);
    });
    return newObj;
  }
  return obj;
}

export const parseError = (e: any): Record<string, any> => {
  Object.defineProperty(Error.prototype, 'toJSON', {
    value: function () {
      const alt: any = {};
      Object.getOwnPropertyNames(this).forEach(function (key) {
        alt[key] = this[key];
      }, this);

      return alt;
    },
    configurable: true,
    writable: true,
  });
  return JSON.parse(JSON.stringify(e));
};

export const getBankComfigKeys = (entityType: string, countryCode: any): Record<string, string> => {
  const key = `type:BANK_INFO::subtype:${entityType.toUpperCase()}::country:${countryCode.toUpperCase()}`;
  const type = 'BankInfo';

  return {
    key,
    type,
  };
};

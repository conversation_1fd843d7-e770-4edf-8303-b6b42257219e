import { validateSync } from "class-validator";
import { ValidationError } from "class-validator/types/validation/ValidationError";
import { BaseDto } from "../dtos/base.dto";
import { isArrayEmpty, isObjectEmpty } from "./utils";

export type CustomValidationError = {
  key: string;
  val: string | number | boolean;
  errors: string[];
};

export class DataValidatorUtils {
  private static errors: CustomValidationError[];

  static validate(dto: BaseDto): CustomValidationError[] {
    this.errors = [];
    const errors = validateSync(dto);
    if (errors.length > 0) {
      this.setErrors(errors);
      return this.errors;
    }

    return [];
  }

  private static setErrors(errors: ValidationError[]): void {
    for (const error of errors) {
      if (this.isParentErrorExists(error)) {
        this.errors.push(this.makeError(error));
      }

      if (this.isChildErrorExists(error)) {
        for (const childError of error.children) {
          this.errors.push(this.makeError(childError, error.property));
        }
      }
    }
  }

  private static makeError(
    error: ValidationError,
    parentKey?: string
  ): CustomValidationError {
    return {
      key: parentKey ? parentKey.concat(`.${error.property}`) : error.property,
      val: error.value,
      errors: Object.values(error.constraints),
    };
  }

  private static isParentErrorExists(error: ValidationError) {
    return error.constraints && !isObjectEmpty(error.constraints);
  }

  private static isChildErrorExists(error: ValidationError) {
    return error.children && !isArrayEmpty(error.children);
  }
}

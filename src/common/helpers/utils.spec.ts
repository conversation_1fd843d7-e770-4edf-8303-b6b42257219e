import { execSync } from 'child_process';
import { PaymentProvider } from '../constants/enums';
import {
  removeEmptyFromJSON,
  getPaymentProvider,
  toDto,
  decodeFromBase64,
  isObjectEmpty,
  isArrayEmpty,
  isEmpty,
  getError,
  convertToMap,
  verifyPostalCode,
  toCamelCase,
  convertToCamelCase,
  getRoutingCodeType,
  extractCountryFromAddress,
  areObjectsEqual,
  findNestedKey,
  validateAreObjectsEqual,
  getFilteredKeys,
  validateFilteredKeys,
  validateObjectValues,
  convertObjectKeysToSnake,
  parseError,
  runMigrations,
  canSkipTest,
  getBankConfigKeys,
} from './utils';

jest.mock('child_process', () => ({
  execSync: jest.fn(),
}));

describe('Utils', () => {
  describe('removeEmptyFromJSON', () => {
    it('should remove empty values from JSON object', () => {
      const input = {
        a: '',
        b: null as any,
        c: undefined as any,
        d: 'value',
        e: { f: null as any, g: 'value' },
      };
      const expected = {
        d: 'value',
        e: { g: 'value' },
      };
      expect(removeEmptyFromJSON(input)).toEqual(expected);
    });
  });

  describe('getPaymentProvider', () => {
    it('should return Payoneer as payment provider', () => {
      expect(getPaymentProvider()).toBe(PaymentProvider.Payoneer);
    });
  });

  describe('toDto', () => {
    class TestDto {
      test: string;
    }
    it('should transform plain object to DTO', () => {
      const obj = { test: 'value', extra: 'ignored' };
      const result = toDto<TestDto>(TestDto, obj);
      expect(result).toBeInstanceOf(TestDto);
    });
  });

  describe('decodeFromBase64', () => {
    it('should decode base64 string correctly', () => {
      const encoded = Buffer.from('test string', 'utf-8').toString('base64');
      expect(decodeFromBase64(encoded)).toBe('test string');
    });
  });

  describe('isObjectEmpty', () => {
    it('should return true for empty object', () => {
      expect(isObjectEmpty({})).toBe(true);
    });

    it('should return false for non-empty object', () => {
      expect(isObjectEmpty({ key: 'value' })).toBe(false);
    });
  });

  describe('isArrayEmpty', () => {
    it('should return true for empty array', () => {
      expect(isArrayEmpty([])).toBe(true);
    });

    it('should return false for non-empty array', () => {
      expect(isArrayEmpty([1])).toBe(false);
    });
  });

  describe('isEmpty', () => {
    it('should return true for empty values', () => {
      expect(isEmpty({})).toBe(true);
      expect(isEmpty([])).toBe(true);
      expect(isEmpty('')).toBe(true);
    });
  });

  describe('getError', () => {
    it('should format error message correctly', () => {
      const error = {
        errors: {
          error_details: {
            errors: [{ message: 'test error', target: 'a.b.field' }],
          },
        },
      };
      expect(getError(error)).toBe('test error');
    });
    it('should extract missing field from target path and generate custom required message', () => {
      const error = {
        errors: {
          error_details: {
            errors: [
              {
                message: 'Field is missing',
                target: 'beneficiary.bank_details.routing_code',
              },
            ],
          },
        },
      };

      const expectedMessage =
        "The field 'routing_code' is required but missing. Please provide it to proceed.";

      expect(getError(error)).toBe(expectedMessage);
    });

    it('should return transformed message for "payee is not active"', () => {
      const error = {
        errors: {
          error_details: {
            errors: [
              {
                message: 'Payee is not active',
              },
            ],
          },
        },
      };

      expect(getError(error)).toMatch(/KYC verification was unsuccessful/i);
    });

    it('should return transformed message for "payee is inactive"', () => {
      const error = {
        errors: {
          error_details: {
            errors: [
              {
                message: 'Payee is inactive',
              },
            ],
          },
        },
      };

      expect(getError(error)).toMatch(/KYC verification was unsuccessful/i);
    });

    it('should apply transformation to top-level error_description', () => {
      const error = {
        error: {
          error_description: 'Payee is not active',
        },
      };

      expect(getError(error)).toMatch(/KYC verification was unsuccessful/i);
    });

    it('should return raw error if no transformation applies', () => {
      const error = {
        error: {
          error_description: 'Some random error',
        },
      };

      expect(getError(error)).toBe('Some random error');
    });

    it('should return default string if message is a primitive', () => {
      expect(getError('Simple error')).toBe('Simple error');
    });
  });

  describe('verifyPostalCode', () => {
    it('should validate postal code against single regex', () => {
      expect(verifyPostalCode('^\\d{5}$', '12345')).toBe(true);
      expect(verifyPostalCode('^\\d{5}$', 'abc')).toBe(false);
    });

    it('should validate postal code against multiple regex', () => {
      const regexArray = ['^\\d{5}$', '^[A-Z]\\d[A-Z]\\s\\d[A-Z]\\d$'];
      expect(verifyPostalCode(regexArray, '12345')).toBe(true);
    });
  });

  describe('getRoutingCodeType', () => {
    it('should identify SWIFT/BIC codes', () => {
      expect(getRoutingCodeType('DEUTDEFF')).toBe('bic_swift');
    });

    it('should identify ACH routing numbers', () => {
      expect(getRoutingCodeType('*********')).toBe('ach_routing_number');
    });

    it('should identify UK sort codes (length 6)', () => {
      expect(getRoutingCodeType('123456')).toBe('sort_code');
    });

    it('should identify wire routing numbers (length 8)', () => {
      expect(getRoutingCodeType('********')).toBe('wire_routing_number');
    });

    it('should return undefined for unknown routing code types', () => {
      expect(getRoutingCodeType('123')).toBeUndefined(); // too short
      expect(getRoutingCodeType('ABCDEFGHIJKL')).toBeUndefined(); // too long and not matching SWIFT
    });
  });

  describe('extractCountryFromAddress', () => {
    it('should extract country from address', () => {
      expect(extractCountryFromAddress('123 Main St, Commerce, GA')).toBe('US');
      expect(extractCountryFromAddress('10 Downing Street, UK')).toBe('GB');
    });
  });

  describe('areObjectsEqual', () => {
    it('should compare objects correctly', () => {
      expect(areObjectsEqual({ a: 1 }, { a: 1 })).toBe(true);
      expect(areObjectsEqual({ a: 1 }, { a: 2 })).toBe(false);
      expect(areObjectsEqual(null, null)).toBe(true);
    });
  });

  describe('findNestedKey', () => {
    it('should find nested key in object', () => {
      const obj = { a: { b: { c: 'value' } } };
      expect(findNestedKey(obj, 'c')).toBe('value');
      expect(findNestedKey(obj, 'x')).toBeUndefined();
    });
  });

  describe('toCamelCase', () => {
    it('should convert snake_case string to camelCase', () => {
      expect(toCamelCase('hello_world')).toBe('helloWorld');
      expect(toCamelCase('first_name')).toBe('firstName');
      expect(toCamelCase('simple')).toBe('simple');
    });
  });

  describe('convertToCamelCase', () => {
    it('should convert object keys from snake_case to camelCase', () => {
      const input = {
        first_name: 'John',
        last_name: 'Doe',
        contact_info: {
          phone_number: '123456',
          home_address: {
            street_name: 'Main St',
          },
        },
        favorite_colors: ['dark_blue', 'light_green'],
      };

      const expected = {
        firstName: 'John',
        lastName: 'Doe',
        contactInfo: {
          phoneNumber: '123456',
          homeAddress: {
            streetName: 'Main St',
          },
        },
        favoriteColors: ['dark_blue', 'light_green'],
      };

      expect(convertToCamelCase(input)).toEqual(expected);
    });

    it('should handle null and undefined values', () => {
      expect(convertToCamelCase(null)).toBeNull();
      expect(convertToCamelCase(undefined)).toBeUndefined();
    });
  });

  describe('convertToMap', () => {
    it('should convert object to Map', () => {
      const obj = { key1: 'value1', key2: 'value2' };
      const result = convertToMap(new Map(), obj, Object.keys(obj));
      expect(result?.get('key1')).toBe('value1');
      expect(result?.get('key2')).toBe('value2');
    });

    it('should handle nested objects', () => {
      const obj = { key1: { nestedKey: 'value' } };
      const result = convertToMap(new Map(), obj, Object.keys(obj));
      expect(result?.get('key1').get('nestedKey')).toBe('value');
    });

    it('should return empty map for empty keys array', () => {
      const emptyMap = new Map();
      expect(convertToMap(emptyMap, {}, [])).toBe(emptyMap);
    });
  });

  describe('validateAreObjectsEqual', () => {
    it('should return true when both objects are null or undefined', () => {
      expect(validateAreObjectsEqual(null, null)).toBe(true);
      expect(validateAreObjectsEqual(undefined, undefined)).toBe(true);
    });

    it('should return true when one object is empty and other has all null values', () => {
      const obj1 = { a: null as any, b: null as any };
      expect(validateAreObjectsEqual(null, obj1)).toBe(true);
    });

    it('should return null for valid objects', () => {
      const obj1 = { a: 1 };
      const obj2 = { a: 1 };
      expect(validateAreObjectsEqual(obj1, obj2)).toBeNull();
    });
  });

  describe('getFilteredKeys', () => {
    it('should return filtered keys excluding null/undefined values', () => {
      const obj = {
        a: 1,
        b: null as any,
        c: undefined as any,
        d: 'test',
      };
      expect(getFilteredKeys(obj)).toEqual(['a', 'd']);
    });

    it('should throw or behave unexpectedly for null/undefined input', () => {
      expect(() => getFilteredKeys(null as any)).toThrow();
      expect(() => getFilteredKeys(undefined as any)).toThrow();
    });
  });

  describe('validateFilteredKeys', () => {
    it('should return false when key lengths dont match', () => {
      const keys1 = ['a', 'b'];
      const keys2 = ['a'];
      expect(validateFilteredKeys(keys1, keys2, { a: 1 })).toBe(false);
    });

    it('should return false when keys dont exist in obj2', () => {
      const keys1 = ['a', 'b'];
      const keys2 = ['a', 'b'];
      const obj2 = { a: 1 };
      expect(validateFilteredKeys(keys1, keys2, obj2)).toBe(false);
    });

    it('should return null when validation passes', () => {
      const keys1 = ['a'];
      const keys2 = ['a'];
      const obj2 = { a: 1 };
      expect(validateFilteredKeys(keys1, keys2, obj2)).toBeNull();
    });
  });

  describe('validateObjectValues', () => {
    it('should return false when primitive values dont match', () => {
      const obj1 = { a: 1 };
      const obj2 = { a: 2 };
      expect(validateObjectValues(['a'], obj1, obj2)).toBe(false);
    });

    it('should return false when nested objects dont match', () => {
      const obj1 = { a: { b: 1 } };
      const obj2 = { a: { b: 2 } };
      expect(validateObjectValues(['a'], obj1, obj2)).toBe(false);
    });

    it('should handle deep object comparison', () => {
      const obj1 = { a: { b: { c: 1 } } };
      const obj2 = { a: { b: { c: 1 } } };
      const result = validateObjectValues(['a'], obj1, obj2);
      expect(result).toBeUndefined();
    });
  });

  describe('convertObjectKeysToSnake', () => {
    it('should convert object keys to snake_case', () => {
      const input = {
        firstName: 'John',
        contactInfo: {
          phoneNumber: '*********0',
        },
      };

      const expected = {
        first_name: 'John',
        contact_info: {
          phone_number: '*********0',
        },
      };

      expect(convertObjectKeysToSnake(input)).toEqual(expected);
    });

    it('should handle arrays of objects', () => {
      const input = [{ firstName: 'Alice' }, { lastName: 'Smith' }];
      const expected = [{ first_name: 'Alice' }, { last_name: 'Smith' }];
      expect(convertObjectKeysToSnake(input)).toEqual(expected);
    });

    it('should return primitive values unchanged', () => {
      expect(convertObjectKeysToSnake('string')).toBe('string');
      expect(convertObjectKeysToSnake(123)).toBe(123);
      expect(convertObjectKeysToSnake(null)).toBe(null);
      expect(convertObjectKeysToSnake(undefined)).toBe(undefined);
    });

    it('should handle empty objects and arrays', () => {
      expect(convertObjectKeysToSnake({})).toEqual({});
      expect(convertObjectKeysToSnake([])).toEqual([]);
    });
  });

  describe('parseError', () => {
    it('should serialize a custom error object with additional props', () => {
      const customErr = new Error('Validation failed') as any;
      customErr.code = 'VALIDATION_ERROR';
      const result = parseError(customErr);
      expect(result).toHaveProperty('message', 'Validation failed');
      expect(result).toHaveProperty('code', 'VALIDATION_ERROR');
    });

    it('should serialize plain objects as-is', () => {
      const obj = { message: 'Network error', code: 500 };
      expect(parseError(obj)).toEqual(obj);
    });
  });

  describe('runMigrations', () => {
    const originalEnv = process.env;
    const logSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    const errorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    const exitSpy = jest.spyOn(process, 'exit').mockImplementation(() => {
      throw new Error('process.exit called'); // simulate exit
    });

    beforeEach(() => {
      jest.clearAllMocks();
      process.env = { ...originalEnv }; // clone env
    });

    afterAll(() => {
      process.env = originalEnv; // restore
    });

    it('should run migrations if ENV is not local', () => {
      process.env.ENV = 'staging';

      runMigrations();

      expect(execSync).toHaveBeenCalledWith('npm run run:migration');
      expect(logSpy).toHaveBeenCalledWith('Migrations ran successfully');
    });

    it('should NOT run migrations if ENV is local', () => {
      process.env.ENV = 'local';

      runMigrations();

      expect(execSync).not.toHaveBeenCalled();
    });

    it('should catch and log error then exit process', () => {
      process.env.ENV = 'staging';
      (execSync as jest.Mock).mockImplementation(() => {
        throw new Error('Migration failed');
      });

      expect(() => runMigrations()).toThrow('process.exit called');

      expect(errorSpy).toHaveBeenCalledWith(expect.stringContaining('Migrations Error'));
      expect(exitSpy).toHaveBeenCalledWith(1);
    });
  });

  describe('canSkipTest', () => {
    it('should return test.skip when condition is true', () => {
      const result = canSkipTest(true);
      expect(result).toBe(test.skip);
    });

    it('should return test when condition is false', () => {
      const result = canSkipTest(false);
      expect(result).toBe(test);
    });

    it('should return test when condition is falsy (e.g. null, undefined, 0)', () => {
      expect(canSkipTest(null)).toBe(test);
      expect(canSkipTest(undefined)).toBe(test);
      expect(canSkipTest(0)).toBe(test);
    });
  });

  describe('getBankConfigKeys', () => {
    it('should generate correct key and type for individual entity in US', () => {
      const result = getBankConfigKeys('individual', 'US');

      expect(result).toEqual({
        key: 'type:BANK_INFO::subtype:INDIVIDUAL::country:US',
        type: 'BankInfo',
      });
    });

    it('should generate correct key and type for company entity in UK', () => {
      const result = getBankConfigKeys('company', 'UK');

      expect(result).toEqual({
        key: 'type:BANK_INFO::subtype:COMPANY::country:UK',
        type: 'BankInfo',
      });
    });

    it('should convert entity type to uppercase', () => {
      const result = getBankConfigKeys('individual', 'us');

      expect(result.key).toContain('::subtype:INDIVIDUAL::');
    });

    it('should convert country code to uppercase', () => {
      const result = getBankConfigKeys('company', 'gb');

      expect(result.key).toContain('::country:GB');
    });

    it('should handle mixed case inputs correctly', () => {
      const result = getBankConfigKeys('InDiViDuAl', 'cA');

      expect(result).toEqual({
        key: 'type:BANK_INFO::subtype:INDIVIDUAL::country:CA',
        type: 'BankInfo',
      });
    });

    it('should handle different entity types', () => {
      const individualResult = getBankConfigKeys('individual', 'US');
      const companyResult = getBankConfigKeys('company', 'US');

      expect(individualResult.key).toContain('::subtype:INDIVIDUAL::');
      expect(companyResult.key).toContain('::subtype:COMPANY::');
      expect(individualResult.type).toBe('BankInfo');
      expect(companyResult.type).toBe('BankInfo');
    });

    it('should handle different country codes', () => {
      const usResult = getBankConfigKeys('individual', 'US');
      const ukResult = getBankConfigKeys('individual', 'UK');
      const caResult = getBankConfigKeys('individual', 'CA');
      const auResult = getBankConfigKeys('individual', 'AU');

      expect(usResult.key).toContain('::country:US');
      expect(ukResult.key).toContain('::country:UK');
      expect(caResult.key).toContain('::country:CA');
      expect(auResult.key).toContain('::country:AU');
    });

    it('should always return BankInfo as type regardless of inputs', () => {
      const result1 = getBankConfigKeys('individual', 'US');
      const result2 = getBankConfigKeys('company', 'UK');
      const result3 = getBankConfigKeys('business', 'CA');

      expect(result1.type).toBe('BankInfo');
      expect(result2.type).toBe('BankInfo');
      expect(result3.type).toBe('BankInfo');
    });

    it('should handle numeric country codes by converting to uppercase string', () => {
      const result = getBankConfigKeys('individual', 123);

      expect(result.key).toContain('::country:123');
      expect(result.type).toBe('BankInfo');
    });

    it('should handle null country code', () => {
      const result = getBankConfigKeys('individual', null);

      expect(result.key).toContain('::country:NULL');
      expect(result.type).toBe('BankInfo');
    });

    it('should handle undefined country code', () => {
      const result = getBankConfigKeys('individual', undefined);

      expect(result.key).toContain('::country:UNDEFINED');
      expect(result.type).toBe('BankInfo');
    });

    it('should handle empty string inputs', () => {
      const result = getBankConfigKeys('', '');

      expect(result).toEqual({
        key: 'type:BANK_INFO::subtype:::country:',
        type: 'BankInfo',
      });
    });

    it('should handle special characters in entity type', () => {
      const result = getBankConfigKeys('individual-business', 'US');

      expect(result.key).toContain('::subtype:INDIVIDUAL-BUSINESS::');
    });

    it('should handle special characters in country code', () => {
      const result = getBankConfigKeys('individual', 'US-CA');

      expect(result.key).toContain('::country:US-CA');
    });

    it('should maintain consistent key format structure', () => {
      const result = getBankConfigKeys('individual', 'US');

      // Check that the key follows the exact format: type:BANK_INFO::subtype:{ENTITY}::country:{COUNTRY}
      expect(result.key).toMatch(/^type:BANK_INFO::subtype:[^:]*::country:[^:]*$/);
    });

    it('should return object with exactly two properties', () => {
      const result = getBankConfigKeys('individual', 'US');

      expect(Object.keys(result)).toHaveLength(2);
      expect(result).toHaveProperty('key');
      expect(result).toHaveProperty('type');
    });

    it('should handle very long entity type and country code', () => {
      const longEntityType = 'a'.repeat(100);
      const longCountryCode = 'b'.repeat(50);

      const result = getBankConfigKeys(longEntityType, longCountryCode);

      expect(result.key).toContain(`::subtype:${longEntityType.toUpperCase()}::`);
      expect(result.key).toContain(`::country:${longCountryCode.toUpperCase()}`);
      expect(result.type).toBe('BankInfo');
    });

    it('should handle boolean country code', () => {
      const result1 = getBankConfigKeys('individual', true);
      const result2 = getBankConfigKeys('individual', false);

      expect(result1.key).toContain('::country:TRUE');
      expect(result2.key).toContain('::country:FALSE');
    });

    it('should handle object country code', () => {
      const result = getBankConfigKeys('individual', { code: 'US' });

      expect(result.key).toContain('::country:[OBJECT OBJECT]');
    });

    it('should handle array country code', () => {
      const result = getBankConfigKeys('individual', ['US', 'CA']);

      expect(result.key).toContain('::country:US,CA');
    });

    it('should be case insensitive for common entity types', () => {
      const result1 = getBankConfigKeys('individual', 'US');
      const result2 = getBankConfigKeys('INDIVIDUAL', 'US');
      const result3 = getBankConfigKeys('Individual', 'US');

      expect(result1.key).toBe(result2.key);
      expect(result2.key).toBe(result3.key);
    });

    it('should be case insensitive for common country codes', () => {
      const result1 = getBankConfigKeys('individual', 'us');
      const result2 = getBankConfigKeys('individual', 'US');
      const result3 = getBankConfigKeys('individual', 'Us');

      expect(result1.key).toBe(result2.key);
      expect(result2.key).toBe(result3.key);
    });
  });
});

import 'dotenv/config';
import { DataSourceOptions } from 'typeorm';
import { AppConfigService } from '../../config/services/app.config.service';
import { CronConfigGenerator } from '../../config/generators/cron.config.generator';

import { ConfigHelper } from '../../config/helpers/config-helper';
import { PayoneerAccountEntity } from '../entities/account.entitiy';
import { ProgramTokensEntity } from '../entities/program-token.entity';
import { ProgramCurrencyMappingEntity } from '../entities/program.entity';

const configHelper = new ConfigHelper();
const cronConfig = new CronConfigGenerator(configHelper);
const appConfig = new AppConfigService(cronConfig, configHelper);
const SnakeNamingStrategy = require('typeorm-naming-strategies').SnakeNamingStrategy;

const { host, port, username, password, database } = appConfig.getPayoneerPostgresConfig();

console.log(__dirname);

const isTsEnv = __filename.endsWith('.ts');

const prepareDatabaseConfig = (url?: string) => {
  let dbConfig: DataSourceOptions = {
    entities: [PayoneerAccountEntity, ProgramTokensEntity, ProgramCurrencyMappingEntity],
    migrations: [__dirname + '/../../' + `/database/migrations/*.ts`],
    synchronize: false,
    logging: true,
    migrationsRun: false,
    type: 'postgres',
    maxQueryExecutionTime: 2000,
    namingStrategy: new SnakeNamingStrategy(),
  };

  if (url) {
    (dbConfig as any)[url] = url;
  } else
    dbConfig = Object.assign(dbConfig, {
      host,
      port,
      username,
      password,
      database,
    });

  return dbConfig;
};

export default prepareDatabaseConfig;

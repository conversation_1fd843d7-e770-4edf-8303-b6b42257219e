import { ValidationArguments, ValidatorConstraint, ValidatorConstraintInterface } from 'class-validator';
import { UtilsGrpcClient } from '../external-service/payments/services/utils-grpc-client.service';
import { Injectable, Logger } from '@nestjs/common';
import { verifyPostalCode } from '../helpers/utils';

@ValidatorConstraint({ name: 'ValidatePostalCode' })
@Injectable()
export class ValidatePostalCode implements ValidatorConstraintInterface {
  constructor(
    protected readonly utilsGrpcService: UtilsGrpcClient,
    protected readonly logger: Logger,
  ) {}
  async validate(value: string, args: ValidationArguments) {
    const country = (args.object as Record<string, any>).country;
    const { data } = await this.utilsGrpcService.getPostalCodeValidationRegex({
      countryCode: country,
    });
    const regex = data.validationRegex;
    const isPostalCodeValid = value && !!regex ? verifyPostalCode(regex, value) : true;

    this.logger.log({
      message: 'postal code validation result',
      country,
      postal_code: value,
      regex,
      is_valid: isPostalCodeValid,
    });
    return isPostalCodeValid;
  }

  defaultMessage(): string {
    return 'postal code is not valid';
  }
}

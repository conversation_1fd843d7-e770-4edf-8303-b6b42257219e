import { ValidatorConstraint, ValidatorConstraintInterface } from 'class-validator';
import { getPaymentProvider } from '../helpers/utils';
import { PayoneerProvider } from '../constants/enums';

@ValidatorConstraint()
export class PaymentProviderValidator implements ValidatorConstraintInterface {
  validate(provider: string) {
    return [PayoneerProvider.Payoneer, PayoneerProvider.PayoneerGBT].includes(provider as PayoneerProvider);
  }

  defaultMessage(): string {
    return `Payment provider must be ${getPaymentProvider()}`;
  }
}

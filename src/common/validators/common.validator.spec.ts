import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { ValidatePostalCode } from './common.validator';
import { UtilsGrpcClient } from '../external-service/payments/services/utils-grpc-client.service';
import { ValidationArguments } from 'class-validator';

describe('ValidatePostalCode', () => {
  let validator: ValidatePostalCode;
  let utilsGrpcService: UtilsGrpcClient;
  let logger: Logger;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ValidatePostalCode,
        {
          provide: UtilsGrpcClient,
          useValue: {
            getPostalCodeValidationRegex: jest.fn(),
          },
        },
        {
          provide: Logger,
          useValue: {
            log: jest.fn(),
          },
        },
      ],
    }).compile();

    validator = module.get<ValidatePostalCode>(ValidatePostalCode);
    utilsGrpcService = module.get<UtilsGrpcClient>(UtilsGrpcClient);
    logger = module.get<Logger>(Logger);
  });

  it('should be defined', () => {
    expect(validator).toBeDefined();
  });

  describe('validate', () => {
    const mockValidationArgs: ValidationArguments = {
      object: { country: 'US' },
      value: '',
      targetName: '',
      property: '',
      constraints: [],
    };

    it('should return true when postal code matches regex', async () => {
      const mockResponse = { data: { validationRegex: '^\\d{5}(-\\d{4})?$' } };
      jest.spyOn(utilsGrpcService, 'getPostalCodeValidationRegex').mockResolvedValue(mockResponse as any);

      const result = await validator.validate('12345', mockValidationArgs);
      expect(result).toBe(true);
    });

    it('should return false when postal code does not match regex', async () => {
      const mockResponse = { data: { validationRegex: '^\\d{5}(-\\d{4})?$' } };
      jest.spyOn(utilsGrpcService, 'getPostalCodeValidationRegex').mockResolvedValue(mockResponse as any);

      const result = await validator.validate('invalid', mockValidationArgs);
      expect(result).toBe(false);
    });

    it('should return true when no regex is returned', async () => {
      const mockResponse = { data: { validationRegex: null } as any };
      jest.spyOn(utilsGrpcService, 'getPostalCodeValidationRegex').mockResolvedValue(mockResponse as any);

      const result = await validator.validate('12345', mockValidationArgs);
      expect(result).toBe(true);
    });

    it('should return true when value is empty', async () => {
      const mockResponse = { data: { validationRegex: '^\\d{5}(-\\d{4})?$' } };
      jest.spyOn(utilsGrpcService, 'getPostalCodeValidationRegex').mockResolvedValue(mockResponse as any);

      const result = await validator.validate('', mockValidationArgs);
      expect(result).toBe(true);
    });

    it('should log validation results', async () => {
      const mockResponse = { data: { validationRegex: '^\\d{5}(-\\d{4})?$' } };
      jest.spyOn(utilsGrpcService, 'getPostalCodeValidationRegex').mockResolvedValue(mockResponse as any);
      const logSpy = jest.spyOn(logger, 'log');

      await validator.validate('12345', mockValidationArgs);

      expect(logSpy).toHaveBeenCalledWith({
        message: 'postal code validation result',
        country: 'US',
        postal_code: '12345',
        regex: '^\\d{5}(-\\d{4})?$',
        is_valid: true,
      });
    });
  });

  describe('defaultMessage', () => {
    it('should return error message', () => {
      expect(validator.defaultMessage()).toBe('postal code is not valid');
    });
  });
});

import { LoggerService } from './LoggerService';
import { BaseAppError as AppError } from 'skuad-utils-ts/dist/common';

describe('LoggerService', () => {
  let service: LoggerService;
  let mockCore: any;

  beforeEach(() => {
    // Create a mock for the core service with a logger
    mockCore = {
      reInitLoggerWithContext: jest.fn(),
      logger: {
        info: jest.fn(),
        error: jest.fn(),
        warn: jest.fn(),
        debug: jest.fn(),
      },
    };

    // Instantiate the LoggerService with the mocked core service
    service = new LoggerService(mockCore);
  });

  describe('log', () => {
    it('should call reInitLoggerWithContext with provided context and then call core.logger.info with message', () => {
      const message = 'Test log message';
      const context = 'TestContext';
      service.log(message, context);
      expect(mockCore.reInitLoggerWithContext).toHaveBeenCalledWith(context);
      expect(mockCore.logger.info).toHaveBeenCalledWith(message);
    });

    it('should use service.context when no context is provided', () => {
      const message = 'Test log message without context';
      // Set a default context on the service (inherited from NestLogger)
      (service as any).context = 'DefaultContext';
      service.log(message);
      expect(mockCore.reInitLoggerWithContext).toHaveBeenCalledWith('DefaultContext');
      expect(mockCore.logger.info).toHaveBeenCalledWith(message);
    });
  });

  describe('error', () => {
    it('should call reInitLoggerWithContext with provided context and then call core.logger.error with message', () => {
      const message = 'Test error message';
      const context = 'ErrorContext';
      service.error(message, context);
      expect(mockCore.reInitLoggerWithContext).toHaveBeenCalledWith(context);
      expect(mockCore.logger.error).toHaveBeenCalledWith(message);
    });
  });

  describe('errorWithTag', () => {
    it('should NOT call AppError.createFromException if error is already an instance of AppError', () => {
      // Create an instance of AppError.
      // Depending on your implementation, you might need to adjust this.
      const appError = AppError.createFromException(new Error('App error message'));
      // Spy on the instance method getFormattedError
      const formattedError = { message: 'Formatted error message' };
      jest.spyOn(appError, 'getFormattedError').mockReturnValue(formattedError as any);

      // Spy on the static method createFromException to ensure it is NOT called
      const createFromExceptionSpy = jest.spyOn(AppError, 'createFromException');

      const tag = 'MY_TAG';
      const context = 'ErrorWithTagContext';

      service.errorWithTag(appError, tag, context);

      expect(createFromExceptionSpy).not.toHaveBeenCalled();
      expect(mockCore.reInitLoggerWithContext).toHaveBeenCalledWith(context);
      expect(mockCore.logger.error).toHaveBeenCalledWith({
        errorInfo: formattedError,
        tag,
      });
    });

    it('should convert error to AppError if error is NOT an instance of AppError and then call core.logger.error', () => {
      // Create a plain error (not an instance of AppError)
      const plainError = new Error('Plain error');
      // Create a fake AppError instance to be returned by createFromException.
      const convertedAppError = AppError.createFromException(new Error('Converted error'));
      const formattedError = { message: 'Formatted converted error' };
      jest.spyOn(convertedAppError, 'getFormattedError').mockReturnValue(formattedError as any);

      // Spy on the static method createFromException and have it return the fake AppError
      const createFromExceptionSpy = jest
        .spyOn(AppError, 'createFromException')
        .mockReturnValue(convertedAppError);

      const tag = 'CONVERSION_TAG';
      const context = 'ErrorWithTagContext';

      service.errorWithTag(plainError, tag, context);

      expect(createFromExceptionSpy).toHaveBeenCalledWith(plainError);
      expect(mockCore.reInitLoggerWithContext).toHaveBeenCalledWith(context);
      expect(mockCore.logger.error).toHaveBeenCalledWith({
        errorInfo: formattedError,
        tag,
      });
    });
  });

  describe('warn', () => {
    it('should call reInitLoggerWithContext with provided context and then call core.logger.warn with message', () => {
      const message = 'Test warn message';
      const context = 'WarnContext';
      service.warn(message, context);
      expect(mockCore.reInitLoggerWithContext).toHaveBeenCalledWith(context);
      expect(mockCore.logger.warn).toHaveBeenCalledWith(message);
    });
  });

  describe('debug', () => {
    it('should call reInitLoggerWithContext with provided context and then call core.logger.debug with message', () => {
      const message = 'Test debug message';
      const context = 'DebugContext';
      service.debug(message, context);
      expect(mockCore.reInitLoggerWithContext).toHaveBeenCalledWith(context);
      expect(mockCore.logger.debug).toHaveBeenCalledWith(message);
    });
  });

  describe('logDBQuery', () => {
    it('should call the log method internally with the provided message and context', () => {
      const message = 'Test DB query';
      const context = 'DBQueryContext';
      // Spy on the log method
      const logSpy = jest.spyOn(service, 'log');
      service.logDBQuery(message, context);
      expect(logSpy).toHaveBeenCalledWith(message, context);
    });
  });
});

import { Injectable, Logger as NestLogger } from '@nestjs/common';
import { BaseAppError as AppError } from 'skuad-utils-ts/dist/common';
import { CoreService, InjectCore } from 'skuad-utils-ts/dist/core';

@Injectable()
export class LoggerService extends NestLogger {
  constructor(
    @InjectCore(LoggerService.name)
    protected readonly core: CoreService,
  ) {
    super();
  }

  log(message: any, context?: string) {
    this.setLoggerContext(context);
    this.core.logger.info(message);
  }

  error(message: any, context?: string) {
    this.setLoggerContext(context);
    this.core.logger.error(message);
  }

  errorWithTag(error: any, tag: string, context?: string) {
    if (!(error instanceof AppError)) error = AppError.createFromException(error);

    this.setLoggerContext(context);
    this.core.logger.error({
      errorInfo: error.getFormattedError(),
      tag,
    });
  }

  warn(message: any, context?: string) {
    this.setLoggerContext(context);
    this.core.logger.warn(message);
  }

  debug(message: any, context?: string) {
    this.setLoggerContext(context);
    this.core.logger.debug(message);
  }

  logDBQuery(message: any, context?: string) {
    this.log(message, context);
  }

  private setLoggerContext(context: string): void {
    this.core.reInitLoggerWithContext(context ?? this.context);
  }
}

import { LoggerMiddleware } from './logger.middleware';
import { Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

describe('LoggerMiddleware', () => {
  let loggerMiddleware: LoggerMiddleware;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;
  let loggerSpy: jest.SpyInstance;

  beforeEach(() => {
    loggerMiddleware = new LoggerMiddleware();
    mockRequest = {
      ip: '127.0.0.1',
      method: 'GET',
      originalUrl: '/test',
      body: {},
      headers: { 'user-agent': 'jest' },
      get: jest.fn().mockReturnValue('jest'),
    };
    mockResponse = {
      statusCode: 200,
      get: jest.fn().mockReturnValue('123'),
      on: jest.fn().mockImplementation((event, callback) => {
        if (event === 'finish') {
          callback();
        }
      }),
    };
    mockNext = jest.fn();
    loggerSpy = jest.spyOn(Logger.prototype, 'log');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should log the request details', () => {
    loggerMiddleware.use(mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockResponse.on).toHaveBeenCalledWith('finish', expect.any(Function));
    expect(mockNext).toHaveBeenCalled();
    expect(loggerSpy).toHaveBeenCalledWith({
      message: expect.stringContaining('GET /test 200'),
    });
  });

  it('should log an error for status code 500', () => {
    mockResponse.statusCode = 500;
    loggerSpy = jest.spyOn(Logger.prototype, 'error');

    loggerMiddleware.use(mockRequest as Request, mockResponse as Response, mockNext);

    expect(loggerSpy).toHaveBeenCalledWith({
      message: expect.stringContaining('GET /test 500'),
    });
  });

  it('should log a warning for status code 400', () => {
    mockResponse.statusCode = 400;
    loggerSpy = jest.spyOn(Logger.prototype, 'warn');

    loggerMiddleware.use(mockRequest as Request, mockResponse as Response, mockNext);

    expect(loggerSpy).toHaveBeenCalledWith({
      message: expect.stringContaining('GET /test 400'),
    });
  });

  it('should not log IntrospectionQuery operations', () => {
    mockRequest.body = { operationName: 'IntrospectionQuery' };

    loggerMiddleware.use(mockRequest as Request, mockResponse as Response, mockNext);

    expect(loggerSpy).not.toHaveBeenCalled();
  });

  it('should log the correct message format', () => {
    const message = loggerMiddleware['getMessage'](mockRequest, mockResponse);
    expect(message).toBe('GET /test 200 - Headers: {"user-agent":"jest"} - Body: {} 123 - jest 127.0.0.1');
  });
});

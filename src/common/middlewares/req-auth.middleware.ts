import { Injectable, NestMiddleware, UnauthorizedException } from '@nestjs/common';
import { NextFunction, Request } from 'express';
import { AppConfigService } from '../../config/services/app.config.service';
import { Messages } from '../../common/constants/messages';

@Injectable()
export class ReqAuthMiddleware implements NestMiddleware {
  constructor(private readonly config: AppConfigService) {}

  public use(req: Request, res: Response, next: NextFunction): any {
    const apiKeyHeader = req.headers['x-api-key'];
    if (apiKeyHeader && apiKeyHeader === this.config.getPrivateAPIKey()) {
      next();
      return;
    }

    throw new UnauthorizedException(Messages.API_AUTHENTICATION_FAILURE);
  }
}

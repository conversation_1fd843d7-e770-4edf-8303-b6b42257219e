import { ReqAuthMiddleware } from './req-auth.middleware';
import { AppConfigService } from '../../config/services/app.config.service';
import { UnauthorizedException } from '@nestjs/common';
import { Messages } from '../../common/constants/messages';

describe('ReqAuthMiddleware', () => {
  let middleware: ReqAuthMiddleware;
  let appConfigService: AppConfigService;
  let mockRequest: any;
  let mockResponse: any;
  let mockNextFunction: jest.Mock;

  beforeEach(() => {
    appConfigService = {
      getPrivateAPIKey: jest.fn(),
    } as unknown as AppConfigService;
    middleware = new ReqAuthMiddleware(appConfigService);
    mockRequest = {
      headers: {},
    };
    mockResponse = {};
    mockNextFunction = jest.fn();
  });

  it('should call next if x-api-key header matches the private API key', () => {
    const apiKey = 'valid-api-key';
    jest.spyOn(appConfigService, 'getPrivateAPIKey').mockReturnValue(apiKey);
    mockRequest.headers['x-api-key'] = apiKey;

    middleware.use(mockRequest, mockResponse, mockNextFunction);

    expect(mockNextFunction).toHaveBeenCalled();
  });

  it('should throw UnauthorizedException if x-api-key header is missing', () => {
    jest.spyOn(appConfigService, 'getPrivateAPIKey').mockReturnValue('valid-api-key');

    expect(() => middleware.use(mockRequest, mockResponse, mockNextFunction)).toThrow(UnauthorizedException);
    expect(() => middleware.use(mockRequest, mockResponse, mockNextFunction)).toThrow(
      Messages.API_AUTHENTICATION_FAILURE,
    );
  });

  it('should throw UnauthorizedException if x-api-key header does not match the private API key', () => {
    jest.spyOn(appConfigService, 'getPrivateAPIKey').mockReturnValue('valid-api-key');
    mockRequest.headers['x-api-key'] = 'invalid-api-key';

    expect(() => middleware.use(mockRequest, mockResponse, mockNextFunction)).toThrow(UnauthorizedException);
    expect(() => middleware.use(mockRequest, mockResponse, mockNextFunction)).toThrow(
      Messages.API_AUTHENTICATION_FAILURE,
    );
  });
});

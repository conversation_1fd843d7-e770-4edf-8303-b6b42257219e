import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';
@Injectable()
export class LoggerMiddleware implements NestMiddleware {
  private readonly logger = new Logger('HTTP');

  use(request: Request, response: Response, next: NextFunction): void {
    const message = this.getMessage(request, response);
    const { body } = request;
    const { statusCode } = response;
    response.on('finish', () => {
      if (statusCode >= 500) {
        if (body.operationName !== 'IntrospectionQuery') this.logger.error({ message });
      }

      if (statusCode >= 400) {
        if (body.operationName !== 'IntrospectionQuery') this.logger.warn({ message });
      }

      if (body.operationName !== 'IntrospectionQuery') this.logger.log({ message });
    });

    next();
  }

  private getMessage(request: any, response: any): string {
    const { ip, method, originalUrl, body, headers } = request;
    const userAgent = request.get('user-agent') || '';
    const { statusCode } = response;
    const contentLength = response.get('content-length');
    return `${method} ${originalUrl} ${statusCode} - Headers: ${JSON.stringify(headers)} - Body: ${JSON.stringify(
      body,
    )} ${contentLength} - ${userAgent} ${ip}`;
  }
}

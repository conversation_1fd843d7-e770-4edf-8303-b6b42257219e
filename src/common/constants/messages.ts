import { getPayment<PERSON>rovider } from '../helpers/utils';

export enum Messages {
  CRON_SERVICE_STARTED = 'CronService started successfully',
  PAYMENT_REQUESTS_NOT_FOUND = 'payment requests are not available for processing',
  PAYMENT_REQUESTS_PROVIDER_CONFIG_NOT_FOUND = 'payment requests provider config are not available for processing',
  IDEMPOTENCY_ID_NOT_FOUND_ERROR = 'idempotency ID is required to create payment',
  API_AUTHENTICATION_FAILURE = 'You are not authorized to access this API',
  PROVIDER_CREDENTIALS_PARAMETERS_REQUIRED = 'x-geography is a required header',
  PROVIDER_CREDENTIALS_NOT_LOADED = 'Could not load provider credentials',
  PAYMENT_PROCESSOR_TRIGGERED_MANUALLY = 'manual payout trigger successfully',
  BENEFICIARY_DELETED = 'beneficiary deleted successfully',
  BANK_HASH_ID_HEADER_REQUIRED = 'x-bank-account-hash-id header is required',
  CUSTOMER_ID_HEADER_REQUIRED = 'x-customer - id header is required',
  WALLET_ID_HEADER_REQUIRED = 'x-wallet - id header is required',
  ERROR_FORMATTING_PROVIDER_BENEFICIARY_REQUEST = 'Error building provider beneficiary request',
  GRPC_SAVE_BENEFICIARY_DETAILS_ERROR = 'Error saving Beneficiary details',
  ERROR_WHILE_TRANSFORMING_BENEFICIARY_DETAILS = 'Error while transforming beneficiary details',
  ERROR_WHILE_FETCHING_REGISTRATION_FORMAT = 'Error while fetching registration format',
  ERROR_WHILE_REGISTERING_PAYEE = 'Error while registering payee',
  GRPC_UPDATE_BENEFICIARY_DETAILS_ERROR = 'Error updating Beneficiary details',
  ERROR_WHILE_FETCHING_PAYEE_DETAILS = 'Error while fetching payee details',
  ERROR_WHILE_FETCHING_BANK_CONFIG = 'Error while fetching bank config',
}

export const DynamicMessages = {
  currencyMismatchFound: (legal_entity_id: string) =>
    `payment Request and Beneficiary currency mismatch found for legal_entity_id: ${legal_entity_id} `,

  providerMismatchFound: (legal_entity_id: string) =>
    `payment Request provider is not ${getPaymentProvider()} for legal_entity_id: ${legal_entity_id}`,

  paymentReleased: (request_id: string, txnNo: string) =>
    `payment successfully released [uniqueRequestId: ${request_id}, txnNo: ${txnNo}]`,

  paymentCreatedForRequestID: (request_id: string) =>
    `payment successfully created for unique request id: ${request_id}`,

  bankAccountOrIBANValidationError: (key: string) => `${key}: account_number or iban is required`,
};

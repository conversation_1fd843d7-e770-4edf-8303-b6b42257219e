export enum PayoneerAPINames {
  CreateApplicationToken = 'CreateApplicationToken',
  RevokeApplicationToken = 'RevokeApplicationToken',
  CreateRegistrationLink = 'CreateRegistrationLink',
  DetermineRequiredBankingFields = 'DetermineRequiredBankingFields',
  GetPayeeStatus = 'GetPayeeStatus',
  SubmitMassPayout = 'SubmitMassPayout',
  GetPayoutStatus = 'GetPayoutStatus',
  CancelPayout = 'CancelPayout',
  QueryProgramBalance = 'QueryProgramBalance',
  TransferFunds = 'TransferFunds',
  CheckPayeeAccountExists = 'CheckPayeeAccountExists',
  GetTransactions = 'GetTransactions',
  RefreshAccessToken = 'RefreshAccessToken',
  GetFundingAccounts = 'GetFundingAccounts',
  ProcessWithdrawFund = 'ProcessWithdrawFund',
  GetEligibleBanks = 'GetEligibleBanks',
  SubmitCommitId = 'SubmitCommitId',

  // White label APIs
  RegisterPayee = 'RegisterPayee',
  GetRegisterPayeeFormat = 'GetRegisterPayeeFormat',
  EditProfile = 'EditProfile',
  EditTransferMethod = 'EditTransferMethod',
  GetKYC = 'GetKYC',
  KYCUploadDocuments = 'KYCUploadDocuments',
  GetPayeeDetailsExtended = 'GetPayeeDetailsExtended',
  SubmitQuestionnaires = 'SubmitQuestionnaires',

  CreateClientAccountCodeUrl = 'CreateClientAccountCodeUrl',
  GenerateAccessToken = 'GenerateAccessToken',
  RevokeAccessToken = 'RevokeAccessToken',

  GetAccountBalance = 'GetAccountBalance',
  ChargeAccountByClientDebit = 'ChargeAccountByClientDebit',
  GetPaymentStatus = 'GetPaymentStatus',
  GetMerchantChargeStatus = 'GetMerchantChargeStatus',
  Commit = 'Commit',
  GetCommitResponseAfterChallenge = 'GetCommitResponseAfterChallenge',

  ChargeAccountByClientPartnerDebit = 'ChargeAccountByClientPartnerDebit',
  CancelCharge = 'CancelCharge',
  CancelCommit = 'CancelCommit',
}

export enum PaymentProvider {
  Payoneer = 'payoneer',
  PayoneerGBT = 'payoneer_gbt',
}

export enum ContractServiceAPIName {
  PaymentRequestStatusUpdate = 'PaymentRequestStatusUpdate',
}

export enum Version {
  V1 = 'v1',
  V2 = 'v2',
  V4 = 'v4',
}

export enum HttpMethod {
  Get = 'get',
  Post = 'post',
  Put = 'put',
  Delete = 'delete',
  Patch = 'patch',
}

export enum PaymentRequestStatus {
  Initiated = 'initiated',
  Processing = 'processing',
  Rejected = 'rejected',
  Failed = 'failed',
  Completed = 'completed',
  Pending = 'pending',
}

export enum PaymentType {
  Priority = 'priority',
  Regular = 'regular',
  Internal = 'internal',
  External = 'external',
}

export enum ValidationConstraint {
  LegalEntityMatcher = 'LegalEntityMatcher',
  ProviderMatcher = 'PaymentProviderMatcher',
  PaymentCurrencyMatcher = 'PaymentCurrencyMatcher',
}

export enum SwiftChargeType {
  Shared = 'shared',
  Ours = 'ours',
}

export interface IRoutingCodes {
  type: RoutingCodes;
  value: string;
}

export enum GrpcErrorCode {
  payoutNotFound = 2306,
}

export enum Country {
  AD = 'AD',
  AE = 'AE',
  AF = 'AF',
  AG = 'AG',
  AI = 'AI',
  AL = 'AL',
  AM = 'AM',
  AO = 'AO',
  AQ = 'AQ',
  AR = 'AR',
  AS = 'AS',
  AT = 'AT',
  AU = 'AU',
  AW = 'AW',
  AX = 'AX',
  AZ = 'AZ',
  BA = 'BA',
  BB = 'BB',
  BD = 'BD',
  BE = 'BE',
  BF = 'BF',
  BG = 'BG',
  BH = 'BH',
  BI = 'BI',
  BJ = 'BJ',
  BL = 'BL',
  BM = 'BM',
  BN = 'BN',
  BO = 'BO',
  BQ = 'BQ',
  BR = 'BR',
  BS = 'BS',
  BT = 'BT',
  BV = 'BV',
  BW = 'BW',
  BY = 'BY',
  BZ = 'BZ',
  CA = 'CA',
  CC = 'CC',
  CD = 'CD',
  CF = 'CF',
  CG = 'CG',
  CH = 'CH',
  CI = 'CI',
  CK = 'CK',
  CL = 'CL',
  CM = 'CM',
  CN = 'CN',
  CO = 'CO',
  CR = 'CR',
  CU = 'CU',
  CV = 'CV',
  CW = 'CW',
  CX = 'CX',
  CY = 'CY',
  CZ = 'CZ',
  DE = 'DE',
  DJ = 'DJ',
  DK = 'DK',
  DM = 'DM',
  DO = 'DO',
  DZ = 'DZ',
  EC = 'EC',
  EE = 'EE',
  EG = 'EG',
  EH = 'EH',
  ER = 'ER',
  ES = 'ES',
  ET = 'ET',
  FI = 'FI',
  FJ = 'FJ',
  FK = 'FK',
  FM = 'FM',
  FO = 'FO',
  FR = 'FR',
  GA = 'GA',
  GB = 'GB',
  GD = 'GD',
  GE = 'GE',
  GF = 'GF',
  GG = 'GG',
  GH = 'GH',
  GI = 'GI',
  GL = 'GL',
  GM = 'GM',
  GN = 'GN',
  GP = 'GP',
  GQ = 'GQ',
  GR = 'GR',
  GS = 'GS',
  GT = 'GT',
  GU = 'GU',
  GW = 'GW',
  GY = 'GY',
  HK = 'HK',
  HM = 'HM',
  HN = 'HN',
  HR = 'HR',
  HT = 'HT',
  HU = 'HU',
  ID = 'ID',
  IE = 'IE',
  IL = 'IL',
  IM = 'IM',
  IN = 'IN',
  IO = 'IO',
  IQ = 'IQ',
  IR = 'IR',
  IS = 'IS',
  IT = 'IT',
  JE = 'JE',
  JM = 'JM',
  JO = 'JO',
  JP = 'JP',
  KE = 'KE',
  KG = 'KG',
  KH = 'KH',
  KI = 'KI',
  KM = 'KM',
  KN = 'KN',
  KP = 'KP',
  KR = 'KR',
  KW = 'KW',
  KY = 'KY',
  KZ = 'KZ',
  LA = 'LA',
  LB = 'LB',
  LC = 'LC',
  LI = 'LI',
  LK = 'LK',
  LR = 'LR',
  LS = 'LS',
  LT = 'LT',
  LU = 'LU',
  LV = 'LV',
  LY = 'LY',
  MA = 'MA',
  MC = 'MC',
  MD = 'MD',
  ME = 'ME',
  MF = 'MF',
  MG = 'MG',
  MH = 'MH',
  MK = 'MK',
  ML = 'ML',
  MM = 'MM',
  MN = 'MN',
  MO = 'MO',
  MP = 'MP',
  MQ = 'MQ',
  MR = 'MR',
  MS = 'MS',
  MT = 'MT',
  MU = 'MU',
  MV = 'MV',
  MW = 'MW',
  MX = 'MX',
  MY = 'MY',
  MZ = 'MZ',
  NA = 'NA',
  NC = 'NC',
  NE = 'NE',
  NF = 'NF',
  NG = 'NG',
  NI = 'NI',
  NL = 'NL',
  NO = 'NO',
  NP = 'NP',
  NR = 'NR',
  NU = 'NU',
  NZ = 'NZ',
  OM = 'OM',
  PA = 'PA',
  PE = 'PE',
  PF = 'PF',
  PG = 'PG',
  PH = 'PH',
  PK = 'PK',
  PL = 'PL',
  PM = 'PM',
  PN = 'PN',
  PR = 'PR',
  PS = 'PS',
  PT = 'PT',
  PW = 'PW',
  PY = 'PY',
  QA = 'QA',
  RE = 'RE',
  RO = 'RO',
  RS = 'RS',
  RU = 'RU',
  RW = 'RW',
  SA = 'SA',
  SB = 'SB',
  SC = 'SC',
  SD = 'SD',
  SE = 'SE',
  SG = 'SG',
  SH = 'SH',
  SI = 'SI',
  SJ = 'SJ',
  SK = 'SK',
  SL = 'SL',
  SM = 'SM',
  SN = 'SN',
  SO = 'SO',
  SR = 'SR',
  SS = 'SS',
  ST = 'ST',
  SV = 'SV',
  SX = 'SX',
  SY = 'SY',
  SZ = 'SZ',
  TC = 'TC',
  TD = 'TD',
  TF = 'TF',
  TG = 'TG',
  TH = 'TH',
  TJ = 'TJ',
  TK = 'TK',
  TL = 'TL',
  TM = 'TM',
  TN = 'TN',
  TO = 'TO',
  TR = 'TR',
  TT = 'TT',
  TV = 'TV',
  TW = 'TW',
  TZ = 'TZ',
  UA = 'UA',
  UG = 'UG',
  UM = 'UM',
  US = 'US',
  UY = 'UY',
  UZ = 'UZ',
  VA = 'VA',
  VC = 'VC',
  VE = 'VE',
  VG = 'VG',
  VI = 'VI',
  VN = 'VN',
  VU = 'VU',
  WF = 'WF',
  WS = 'WS',
  YE = 'YE',
  YT = 'YT',
  ZA = 'ZA',
  ZM = 'ZM',
  ZW = 'ZW',
}

export enum Currency {
  AED = 'AED',
  AFN = 'AFN',
  ALL = 'ALL',
  AMD = 'AMD',
  ANG = 'ANG',
  AOA = 'AOA',
  ARS = 'ARS',
  AUD = 'AUD',
  AWG = 'AWG',
  AZN = 'AZN',
  BAM = 'BAM',
  BBD = 'BBD',
  BDT = 'BDT',
  BGN = 'BGN',
  BHD = 'BHD',
  BIF = 'BIF',
  BMD = 'BMD',
  BND = 'BND',
  BOB = 'BOB',
  BOV = 'BOV',
  BRL = 'BRL',
  BSD = 'BSD',
  BTN = 'BTN',
  BWP = 'BWP',
  BYN = 'BYN',
  BZD = 'BZD',
  CAD = 'CAD',
  CDF = 'CDF',
  CHE = 'CHE',
  CHF = 'CHF',
  CHW = 'CHW',
  CLF = 'CLF',
  CLP = 'CLP',
  CNY = 'CNY',
  COP = 'COP',
  COU = 'COU',
  CRC = 'CRC',
  CUC = 'CUC',
  CUP = 'CUP',
  CVE = 'CVE',
  CZK = 'CZK',
  DJF = 'DJF',
  DKK = 'DKK',
  DOP = 'DOP',
  DZD = 'DZD',
  EGP = 'EGP',
  ERN = 'ERN',
  ETB = 'ETB',
  EUR = 'EUR',
  FJD = 'FJD',
  FKP = 'FKP',
  GBP = 'GBP',
  GEL = 'GEL',
  GHS = 'GHS',
  GIP = 'GIP',
  GMD = 'GMD',
  GNF = 'GNF',
  GTQ = 'GTQ',
  GYD = 'GYD',
  HKD = 'HKD',
  HNL = 'HNL',
  HRK = 'HRK',
  HTG = 'HTG',
  HUF = 'HUF',
  IDR = 'IDR',
  ILS = 'ILS',
  INR = 'INR',
  IQD = 'IQD',
  IRR = 'IRR',
  ISK = 'ISK',
  JMD = 'JMD',
  JOD = 'JOD',
  JPY = 'JPY',
  KES = 'KES',
  KGS = 'KGS',
  KHR = 'KHR',
  KMF = 'KMF',
  KPW = 'KPW',
  KRW = 'KRW',
  KWD = 'KWD',
  KYD = 'KYD',
  KZT = 'KZT',
  LAK = 'LAK',
  LBP = 'LBP',
  LKR = 'LKR',
  LRD = 'LRD',
  LSL = 'LSL',
  LYD = 'LYD',
  MAD = 'MAD',
  MDL = 'MDL',
  MGA = 'MGA',
  MKD = 'MKD',
  MMK = 'MMK',
  MNT = 'MNT',
  MOP = 'MOP',
  MRU = 'MRU',
  MUR = 'MUR',
  MVR = 'MVR',
  MWK = 'MWK',
  MXN = 'MXN',
  MXV = 'MXV',
  MYR = 'MYR',
  MZN = 'MZN',
  NAD = 'NAD',
  NGN = 'NGN',
  NIO = 'NIO',
  NOK = 'NOK',
  NPR = 'NPR',
  NZD = 'NZD',
  OMR = 'OMR',
  PAB = 'PAB',
  PEN = 'PEN',
  PGK = 'PGK',
  PHP = 'PHP',
  PKR = 'PKR',
  PLN = 'PLN',
  PYG = 'PYG',
  QAR = 'QAR',
  RON = 'RON',
  RSD = 'RSD',
  RUB = 'RUB',
  RWF = 'RWF',
  SAR = 'SAR',
  SBD = 'SBD',
  SCR = 'SCR',
  SDG = 'SDG',
  SEK = 'SEK',
  SGD = 'SGD',
  SHP = 'SHP',
  SLL = 'SLL',
  SOS = 'SOS',
  SRD = 'SRD',
  SSP = 'SSP',
  STN = 'STN',
  SVC = 'SVC',
  SYP = 'SYP',
  SZL = 'SZL',
  THB = 'THB',
  TJS = 'TJS',
  TMT = 'TMT',
  TND = 'TND',
  TOP = 'TOP',
  TRY = 'TRY',
  TTD = 'TTD',
  TWD = 'TWD',
  TZS = 'TZS',
  UAH = 'UAH',
  UGX = 'UGX',
  USD = 'USD',
  USN = 'USN',
  UYI = 'UYI',
  UYU = 'UYU',
  UYW = 'UYW',
  UZS = 'UZS',
  VED = 'VED',
  VES = 'VES',
  VND = 'VND',
  VUV = 'VUV',
  WST = 'WST',
  XAF = 'XAF',
  XAG = 'XAG',
  XAU = 'XAU',
  XBA = 'XBA',
  XBB = 'XBB',
  XBC = 'XBC',
  XBD = 'XBD',
  XCD = 'XCD',
  XDR = 'XDR',
  XOF = 'XOF',
  XPD = 'XPD',
  XPF = 'XPF',
  XPT = 'XPT',
  XSU = 'XSU',
  XTS = 'XTS',
  XUA = 'XUA',
  YER = 'YER',
  ZAR = 'ZAR',
  ZMW = 'ZMW',
  ZWL = 'ZWL',
}

export enum BeneficiaryRole {
  Contractor = 'contractor',
  Employee = 'employee',
  EOR = 'eor',
}

export enum Provider {
  Payoneer = 'payoneer',
  ContractService = 'contract_service',
}

export enum EntityType {
  Individual = 'Individual',
  Company = 'Company',
}

export enum PayoutMethod {
  Swift = 'SWIFT',
  Local = 'LOCAL',
}

export enum GrpcHttpApiPath {
  PingCheck = '/ping',
  HealthCheck = '/health/check',
  ValiatePostalCode = '/validate_postal_code',
  CreateBeneficiary = '/beneficiaries/create',
  UpdateBeneficiary = '/beneficiaries/update',
  DeleteBeneficiary = '/beneficiaries/delete',
  FindBeneficiary = '/beneficiaries/find',
  GetPaymentRequests = '/fetch/payment_requests',
  UpdatePaymentRequest = '/update/payment_request',
}

export enum GrpcServiceName {
  PayoneerRpcService = 'PayoneerRpcService',
}

export enum GrpcServiceToken {
  Beneficiary = 'BENEFICIARY_PACKAGE',
  Utils = 'UTILS_PACKAGE',
  PaymentRequest = 'PAYMENT_REQUEST_PACKAGE',
  Health = 'HEALTH_PACKAGE',
}

export enum ProtoPackage {
  PayoneerRpcService = 'payoneer.service',
}

export enum GrpcQueryType {
  UpdateBeneficiaryPayoutProfile = 'update_beneficiary_payout_profile',
  UpdateBeneficiaryWithProviderError = 'update_beneficiary_with_provider_error',
}

export enum RoutingCodes {
  IFSC_CODE = 'IFSC CODE',
  SWIFT_CODE = 'SWIFT',
  CNAPS = 'CNAPS',
  SORT_CODE = 'SORT CODE',
  BSB_CODE = 'BSB CODE',
  BANK_CODE = 'BANK CODE',
  BRANCH_CODE = 'BRANCH CODE',
  BRANCH_NAME = 'BRANCH NAME',
  ACH_CODE = 'ACH CODE',
  BRANCH_TRANSIT_NUMBER = 'BRANCH TRANSIT NUMBER',
}

export const RoutingCodesArray: string[] = [
  RoutingCodes.IFSC_CODE,
  RoutingCodes.SWIFT_CODE,
  RoutingCodes.CNAPS,
  RoutingCodes.SORT_CODE,
  RoutingCodes.BSB_CODE,
  RoutingCodes.ACH_CODE,
  RoutingCodes.BANK_CODE,
  RoutingCodes.BRANCH_CODE,
  RoutingCodes.BRANCH_NAME,
];

export enum PaymentRequestType {
  PayIn = 'payin',
  PayOut = 'payout',
}

export enum PayeeRole {
  Contractor = 'contractor',
  EOR = 'eor',
  Employee = 'employee',
}

export enum CronJobName {
  FetchPendingPayouts = 'FetchPendingPayoutsCronJob',
  RefreshExpiringToken = 'RefreshExpiringToken',
  PopulateFundingAccounts = 'PopulateFundingAccounts',
  FetchPendingPayoutsCronJobForPayoneerGBT = 'FetchPendingPayoutsCronJobForPayoneerGBT',
  AutoDebitProcessor = 'AutoDebitProcessor',
}

export enum PayoneerAPICode {
  MassPayoutSubmit = 12108,
  PayoutNotCreated = 10311,
}

export enum CreateApplicationTokenValues {
  ClientCredentials = 'client_credentials',
  Scope = 'read write',
}

export enum AuthType {
  Basic = 'Basic',
  Bearer = 'Bearer',
}

export enum LockType {
  NONE = 'NONE',
  ALL = 'ALL',
  ADDRESS = 'ADDRESS',
  NAMES = 'NAMES',
  EMAIL = 'EMAIL',
  NAME_ADDRESS_EMAIL = 'NAME_ADDRESS_EMAIL',
  DATE_OF_BIRTH = 'DATE_OF_BIRTH',
  ALL_NAMES = 'ALL_NAMES',
  ALL_NAMES_ADDRESS_EMAIL = 'ALL_NAMES_ADDRESS_EMAIL',
  ENTITY_NAME_COUNTRY_ID = 'ENTITY_NAME_COUNTRY_ID',
  COMPANY = 'COMPANY',
  NAMES_COUNTRY = 'NAMES_COUNTRY',
  ACCOUNT_TYPE_AND_COUNTRY = 'ACCOUNT_TYPE_AND_COUNTRY',
  BANK_COUNTRY = 'BANK_COUNTRY',
  ACCOUNT_TYPE_COUNTRY_ID = 'ACCOUNT_TYPE_COUNTRY_ID',
}

export enum PayeeDataMatchingType {
  ALL = 'ALL',
  ADDRESS = 'ADDRESS',
  NAMES = 'NAMES',
  EMAIL = 'EMAIL',
  NAME_ADDRESS_EMAIL = 'NAME_ADDRESS_EMAIL',
  DATE_OF_BIRTH = 'DATE_OF_BIRTH',
  ALL_NAMES = 'ALL_NAMES',
  ALL_NAMES_ADDRESS_EMAIL = 'ALL_NAMES_ADDRESS_EMAIL',
  ENTITY_NAME_COUNTRY_ID = 'ENTITY_NAME_COUNTRY_ID',
  NAMES_COUNTRY = 'NAMES_COUNTRY',
  ACCOUNT_TYPE_AND_COUNTRY = 'ACCOUNT_TYPE_AND_COUNTRY',
  COI_ID = 'COI_ID',
  BANK_COUNTRY = 'BANK_COUNTRY',
  ACCOUNT_TYPE_COUNTRY_ID = 'ACCOUNT_TYPE_COUNTRY_ID',
  NONE = 'NONE', // Default value
}

export enum PayoutMethod {
  BankTransfer = 'BankTransfer',
}

export enum BankAccountType {
  Personal = 1,
  Company = 2,
}

export enum PayeeType {
  Individual = 'INDIVIDUAL',
  Company = 'COMPANY',
}

export enum LegalType {
  Public = 'PUBLIC',
  Private = 'PRIVATE',
  SoleProprietorship = 'SOLE PROPRIETORSHIP',
  Llc = 'LLC',
  Llp = 'LLP',
  Inc = 'INC',
  Ltd = 'LTD',
  NonProfit = 'NON PROFIT',
  Trust = 'TRUST',
}

export enum IdDocumentType {
  SSN = 'SSN',
  DriverLicense = 'DriverLicense',
  ForeignId = 'ForeignId',
  Passport = 'Passport',
  EIN = 'EIN',
  CompanyRegistrationNumber = 'CompanyRegistrationNumber',
  UnifiedSocialCreditCode = 'UnifiedSocialCreditCode',
  DomesticPassport = 'DomesticPassport',
  PAN_Individual = 'PAN_Individual',
  Aadhaar = 'Aadhaar',
  PAN_Company = 'PAN_Company',
  SmartNationalIdentityNumber = 'SmartNationalIdentityNumber',
  CompanyCIN = 'CompanyCIN',
  SouthKoreaDriverLicense = 'SouthKoreaDriverLicense',
  AustralianBusinessNumber = 'AustralianBusinessNumber',
  AustralianCompanyNumber = 'AustralianCompanyNumber',
  TaxNumber_Ukraine = 'TaxNumber_Ukraine',
  TaxNumber_Brazil = 'TaxNumber_Brazil',
  TaxNumber_Italy = 'TaxNumber_Italy',
  InternalPassport = 'InternalPassport',
  SSS_Philippines = 'SSS_Philippines',
}

export enum PayoutMethodType {
  ACCOUNT = 'ACCOUNT',
  PREPAID_CARD = 'PREPAID_CARD',
  BANK = 'BANK',
  DIRECT_DEPOSIT = 'DIRECT_DEPOSIT',
  MPESA = 'MPESA',
}

export enum PaymentCategory {
  IncomingPayments = 'IncomingPayments',
  Refunds = 'Refunds',
  WithdrawalToBank = 'WithdrawalToBank',
  CardTransactions = 'CardTransactions',
  OutgoingPayments = 'OutgoingPayments',
  Fees = 'Fees',
}

export enum PaymentStatus {
  Pending = 'Pending',
  Completed = 'Completed',
  Failed = 'Failed',
  Canceled = 'Canceled',
  completed = 'completed',
  canceled = 'canceled',
}

export const PaymentCategoryMap = {
  [PaymentCategory.IncomingPayments]: 1,
  [PaymentCategory.Refunds]: 2,
  [PaymentCategory.WithdrawalToBank]: 3,
  [PaymentCategory.CardTransactions]: 4,
  [PaymentCategory.OutgoingPayments]: 5,
  [PaymentCategory.Fees]: 6,
};

export const PaymentStatusMap = {
  [PaymentStatus.Pending]: 1,
  [PaymentStatus.Completed]: 2,
  [PaymentStatus.Failed]: 3,
  [PaymentStatus.Canceled]: 4,
};

export enum DbTableName {
  Accounts = 'accounts',
  ProgramTokens = 'program_tokens',
  ProgramCurrencyMapping = 'programs_currency_mapping',
}

export enum TokenType {
  AccessToken = 'ACCESS_TOKEN',
  ApplicationToken = 'APPLICATION_TOKEN',
}

export enum BankRoutingCode {
  IFSC = 'IFSC_CODE',
  SWIFT = 'SWIFT_CODE',
  ACH_CODE = 'ACH_CODE',
  FEDWIRE = 'FEDWIRE_CODE',
  SORT_CODE = 'SORT_CODE',
  BSB_CODE = 'BSB_CODE',
  BRANCH_CODE = 'BRANCH_CODE',
  BANK_CODE = 'BANK_CODE',
  BRANCH_TRANSIT_NUMBER = 'BRANCH_TRANSIT_NUMBER',
  CNAPS = 'CNAPS_CODE',
  BIC_CODE = 'BIC_CODE',
  BIN = 'BIN',
  TIN = 'TIN',
  ID_NUMBER = 'ID_NUMBER',
}

export enum FundingAccountStatus {
  Enabled = 'Enabled',
  Disabled = 'Disabled',
}

export enum AppEnv {
  Production = 'production',
  Staging = 'staging',
  Local = 'local',
  Development = 'development',
  Test = 'test',
}

export enum PayoneerProvider {
  Payoneer = 'payoneer',
  PayoneerGBT = 'payoneer_gbt',
  PayoneerAutoDebit = 'payoneer_auto_debit',
}

export enum RoutingCode {
  IFSC_CODE = 'ifsc',
  SWIFT_CODE = 'bic_swift',
  SORT_CODE = 'sort_code',
  ACH_CODE = 'aba',
  BANK_CODE = 'bank_code',
  BRANCH_CODE = 'branch_code',
  BSB_CODE = 'bsb_code',
  INSTITUTION_NO = 'institution_no',
  CNAPS_CODE = 'cnaps',
  CLABE_CODE = 'clabe',
  BRANCH_TRANSIT_NUMBER = 'branch_transit_number',
  BIC_CODE = 'bic_code',
  BANK_NUMBER = 'bank_number',
  BIN = 'bin',
  TIN = 'tin',
  ID_NUMBER = 'id_number',
}

export enum PayoneerBankFieldNames {
  BankName = 'BankName',
  AccountName = 'AccountName',
  Swift = 'Swift',
  IBAN = 'IBAN',
  AccountNumber = 'AccountNumber',
  BSB = 'BSB',
  BIC = 'BIC',
  BankCode = 'BankCode',
  IdType = 'IdType',
  BIN = 'BIN',
  TIN = 'TIN',
  BranchName = 'BranchName',
  RoutingNumber = 'RoutingNumber',
  BranchCode = 'BranchCode',
  AccountTaxNumber = 'AccountTaxNumber',
  AccountType = 'AccountType',
  IdNumber = 'IdNumber',
  AccountNameEnglish = 'AccountNameEnglish',
  State = 'State',
  Address = 'Address',
  ProviceCode = 'ProviceCode',
  CityCode = 'CityCode',
  StorefrontURL = 'StorefrontURL',
  SortCode = 'SortCode',
  BankNumber = 'BankNumber',
  AccountHolderCitizenship = 'AccountHolderCitizenship',
  Suffix = 'Suffix',
  IncorporationNumber = 'IncorporationNumber',
  PassportNumber = 'PassportNumber',
  ForeignIDNumber = 'ForeignIDNumber',
  CNIC = 'CNIC',
  SNIC = 'SNIC',
}

export enum AccountType {
  Savings = 'SAVINGS',
  Checking = 'CHECKING',
  Current = 'CURRENT',
  Other = 'Other',
}

export const PayoneerBankFieldAccountTypeOptionsMapping: {
  [key in PayoneerBankFieldNames]?: {
    [key in Country]?: {
      [key in AccountType]?: { key?: string; value?: string };
    };
  };
} = {
  [PayoneerBankFieldNames.AccountType]: {
    // ----------------------------------
    // BR
    // ----------------------------------
    [Country.BR]: {
      [AccountType.Checking]: {
        key: 'CH (Conta corrente)',
        value: '1',
      },
      [AccountType.Savings]: {
        key: 'SA (Poupanca)',
        value: '2',
      },
      // `Current` was empty, so we copy the Checking entry.
      [AccountType.Current]: {
        key: 'CH (Conta corrente)',
        value: '1',
      },
      [AccountType.Other]: {},
    },

    // ----------------------------------
    // CA
    // ----------------------------------
    [Country.CA]: {
      [AccountType.Checking]: {
        key: 'Checking',
        value: '1',
      },
      [AccountType.Savings]: {
        key: 'Saving',
        value: '2',
      },
      // `Current` was empty, so we copy the Checking entry.
      [AccountType.Current]: {
        key: 'Checking',
        value: '1',
      },
      [AccountType.Other]: {},
    },

    // ----------------------------------
    // CL
    // ----------------------------------
    [Country.CL]: {
      [AccountType.Checking]: {
        key: '1 - checking',
        value: '1',
      },
      [AccountType.Savings]: {
        key: '2 - saving',
        value: '2',
      },
      [AccountType.Other]: {
        key: '4 - cuenta en línea/cuenta vista',
        value: '4',
      },
      // `Current` was empty, so we copy the Checking entry.
      [AccountType.Current]: {
        key: '1 - checking',
        value: '1',
      },
    },

    // ----------------------------------
    // CO
    // ----------------------------------
    [Country.CO]: {
      [AccountType.Checking]: {
        key: 'Checking (Cuenta Corriente)',
        value: '27',
      },
      [AccountType.Savings]: {
        key: 'Savings (Cuenta Ahorros)',
        value: '37',
      },
      // `Current` was empty, so we copy the Checking entry.
      [AccountType.Current]: {
        key: 'Checking (Cuenta Corriente)',
        value: '27',
      },
      [AccountType.Other]: {},
    },

    // ----------------------------------
    // EC
    // ----------------------------------
    [Country.EC]: {
      [AccountType.Checking]: {
        key: 'Checking (Cuenta Corriente)',
        value: '1',
      },
      [AccountType.Savings]: {
        key: 'Saving (Cuenta Ahorro)',
        value: '2',
      },
      // `Current` was empty, so we copy the Checking entry.
      [AccountType.Current]: {
        key: 'Checking (Cuenta Corriente)',
        value: '1',
      },
      [AccountType.Other]: {},
    },

    // ----------------------------------
    // IN
    // ----------------------------------
    [Country.IN]: {
      // Checking was empty, but we do have Current → copy Current into Checking
      [AccountType.Checking]: {
        key: 'Current',
        value: '1',
      },
      [AccountType.Savings]: {
        key: 'Savings',
        value: '2',
      },
      [AccountType.Current]: {
        key: 'Current',
        value: '1',
      },
      [AccountType.Other]: {},
    },

    // ----------------------------------
    // JM
    // ----------------------------------
    [Country.JM]: {
      [AccountType.Checking]: {
        key: 'Checking',
        value: '1',
      },
      [AccountType.Savings]: {
        key: 'Saving',
        value: '2',
      },
      // `Current` was empty, so we copy the Checking entry.
      [AccountType.Current]: {
        key: 'Checking',
        value: '1',
      },
      [AccountType.Other]: {},
    },

    // ----------------------------------
    // JP
    // ----------------------------------
    [Country.JP]: {
      // JP actually has distinct Checking & Current data in the JSON:
      [AccountType.Current]: {
        key: '1 - Futsu (普通)',
        value: '1',
      },
      [AccountType.Checking]: {
        key: '2 - Touza (当座)',
        value: '2',
      },
      [AccountType.Savings]: {
        key: '4 - Chochiku (貯蓄)',
        value: '4',
      },
      [AccountType.Other]: {},
    },

    // ----------------------------------
    // PE
    // ----------------------------------
    [Country.PE]: {
      [AccountType.Checking]: {
        key: '1 - Checking (Cuenta Corriente)',
        value: '1',
      },
      [AccountType.Savings]: {
        key: '2 - Saving (Cuenta Ahorro)',
        value: '2',
      },
      // `Current` was empty, so we copy the Checking entry.
      [AccountType.Current]: {
        key: '1 - Checking (Cuenta Corriente)',
        value: '1',
      },
      [AccountType.Other]: {},
    },

    // ----------------------------------
    // PR
    // ----------------------------------
    [Country.PR]: {
      [AccountType.Checking]: {
        key: 'C - Checking Account',
        value: 'C',
      },
      [AccountType.Savings]: {
        key: 'S -  Savings Account',
        value: 'S',
      },
      // `Current` was empty, so we copy the Checking entry.
      [AccountType.Current]: {
        key: 'C - Checking Account',
        value: 'C',
      },
      [AccountType.Other]: {},
    },

    // ----------------------------------
    // ZA
    // ----------------------------------
    [Country.ZA]: {
      [AccountType.Checking]: {
        key: '1 - checking account',
        value: '1',
      },
      [AccountType.Savings]: {
        key: '2 - savings account',
        value: '2',
      },
      // `Current` was empty, so we copy the Checking entry.
      [AccountType.Current]: {
        key: '1 - checking account',
        value: '1',
      },
      [AccountType.Other]: {},
    },

    // ----------------------------------
    // TT
    // ----------------------------------
    [Country.TT]: {
      [AccountType.Checking]: {
        key: 'Chequing account',
        value: '1',
      },
      [AccountType.Savings]: {
        key: 'Saving account',
        value: '2',
      },
      // `Current` was empty, so we copy the Checking entry.
      [AccountType.Current]: {
        key: 'Chequing account',
        value: '1',
      },
      [AccountType.Other]: {
        key: 'Loan',
        value: '3',
      },
    },

    // ----------------------------------
    // TN
    // ----------------------------------
    [Country.TN]: {
      [AccountType.Checking]: {
        key: 'Checking',
        value: '1',
      },
      [AccountType.Savings]: {
        key: 'Saving',
        value: '2',
      },
      // `Current` was empty, so we copy the Checking entry.
      [AccountType.Current]: {
        key: 'Checking',
        value: '1',
      },
      [AccountType.Other]: {},
    },

    // ----------------------------------
    // US
    // ----------------------------------
    [Country.US]: {
      [AccountType.Checking]: {
        key: 'C - Checking Account',
        value: 'C',
      },
      [AccountType.Savings]: {
        key: 'S -  Savings Account',
        value: 'S',
      },
      // `Current` was empty, so we copy the Checking entry.
      [AccountType.Current]: {
        key: 'C - Checking Account',
        value: 'C',
      },
      [AccountType.Other]: {},
    },
  },
};

export enum BankAccountTypeForPayoneerGBT {
  Personal = 'PERSONAL',
  Company = 'COMPANY',
}

export const PayoneerBankFieldAccountTypeOptionsReverseMapping: {
  [country in Country]?: {
    [value: string]: AccountType; // The key is the `optionValue` as string
  };
} = {
  // ----------------------------------
  // BR
  // ----------------------------------
  [Country.BR]: {
    '1': AccountType.Checking,
    '2': AccountType.Savings,
    // No "Other"/"Current" in original JSON
  },

  // ----------------------------------
  // CA
  // ----------------------------------
  [Country.CA]: {
    '1': AccountType.Checking,
    '2': AccountType.Savings,
  },

  // ----------------------------------
  // CL
  // ----------------------------------
  [Country.CL]: {
    '1': AccountType.Checking,
    '2': AccountType.Savings,
    '4': AccountType.Other,
  },

  // ----------------------------------
  // CO
  // ----------------------------------
  [Country.CO]: {
    '27': AccountType.Checking,
    '37': AccountType.Savings,
  },

  // ----------------------------------
  // EC
  // ----------------------------------
  [Country.EC]: {
    '1': AccountType.Checking,
    '2': AccountType.Savings,
  },

  // ----------------------------------
  // IN
  // ----------------------------------
  [Country.IN]: {
    '1': AccountType.Current,
    '2': AccountType.Savings,
  },

  // ----------------------------------
  // JM
  // ----------------------------------
  [Country.JM]: {
    '1': AccountType.Checking,
    '2': AccountType.Savings,
  },

  // ----------------------------------
  // JP
  // ----------------------------------
  [Country.JP]: {
    '1': AccountType.Current, // 1 - Futsu (普通)
    '2': AccountType.Checking, // 2 - Touza (当座)
    '4': AccountType.Savings, // 4 - Chochiku (貯蓄)
  },

  // ----------------------------------
  // PE
  // ----------------------------------
  [Country.PE]: {
    '1': AccountType.Checking,
    '2': AccountType.Savings,
  },

  // ----------------------------------
  // PR
  // ----------------------------------
  [Country.PR]: {
    C: AccountType.Checking,
    S: AccountType.Savings,
  },

  // ----------------------------------
  // ZA
  // ----------------------------------
  [Country.ZA]: {
    '1': AccountType.Checking,
    '2': AccountType.Savings,
  },

  // ----------------------------------
  // TT
  // ----------------------------------
  [Country.TT]: {
    '1': AccountType.Checking,
    '2': AccountType.Savings,
    '3': AccountType.Other, // Loan
  },

  // ----------------------------------
  // TN
  // ----------------------------------
  [Country.TN]: {
    '1': AccountType.Checking,
    '2': AccountType.Savings,
  },

  // ----------------------------------
  // US
  // ----------------------------------
  [Country.US]: {
    C: AccountType.Checking,
    S: AccountType.Savings,
  },
};

export enum AccountBalanceStatus {
  NotIssued = 'Not Issued',
  IssuedNotActivated = 'IssuedNotActivated',
  Activated = 'Activated',
  Blocked = 'Blocked',
  Cancelled = 'Cancelled',
  LostOrStolen = 'LostOrStolen',
}

export enum ChargeStatus {
  pending_commit = 'pending_commit',
  in_progress = 'in_progress',
  completed = 'completed',
  cancelled = 'cancelled',
  failed = 'failed',
  expired = 'expired',
  not_found = 'not found',
  unknown = 'unknown',
}

export enum CommitStatus {
  in_progress = 'in_progress',
  completed = 'completed',
  failed = 'failed',
  unknown = 'unknown',
  expired = 'expired',
}

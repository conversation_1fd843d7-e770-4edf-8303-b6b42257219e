export const ErrorMessages = {
  ACCESS_TOKEN_FETCH_ERROR: 'Could not fetch access token from DB',
  REQUEST_TIMEOUT_ERROR: 'Downstream server is not responding',
  API_MODE_NOT_VALID: 'API mode is not valid',
  DUPLICATE_PAYMENT_ERROR: 'Duplicate Payment found',
  GET_BENEFICIARY_FORMAT_DB_ERROR: 'Error Fetching Provider Get Beneficiary Format from DB',
  SAVE_BENEFICIARY_FORMAT_DB_ERROR: 'Error saving Provider Beneficiary Format to DB',
  GET_BENEFICIARY_FORMAT_PROVIDER_ERROR: 'Error Fetching Get Beneficiary Format from Provider',
  UPDATE_PAYOUT_DETAILS_ERROR: 'Error Updating Payout details with Provider',
  UPDATE_BENEFICIARY_PROFILE_DETAILS_ERROR: 'Error Updating Payout Beneficiary profile details with Provider',
  REGISTER_PAYEE_PROVIDER_ERROR: 'Error Creating Payout Profile with Provider',
  GRPC_SAVE_BENEFICIARY_DETAILS_ERROR: 'Error saving Beneficiary details',
  GRPC_UPDATE_BENEFICIARY_DETAILS_ERROR: 'Error Updating Beneficiary details',
  GRPC_FETCH_BANK_DETAILS_DIFFERENCE_ERROR: 'Error fetching bank details difference',
  GRPC_UPDATE_PROVIDER_ERROR: 'Error Updating Error from Provider for Beneficiary Profile',
  GET_BENEFICIARY_DETAILS_ERROR: 'Error fetching beneficiary details',
  GET_PAYOUT_DETAILS_ERROR: 'Error fetching payout details',
  INPUT_VALIDATION_ERROR: 'Validation Error, invalid data supplied',
  DEFAULT_ERROR_MESSAGE: 'Error processing request',

  PAYOUT_CREATION_FAILED: 'payout creation failed',
};

export const DynamicMessages = {
  getGRPCBeneficiaryRequestFormatError: (beneficiary_legal_id: string) =>
    `Error Fetching Provider Get Beneficiary Format from DB for beneficiary_legal_entity_id: ${beneficiary_legal_id} `,

  getBeneficiaryRequestFormatProviderError: (beneficiary_legal_id: string) =>
    `Error Fetching Provider Get Beneficiary Format from Provider for beneficiary_legal_entity_id: ${beneficiary_legal_id} `,

  saveBeneficiaryRequestFormatProviderError: (beneficiary_legal_id: string) =>
    `Error saving Provider Beneficiary Format to DB for beneficiary_legal_entity_id: ${beneficiary_legal_id} `,

  saveBeneficiaryDetailsGRPCError: (beneficiary_legal_id: string) =>
    `Error saving Beneficiary details for beneficiary_legal_entity_id: ${beneficiary_legal_id} `,

  registerPayeeProviderError: (beneficiary_legal_id: string) =>
    `Error Creating Payout Profile with Provider for beneficiary_legal_entity_id: ${beneficiary_legal_id} `,

  fetchBankDetailsDifferenceGRPCError: (beneficiary_legal_id: string) =>
    `Error fetching bank details difference for beneficiary_legal_entity_id: ${beneficiary_legal_id} `,

  updatePayoutBeneficiaryPayoutDetailsError: (beneficiary_legal_id: string) =>
    `Error Updating Payout Beneficiary profile details with Provider for beneficiary_legal_entity_id: ${beneficiary_legal_id} `,

  updateBeneficiaryDetailsGRPCError: (beneficiary_legal_id: string) =>
    `Error Updating Payout Beneficiary profile details with Provider for beneficiary_legal_entity_id: ${beneficiary_legal_id} `,

  getBeneficiaryDetailsProviderError: (beneficiaryProviderId: string) =>
    `Error fetching beneficiary details for beneficiaryProviderId: ${beneficiaryProviderId}`,

  getPayoutDetailsProviderError: (clientReferenceId: string) =>
    `Error fetching payout details clientReferenceId: ${clientReferenceId}`,
};

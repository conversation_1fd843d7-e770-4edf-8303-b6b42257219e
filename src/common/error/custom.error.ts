import { HttpStatus, Logger } from '@nestjs/common';

export enum ErrorCodes {
  ServiceError = 'internal_server_error',
  PaymentGateWayError = 'payment_gateway_error',
  ValidationError = 'validation_error',
}

export enum ErrorTypes {
  GRPCClientError = 'gRPCClientError',
  ProviderClientError = 'providerClientError',
  ServiceError = 'serviceError',
  ClientInputValidationError = 'clientInputValidationError',
}

export interface ErrorDetails {
  service?: string;
  controller?: string;
  method?: string;
  errorCode?: ErrorCodes;
  description?: string;
  message?: string | string[] | Record<string, any>;
  stack?: string[] | string;
  externalService?: string;
  type?: ErrorTypes;
  statusCode?: number;
  meta?: Record<string, any>;
}

export class CustomError extends Error {
  public errorInfo: ErrorDetails;
  protected readonly logger = new Logger();

  constructor(message: string, errorInfo: ErrorDetails = {}) {
    super(message);
    this.errorInfo = {
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      errorCode: ErrorCodes.ServiceError,
      ...errorInfo,
    };
    this.logger = new Logger(errorInfo.service);
  }

  log() {
    this.logger.error(JSON.stringify(this.errorInfo));
  }
}

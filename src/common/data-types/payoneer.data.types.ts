export interface IPayoneerPayoutRequest {
  client_reference_id: string;
  payee_id: string;
  description: string;
  currency: string;
  amount: string;
}

export interface IPayoneerAccount {
  id: string;
  type: string;
  currency: string;
  status: string;
  status_description: string;
  available_balance: string;
  update_time: string;
}

export type PayoneerMassPayoutRequest = {
  Payments: IPayoneerPayoutRequest[];
};

export type PayoneerAccountBalances = {
  result: {
    balances: {
      items: IPayoneerAccount[];
    };
  };
};

export interface IPayoneerTokenResponse {
  token_type: string;
  access_token: string;
  expires_in: number;
  consented_on: number;
  scope: string;
  refresh_token: string;
  refresh_token_expires_in: number;
  id_token: string;
  error: string;
  error_description: string;
}

export interface IPayoneerRegisterPayeeFormatResponse {
  audit_id: number;
  code: number;
  description: string;
  payout_method: {
    type: string;
    country: string;
    currency: string;
    bank_account_type: string;
    details: Record<string, any>;
  };
}

export interface IPayoneerBeneficiaryResponse {
  error?: string;
  audit_id: number;
  code: number;
  description: string;
}

export const PAYONEER_LEGAL_TYPES = {
  PUBLIC: 'PUBLIC',
  PRIVATE: 'PRIVATE',
  SOLE_PROPRIETORSHIP: 'SOLE_PROPRIETORSHIP',
  LLC: 'LLC',
  LLP: 'LLP',
  LTD: 'LTD',
  INC: 'INC',
  NON_PROFIT: 'NON_PROFIT',
  TRUST: 'TRUST',
  NONE: 'NONE',
};

export interface IPayoneerBeneficiaryPayeeDetails {
  type: string;
  contact: {
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
  };
  address: {
    address_line_1: string;
    address_line_2?: string;
    city: string;
    state: string;
    country: string;
    zip_code: string;
  };
  company?: {
    legal_type: string;
    incorporated_country: string;
    incorporated_state?: string;
    name: string;
    url: string;
  };
}

export interface IPayoneerPayoutMethodDetails {
  bank_account_type: string;
  country: string;
  currency: string;
  details: Record<string, any>;
}

export interface IPayoneerBeneficiaryRequest {
  payee_id: string;
  registration_mode: string;
  payee: IPayoneerBeneficiaryPayeeDetails;
  payout_method: IPayoneerPayoutMethodDetails;
}

export interface IGetPayoneerBeneficiaryDetailsResponse {
  audit_id: number;
  code: number;
  description: string;
  payout_method: {
    type: string;
    country: string;
    currency: string;
    bank_account_type: string;
    details: Record<string, any>;
  };
  type: string;
  status: string;
  registration_date: string;
  payoneer_id: string;
  contact: {
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
  };
  address: {
    address_line_1: string;
    address_line_2?: string;
    city: string;
    state: string;
    country: string;
    zip_code: string;
  };
  company?: {
    legal_type?: string;
    incorporated_country?: string;
    incorporated_state?: string;
    name?: string;
    url?: string;
  };
}

export interface IPayoutInstruction {
  client_reference_id: string;
  payee_id: string;
  description: string;
  currency: string;
  amount: string;
  payout_date: string;
  group_id: string;
}

export interface IMassPayoutRequest {
  Payments: IPayoutInstruction[];
}

export interface IMassPayoutRequestResponse {
  error?: string;
  audit_id: number;
  code: number;
  description: string;
}

export interface IPayoutStatusResponse {
  audit_id: number;
  code: number;
  description: string;
  payout_id: string;
  status: string;
  status_category: string;
  payee_id: string;
  payout_date: string;
  amount: number;
  currency: string;
  load_date?: string;
  reason_code?: string;
  reason_description?: string;
  cancel_reason_code?: string;
  cancel_reason_description?: string;
}

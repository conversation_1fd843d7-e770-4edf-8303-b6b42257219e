import { BaseDto } from '../dtos/base.dto';
import { HTTP_CONTENT_TYPE_JSON } from '../constants/keys';
import {
  IPayoutInstruction,
  PayoneerAccountBalances,
  PayoneerMassPayoutRequest,
} from './payoneer.data.types';
import { BeneficiaryRole, PaymentRequestStatus, PaymentType, SwiftChargeType } from '../constants/enums';
import { AxiosRequestConfig } from 'axios';

export type APIResponseDataType = Record<string, any> | PayoneerAccountBalances | PayoneerMassPayoutRequest;

export interface IAPIResponseError {
  message: string;
}

export interface IClientProviderConfig {
  account_id?: string;
  wallet_id?: string;
}

export interface IAxiosHeader {
  Authorization: string;
  [HTTP_CONTENT_TYPE_JSON]?: string;
}

export type AxiosParams = Record<string, any>;

export type ValidatableDto = BaseDto;

export type AxiosHeader = Record<string, any>;

export interface IAxiosConfig extends AxiosRequestConfig {
  headers?: AxiosHeader;
  params?: AxiosParams;
}

export type APIResponse<T> = {
  statusCode: number;
  data: T;
  error: IAPIResponseError;
};

export type GrpcHttpResponse = {
  statusCode: number;
  data: any;
  errors: any;
};

export interface IBeneficiaryInfo {
  beneficiary_id: string;
  currency: string;
  bank_country: string;
  beneficiary_payout_id?: string;
  beneficiary_role: BeneficiaryRole;
  swift_charge_type: SwiftChargeType;
}

export interface IClientProviderConfig {
  account_id?: string;
  contact_id?: string;
}

export interface IProviderConfig {
  providerProgramId?: string;
  providerToken?: string;
}

export interface IPaymentRequestContract {
  legal_entity_id: string;
  amount: number;
  currency: string;
  status: PaymentRequestStatus;
  payment_date: string;
  purpose_of_payment: string;
  reference_number: string;
  unique_request_id: string;
}

export interface IPaymentRequestV2Contract extends IPaymentRequestContract {
  client_legal_entity_id: string;
  payment_type: PaymentType;
  provider: string;
  checksum: string;
  is_valid: boolean;
  beneficiary: IBeneficiaryInfo;
  client_config: IClientProviderConfig;
}

export interface IPaymentRequestExtendedV2Contract extends IPaymentRequestV2Contract {
  version: string;
  provider_system_reference_number: string;
  provider_payload: Record<string, any>;
  payment_error: Record<string, any>;
  sent_to_provider_at: Date;
  failed_by_provider_at: Date;
  rejected_by_system_at: Date;
}

export interface IPaymentRequestQuery {
  provider?: string;
  legal_entity_id?: string;
  legal_entity_ids?: string[];
  client_legal_entity_id?: string;
  client_legal_entity_ids?: string[];
  unique_request_id?: string;
  unique_request_ids?: string[];
  status?: PaymentRequestStatus;
}

export interface IPaymentRequestUpdate {
  status?: PaymentRequestStatus;
  provider_system_reference_number?: string;
  provider_payload?: IPayoutInstruction | Record<string, any>;
  sent_to_provider_at?: string;
  failed_by_provider_at?: string;
  rejected_by_system_at?: string;
  payment_error?: Record<string, any>;
}

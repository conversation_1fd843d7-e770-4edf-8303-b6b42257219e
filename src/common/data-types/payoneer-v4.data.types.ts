import {
  BankAccountTypeForPayoneerGBT,
  CommitStatus,
  IdDocumentType,
  LegalType,
  LockType,
  PayeeDataMatchingType,
  PayeeType,
  PaymentStatus,
  PayoutMethodType,
} from '../constants/enums';

export interface IPayoneerErrorObject {
  error: string;
  error_description: string;
  error_details: {
    code: number;
  };
}

// Authorization APIs
export interface ICreateApplicationTokenRequest {
  grant_type: string; // "client_credentials" (fixed value)
  scope: string; // A space-separated list of scopes which have been granted for this access_token (read write)
}

export interface ICreateApplicationTokenResponse {
  token_type: string;
  access_token: string;
  expires_in: number;
  consented_on: number;
  scope: string;
  refresh_token: string | null;
  refresh_token_expires_in: number;
  error: string | null;
  error_description: string | null;
  id_token: string | null;
}

export interface IRevokeApplicationTokenRequest {
  token_type_hint: string; // This field must be “access_token”
  token: string; // The access_token that the user wants to revoke access from
}

export interface IRevokeApplicationTokenResponse {
  status: string;
  error: string | null;
  error_description: string | null;
}

// Payee Onboarding APIs
export interface ICreateRegistrationLinkRequest {
  client_session_id?: string;
  payee_id: string; // Unique identifier of the payee on the client's platform
  redirect_url?: string; // will be skuad url
  redirect_time?: string;
  lock_type?: LockType;
  payee_data_matching_type?: PayeeDataMatchingType; // check againist specific values with the existing Payoneer user when “Payee Entity Matching Type” value is sent in the API
  already_have_an_account?: boolean;
  language_id?: string;
  payee: {
    type: string; // use enum value PayeeType
    contact: {
      first_name: string;
      last_name: string;
      email: string;
      date_of_birth: string; // YYYY-MM-DD
      mobile: string;
    };
    address: {
      address_line_1: string;
      address_line_2: string;
      city: string;
      state: string;
      country: string;
      zip_code: string;
    };
    company?: {
      legal_type: LegalType;
      name: string;
      url: string;
      incorporated_country: string;
      incorporated_state: string;
    };
    id_document: {
      type: IdDocumentType;
      number: string;
      issue_country: string;
      issue_date: string;
      expiration_date: string;
      first_name_in_local_language: string;
      last_name_in_local_language: string;
    };
  };
  payout_method: {
    type: string;
    bank_account_type: string;
    country: string;
    currency: string;
    bank_field_details: {
      name: string;
      value: string;
    }[];
  };
}

export interface ICreateRegistrationLinkResponse {
  result: {
    registration_link: string;
    token: string;
  };
  error_description?: string;
  error_details?: {
    code: number;
    errors: {
      code: string;
      target: string;
      message: string;
    }[];
  };
}

export interface IGetBankFieldsForPayeeOuput {
  result: {
    payout_method: {
      type: string;
      country: string;
      currency: string;
      bank_account_type: string;
      fields: {
        items: {
          field_name: string;
          field_id: number;
          field_type: string;
          label: string;
          description: string;
          watermark: string;
          required: boolean;
          min_length: number;
          max_length: number;
          regex?: string;
          options_contains_other: boolean;
          dependent_options_contains_others: boolean;
        }[];
      };
    };
  };
}

// Payee Management APIs
export interface IPayeeStatusResponse extends IPayoneerErrorObject {
  result: {
    account_id: string; // Account ID is the same as Payoneer ID

    status: {
      type: number; // Refer to "Payee Status" table
      description: string;

      reasons?: {
        items: Array<{
          reason_id: number;
          reason_description: string;
          requirement_id: number;
          requirement_description: string;
        }>;
      };
    };

    registration_date: string; // The payee registration date

    payout_method: {
      type: PayoutMethodType; // Allowed values: ACCOUNT, PREPAID_CARD, BANK, DIRECT_DEPOSIT
      currency: string; // ISO3 currency code, exactly 3 characters
    };
  };
}

export interface ICheckAccountExistsResponse extends IPayoneerErrorObject {
  result: {
    exist: boolean;
  };
}

// Mass Payout APIs
export interface ISubmitPayoutRequest {
  client_reference_id: string;
  payee_id: string;
  description: string;
  currency: string;
  amount: number;
  payout_date?: string;
  group_id?: string;
}

export interface ISubmitMassPayoutRequest {
  payments: ISubmitPayoutRequest[];
}

export interface ISubmitMassPayoutResponse extends IPayoneerErrorObject {
  result: string;
}

export interface IGetPayoutStatusOutputDto {
  payout_date: string;
  amount: number;
  currency: string;
  status: string;
  target_amount?: number;
  target_currency?: string;
  payee_id: string;
  payout_id: string;
  scheduled_payout_date?: string;
  load_date?: string;
  reason_code?: string;
  reason_description?: string;
  cancel_reason_code?: number;
  cancel_reason_description?: string;
}

export interface IGetPayoutStatusResponse extends IPayoneerErrorObject {
  result: IGetPayoutStatusOutputDto;
}

export interface ICancelPayoutResponse extends IPayoneerErrorObject {
  result: {
    status_description: string;
    date_updated: string;
  };
}

// Utility APIs
export interface IAccountBalanceResponse extends IPayoneerErrorObject {
  result: {
    balance: number;
    currency: string;
    fees_due: number;
  };
}

export interface ITransferFundsRequest {
  target_partner_id: string;
  amount: number;
  description: string;
}

export interface ITransferFundsResponse {
  result?: {
    date_time: string;
    funding_request_id: number;
    source_currency: string;
    source_amount: number;
    target_currency: string;
    target_amount: number;
  };
  error?: string;
  error_description?: string;
  error_details?: Record<string, any>;
}

// General Error Interface
export interface ClientErrorV2 {
  error: string;
  errors: Record<any, any>[];
}

export interface TransactionResult {
  result: {
    transactions: Transactions;
  };
  error: {
    code: number;
    message: string;
    description: string;
    target: string;
    errors?: Array<{
      code: number;
      target: string;
      message: string;
    }>;
  };
}

export interface Transactions {
  items: TransactionItem[];
  total: number;
  next: string;
}

export interface TransactionItem {
  balance_id: string;
  amount: number;
  amount_currency: string;
  date: string;
  description: string;
  id: string;
  status_id: number;
  status_name: string;
  balance_name: string;
  category_id: number;
  category_name: string;
  source: string;
  target: string;
  masked_card_number?: string;
  running_balance?: number;
  transactionDetails: TransactionDetails;
}

export interface TransactionDetails {
  fee?: number;
  fee_currency?: string;
  payment_description?: string;
  amount_charged?: number;
  amount_charged_currency?: string;
  payee_id?: string;
  transfer_amount?: number;
  transfer_amount_currency?: string;
  transfer_fx_rate?: number;
  transfer_source_currency?: string;
  transfer_target_currency?: string;
  payment_reference?: string;
  recipient_name?: string;
  recipient_email?: string;
  batch_id?: number;
  payment_date?: string;
  receiving_account_id?: number;
  beneficiary?: string;
  note?: string;
  source_address_line_1?: string;
  source_address_line_2?: string;
  source_city?: string;
  source_state?: string;
  source_country?: string;
  source_zip_code?: string;
  source_company_name?: string;
  source_first_name?: string;
  source_last_name?: string;
  source_legal_name?: string;
  requested_amount?: number;
  requested_amount_currency?: string;
  location_city?: string;
  location_country?: string;
  authorization_code?: string;
  original_transaction_amount?: number;
  original_transaction_currency?: string;
  fx_rate?: number;
  decline_reason?: string;
  source_currency?: string;
  target_currency?: string;
  authorization_id_response?: string;
}

export interface AccessTokenDto {
  token_type: string;
  access_token: string;
  expires_in: number;
  consented_on: number;
  //example "read write openid personal-details"
  scope: string;
  refresh_token: string;
  refresh_token_expires_in: number;
  /**
  token thriough which we will identify account id. decode base 64 to get account id
  will be gettin this object on decode
    {
    "sub": "1-5/2968916",
    "eid": "2968916",
    "etid": "5",
    "account_id": "2968916"
  }
   */
  id_token: string;
  error: string;
  error_description: string;
}
export interface ReceivingAccount {
  id: string;
  currency: string;
  creation_date: string;
  nickname: string;
  status: string;
  status_description: string;
  details: {
    items: {
      name: string;
      value: string;
    }[];
    total: number;
  };
}

export interface ReceivingAccounts {
  items: ReceivingAccount[];
  total: number;
}

export interface GetFundingsAccountsResponse {
  result: {
    receiving_accounts: ReceivingAccounts;
  };
}

export interface PayeeInput {
  type: PayeeType;
  contact?: {
    first_name?: string;
    last_name?: string;
    date_of_birth?: string; // Format: YYYY-MM-DD
  };
  address?: {
    address_line_1?: string;
    address_line_2?: string;
    city?: string;
    state?: string;
    country?: string;
    zip_code?: string;
  };
  company?: {
    legal_type?: LegalType;
    name?: string;
    incorporated_country?: string;
    incorporated_state?: string;
    incorporated_address_1?: string;
    incorporated_address_2?: string;
    incorporated_city?: string;
    incorporated_zipcode?: string;
  };
  id_document?: {
    number?: string;
    type?: string;
    issue_country?: string;
    name_on_id?: string;
    expiration_date?: string;
    issue_date?: string;
    first_name_in_local_language?: string;
    last_name_in_local_language?: string;
    company_name_in_local_language?: string;
  };
}

export interface RegisterPayeePayoutMethod {
  type: PayoutMethodType;
  bank_account_type: BankAccountTypeForPayoneerGBT;
  country: string;
  currency: string;
  bank_field_details: Record<string, string>[];
}

export interface RegisterPayeeInput {
  payee_id: string;
  payee: PayeeInput;
  payout_method: RegisterPayeePayoutMethod;
}

interface PayoutMethodFieldItem {
  field_name: string;
  field_id: number;
  field_type: string;
  label: string;
  description: string;
  watermark: string;
  required: boolean;
  min_length: number;
  max_length: number;
  regex: string;
  options_contains_other: boolean;
  dependent_options_contains_others: boolean;
}

interface PayoutMethodFields {
  items: PayoutMethodFieldItem[];
}

interface PayoutMethod {
  type: string;
  country: string;
  currency: string;
  bank_account_type: string;
  fields: PayoutMethodFields;
}

export interface RegisterPayeeFormatOutput {
  result: {
    payout_method: PayoutMethod;
  };
}

export interface EditPayeeProfileInput {
  contact?: {
    first_name: string;
    last_name: string;
    email?: string;
    date_of_birth?: string;
  };
  address: {
    address_line_1: string;
    address_line_2: string;
    city: string;
    state: string;
    country: string;
    zip_code: string;
  };
  company: {
    legal_type: LegalType;
    name: string;
    incorporated_country: string;
    incorporated_state: string;
    incorporated_address_1: string;
    incorporated_address_2: string;
    incorporated_city: string;
    incorporated_zipcode: string;
  };
}

export interface EditPayeeResponse extends IPayoneerErrorObject {
  result: string;
}

export interface GetKYCResponse {
  contexts: Array<{
    context_id: string;
    ahbilling_country: string;
    context_type_id: number;
    context_status: number;
    issues: Array<{
      issue_id: string;
      related_context_id: string;
      issue_status: number;
      issue_type_id: number;
      requirements: Array<{
        requirement_id: string;
        related_issue_id: string;
        requirement_status: number;
        requirement_type_id: number;
        sub_requirement_type_ids: Array<number>;
      }>;
    }>;
  }>;
}

export interface GetKYCUploadDocumentResponse {
  result: {
    upload_status: string;
  };
}

export interface SubmitQuestionnaireInput {
  RequirementId: string;
  SubRequirementTypeId: number;
  Questionnaire: {
    SubmittedQuestions: Array<{
      QuestionId: number;
      Answers: Array<string>;
    }>;
  };
}

export interface GetPayeeExtendedDetailsResponse {
  result: {
    accountId: string;
    type: string;
    company: {
      incorporated_address_1: string;
      incorporated_address_2: string;
      incorporated_city: string;
      incorporated_state: string;
      incorporated_zipcode: string;
    };
    contact: {
      first_name: string;
      last_name: string;
      email: string;
      phone: string;
      mobile: string;
      mobile_country_code: string;
    };
    address: {
      address_line_1: string;
      address_line_2: string;
      city: string;
      state: string;
      country: string;
      zip_code: string;
    };
    payout_method: {
      type: string;
      bank_account_type: string;
      country: string;
      currency: string;
      bank_field_details: Record<string, string>[];
    };
  };
}

export interface GetAccountBalanceOutput {
  result: {
    balances: {
      items: Array<{
        id: string;
        type: string;
        currency: string;
        status: string;
        status_name: string;
        available_balance: string;
        update_time: string;
      }>;
    };
  };
}

export interface ChargeAccountByClientDebitRequest {
  client_reference_id: string;
  amount: number;
  currency: string;
  description: string;
  to: {
    type: string; // partner in this case
    id: string;
  };
}

export interface ChargeAccountByClientDebitResponse {
  result: {
    type: string; // e.g., "debit"
    commit_id: string;
    client_reference_id: string;
    last_status: string; // ISO timestamp of last status update
    created_at: string; // ISO timestamp of creation
    request_details: {
      client_reference_id: string;
      amount: number;
      description: string;
      currency: string;
      to: {
        id: number | string;
        type: string;
      };
    };
    fx: {
      quote: string;
      rate: number;
      base_rate: number;
      source_currency: string;
      target_currency: string;
    };
    fees: Array<{
      type: string;
      amount: number;
      currency: string;
    }>;
    amounts: {
      charged: { amount: number; currency: string };
      target: { amount: number; currency: string };
    };
    expires_at: string; // ISO timestamp when commit_id expires
  };
}

export interface GetPaymentStatusResponse {
  result: {
    status: number; // e.g., 2
    status_description: PaymentStatus; // e.g., "completed"
    payment_id: string;
  };
}

export interface CommitResponse {
  result: {
    payment_id: string;
    status: number; // e.g., 2 for completed
    status_description: CommitStatus;
    last_status: string; // ISO timestamp of last status update
    created_at: string; // ISO timestamp of original request creation
    client_reference_id: string;
    request_details: {
      url: string;
      body: {
        client_reference_id: string;
        amount: number;
        description: string;
        currency: string;
        to: { id: number | string; type: string };
      };
    };
    to: { type: string; id: string };
    fx: {
      quote: string;
      rate: number;
      base_rate: number;
      source_currency: string;
      target_currency: string;
    };
    fees: Array<{ type: string; amount: number; currency: string }>;
    amounts: {
      charged: { amount: number; currency: string };
      target: { amount: number; currency: string };
    };
  };
  challenge: {
    type: string;
    expires_at: string;
    session_id: string;
    url: string;
  };
  error?: string; // e.g., "Forbidden" if failed
  error_description?: string; // e.g., "Insufficient balance"
  error_details?: {
    code: number;
    sub_code: number;
    target: string;
    errors: Array<{
      code: number;
      target: string;
      message: string;
    }>;
  };
}

export interface CommitResponseAfterChallengeResponse {
  result: {
    status: number;
    status_description: string;
    last_status: string;
    client_reference_id: string;
    request_details: {
      url: string;
      body: {
        client_reference_id: string;
        amount: number;
        description: string;
        currency: string;
        to: { id: number | string; type: string };
      };
    };
    to: { type: string; id: string };
    fx: {
      quote: string;
      rate: number;
      base_rate: number;
      source_currency: string;
      target_currency: string;
    };
    fees: Array<{ type: string; amount: number; currency: string }>;
    amounts: {
      charged: { amount: number; currency: string };
      target: { amount: number; currency: string };
    };
  };
  error?: string; // e.g., "Forbidden" if failed
  error_description?: string; // e.g., "Insufficient balance"
  error_details?: {
    code: number;
    sub_code: number;
    target: string;
    errors: Array<{
      code: number;
      target: string;
      message: string;
    }>;
  };
}

export interface MerchantChargeStatusResponse {
  result: {
    status: number;
    status_description: string;
    last_status: string;
    request_details: {
      client_reference_id: string;
      amount: number;
      description: string;
      currency: string;
      to: {
        id: number;
        type: string;
      };
    };
    payment_id: string;
    fees: Array<{
      type: string;
      amount: number;
      currency: string;
    }>;
    fx: {
      quote: string;
      rate: number;
      base_rate: number;
      source_currency: string;
      target_currency: string;
    };
    amounts: {
      charged: {
        amount: number;
        currency: string;
      };
      target: {
        amount: number;
        currency: string;
      };
    };
  };
}

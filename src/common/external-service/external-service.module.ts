import { Module } from '@nestjs/common';
import { PayoneerClientModule } from './payoneer/payoneer.module';
import { PaymentGrpcClientModule } from './payments/payments.module';
import { ContractServiceModule } from './contract-service/contract.module';
import { ForexServiceModule } from './forex-service/forex.module';

@Module({
  imports: [PayoneerClientModule, PaymentGrpcClientModule, ContractServiceModule, ForexServiceModule],
})
export class ExternalApiClientModule {}

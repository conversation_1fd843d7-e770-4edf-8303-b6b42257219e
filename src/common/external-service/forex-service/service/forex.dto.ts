export interface CurrencyPairs {
  fromCurrency: string;
  toCurrency: string;
}

export interface ForexPairs {
  forexPair: string;
  clientId: string;
  invoiceId: string;
  toCurrency: string;
  fromCurrency: string;
}

export interface ForexRatesInput {
  clientId?: string;
  currencyPairs: string[];
}

export interface CurrencyExchangeRates {
  fromCurrency: string;
  toCurrency: string;
  exchangeRate: number;
  originalExchangeRate?: number;
}
export interface CurrencyExchangeOutput {
  clientId: string;
  exchangeRates: CurrencyExchangeRates[];
}
export interface ForexResponse {
  errors: any[];
  currencyExchangeOutput: CurrencyExchangeOutput[];
}

export interface NonMarkedUpResponse {
  currencyExchangeRatesNonMarkedUp: any[];
}

export interface NonMarkedUpResponse {
  errors: any[];
  data: NonMarkedUpResponse;
}

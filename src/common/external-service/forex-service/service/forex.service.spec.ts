import { Test, TestingModule } from '@nestjs/testing';
import { ForexService } from './forex-service';
import { AppConfigService } from '../../../../config/services/app.config.service';
import axios from 'axios';
import { Logger } from '@nestjs/common';

// Mock axios to control its behavior in tests
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('ForexService', () => {
  let service: ForexService;

  // Mock Logger to prevent console output during tests and to spy on it
  const mockLogger = {
    error: jest.fn(),
    log: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  };

  const mockAppConfigService = {
    getGatewayUrl: jest.fn().mockReturnValue('http://test-gateway.com'),
    getContractServiceUrl: jest.fn().mockReturnValue('http://test-contract.com'),
    getSkuadSGId: jest.fn().mockReturnValue('test-skuad-id'),
    getUserToken: jest.fn().mockReturnValue('test-user-token'),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ForexService,
        {
          provide: AppConfigService,
          useValue: mockAppConfigService,
        },
      ],
    })
      .setLogger(mockLogger as unknown as Logger)
      .compile();

    service = module.get<ForexService>(ForexService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getForexCharges', () => {
    const currencyPairs = [{ fromCurrency: 'USD', toCurrency: 'INR' }];
    const forexServiceUrl = process.env?.FOREX_SERVICE_URL + '/graphql';

    it('should fetch forex charges successfully', async () => {
      const mockResponseData = {
        currencyExchangeRates: [
          {
            fromCurrency: 'USD',
            toCurrency: 'INR',
            exchangeRate: 82.5,
            originalExchangeRate: 82.0,
          },
        ],
      };
      mockedAxios.post.mockResolvedValue({
        data: { data: mockResponseData },
      } as any);

      const result = await service.getForexCharges(currencyPairs);

      expect(result).toEqual(mockResponseData);
      expect(mockedAxios.post).toHaveBeenCalledWith(
        forexServiceUrl,
        expect.objectContaining({
          query: expect.any(String),
          variables: {
            currencyExchangeInput: {
              currencyPairsList: currencyPairs,
            },
          },
        }),
        expect.objectContaining({
          headers: expect.any(Object),
        }),
      );
    });

    it('should return null and log an error if axios post fails', async () => {
      const error = new Error('Network Error');
      mockedAxios.post.mockRejectedValue(error);

      const result = await service.getForexCharges(currencyPairs);

      expect(result).toBeNull();
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.objectContaining({ message: 'Unable to fetch config' }),
        undefined,
        'ForexService',
      );
    });

    it('should return undefined if response data is not in the expected format', async () => {
      mockedAxios.post.mockResolvedValue({ data: { not_data: {} } } as any);

      const result = await service.getForexCharges(currencyPairs);

      expect(result).toBeUndefined();
    });
  });
});

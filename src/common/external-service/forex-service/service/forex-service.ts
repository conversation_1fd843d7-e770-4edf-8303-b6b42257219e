import { Injectable, Logger } from '@nestjs/common';
import { CommonService } from '../../common-service/gql/common-gql.service';
import { AppConfigService } from '../../../../config/services/app.config.service';
import axios from 'axios';

@Injectable()
export class ForexService extends CommonService {
  private readonly forex_service_base_url = process.env?.FOREX_SERVICE_URL + '/graphql';
  private readonly logger = new Logger(ForexService.name);

  constructor(config: AppConfigService) {
    super(config);
  }

  async getForexCharges(currencyPairs: any) {
    try {
      const query = `query currencyExchangeRates($currencyExchangeInput: GetCurrencyExchangeInput!){
        currencyExchangeRates(currencyExchangeInput: $currencyExchangeInput){
                fromCurrency
                toCurrency
                exchangeRate
                originalExchangeRate
        }
    }`;
      const variables = {
        currencyExchangeInput: {
          currencyPairsList: currencyPairs,
        },
      };
      const headers = this.getAxiosHeader();

      const response = await axios.post(
        this.forex_service_base_url,
        {
          query,
          variables,
        },
        {
          headers,
        },
      );

      // Access the response data
      const data = response?.data?.data;

      return data;
    } catch (error) {
      this.logger.error({
        message: `Unable to fetch config`,
        error: error?.message,
        stack: error?.stack,
        data: { currencyPairs },
      });
      return null;
    }
  }
}

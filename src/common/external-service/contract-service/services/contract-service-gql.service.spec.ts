import { Test, TestingModule } from '@nestjs/testing';
import { ContractService } from './contract-service-gql.service';
import { AppConfigService } from '../../../../config/services/app.config.service';
import { HttpService } from '@nestjs/axios';
import { of, throwError } from 'rxjs';

describe('ContractService', () => {
  let service: ContractService;
  let httpService: HttpService;
  let configService: AppConfigService;

  const mockConfig = {
    getContractServiceGraphqlUrl: jest.fn(),
    getContractServiceKey: jest.fn(),
    getGatewayUrl: jest.fn(),
    getContractServiceUrl: jest.fn(),
    getSkuadSGId: jest.fn(),
    getUserToken: jest.fn(),
  };

  const mockHttpService = {
    post: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ContractService,
        {
          provide: AppConfigService,
          useValue: mockConfig,
        },
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
      ],
    }).compile();

    httpService = module.get<HttpService>(HttpService);
    configService = module.get<AppConfigService>(AppConfigService);

    service = module.get<ContractService>(ContractService);

    service = new ContractService(configService, httpService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getConfigByKey', () => {
    const mockResponse = {
      data: {
        pay_getConfig_private: {
          key: 'testKey',
          values: ['testValue'],
        },
      },
    };

    it('should successfully fetch config', async () => {
      mockHttpService.post.mockReturnValue(of({ data: mockResponse }));

      const result = await service.getConfigByKey('testKey', 'testType');

      expect(result).toEqual(mockResponse.data.pay_getConfig_private);
      expect(httpService.post).toHaveBeenCalled();
    });

    it('should return null when API call fails', async () => {
      const error = new Error('API Error');
      mockHttpService.post.mockReturnValue(throwError(() => error));

      const result = await service.getConfigByKey('testKey', 'testType');

      expect(result).toBeNull();
    });

    it('should call API with correct parameters', async () => {
      mockHttpService.post.mockReturnValue(of({ data: mockResponse }));

      await service.getConfigByKey('testKey', 'testType');

      expect(httpService.post).toHaveBeenCalledWith(
        expect.any(String),
        {
          query: expect.any(String),
          variables: {
            configKey: 'testKey',
            type: 'testType',
            exact: true,
          },
        },
        { headers: expect.any(Object) },
      );
    });

    it('should handle empty response', async () => {
      mockHttpService.post.mockReturnValue(of({ data: { data: { pay_getConfig_private: null } } }));

      const result = await service.getConfigByKey('testKey', 'testType');

      expect(result).toBeNull();
    });
  });

  describe('error handling', () => {
    it('should handle network timeout errors', async () => {
      const timeoutError = new Error('Request timeout');
      mockHttpService.post.mockReturnValue(throwError(() => timeoutError));

      const result = await service.getConfigByKey('testKey', 'testType');
      expect(result).toBeNull();
    });

    it('should handle malformed response data', async () => {
      mockHttpService.post.mockReturnValue(of({ data: 'invalid' }));

      const result = await service.getConfigByKey('testKey', 'testType');
      expect(result).toBeNull();
    });

    it('should handle undefined response', async () => {
      mockHttpService.post.mockReturnValue(of(undefined));

      const result = await service.getConfigByKey('testKey', 'testType');
      expect(result).toBeNull();
    });

    it('should log error details when API call fails', async () => {
      const error = new Error('API Error');
      const loggerSpy = jest.spyOn(service['logger'], 'error');
      mockHttpService.post.mockReturnValue(throwError(() => error));

      await service.getConfigByKey('testKey', 'testType');

      expect(loggerSpy).toHaveBeenCalledWith({
        message: 'Unable to fetch config',
        error: error.message,
        stack: error.stack,
        data: { configKey: 'testKey', type: 'testType' },
      });
    });
  });
});

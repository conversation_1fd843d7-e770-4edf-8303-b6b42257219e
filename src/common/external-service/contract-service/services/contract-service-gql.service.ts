import { Injectable, Logger } from '@nestjs/common';
import { CommonService } from '../../common-service/gql/common-gql.service';
import { AppConfigService } from '../../../../config/services/app.config.service';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom, map } from 'rxjs';

@Injectable()
export class ContractService extends CommonService {
  private readonly logger = new Logger(ContractService.name);

  constructor(
    config: AppConfigService,
    private httpService: HttpService,
  ) {
    super(config);
  }

  async getConfigByKey(configKey: string, type: string) {
    try {
      const variables = {
        configKey: configKey,
        type,
        exact: true,
      };
      const headers = this.getAxiosHeader();
      const query = `
      query($configKey: String!, $type:ConfigType!, $exact:Boolean) {
        pay_getConfig_private(configKey:$configKey, type: $type, exact:$exact){
          key
          values
        }
      }`;

      const response = await lastValueFrom(
        this.httpService
          .post(`${this.contractServiceGraphqlUrl}`, { query, variables }, { headers })
          .pipe(map((resp) => resp.data)),
      );

      return response['data']['pay_getConfig_private'];
    } catch (error) {
      this.logger.error({
        message: `Unable to fetch config`,
        error: error?.message,
        stack: error?.stack,
        data: { configKey, type },
      });
      return null;
    }
  }
}

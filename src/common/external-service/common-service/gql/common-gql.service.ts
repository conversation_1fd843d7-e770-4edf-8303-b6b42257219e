import { AppConfigService } from '../../../../config/services/app.config.service';

export class CommonService {
  protected config: AppConfigService;
  protected url: string;
  protected token: string;
  protected userToken: string;
  protected skuadSGId: string;
  protected contractServiceUrl: string;
  protected contractServiceGraphqlUrl: string;
  constructor(config: AppConfigService) {
    this.config = config;
    this.url = this.config.getGatewayUrl();
    this.contractServiceUrl = this.config.getContractServiceUrl();
    this.skuadSGId = this.config.getSkuadSGId();
    this.token = this.config.getUserToken();

    this.userToken = 'skuad-token=' + this.token;
    this.contractServiceGraphqlUrl = this.contractServiceUrl + '/api/v1/graphql';
  }

  getAxiosHeader() {
    return {
      'Content-Type': 'application/json',
      'x-auth-legal-entity-id': this.skuadSGId,
      cookie: this.userToken,
    };
  }
}

import { AppConfigService } from '../../../../config/services/app.config.service';
import { CommonService } from './common-gql.service';

describe('CommonService', () => {
  let service: CommonService;
  let configService: AppConfigService;

  beforeEach(() => {
    configService = {
      getGatewayUrl: jest.fn().mockReturnValue('http://gateway-url'),
      getContractServiceUrl: jest.fn().mockReturnValue('http://contract-url'),
      getSkuadSGId: jest.fn().mockReturnValue('skuad-sg-id'),
      getUserToken: jest.fn().mockReturnValue('user-token'),
    } as unknown as AppConfigService;

    service = new CommonService(configService);
  });

  describe('constructor', () => {
    it('should initialize with correct values from config', () => {
      expect(configService.getGatewayUrl).toHaveBeenCalled();
      expect(configService.getContractServiceUrl).toHaveBeenCalled();
      expect(configService.getSkuadSGId).toHaveBeenCalled();
      expect(configService.getUserToken).toHaveBeenCalled();

      expect(service['url']).toBe('http://gateway-url');
      expect(service['contractServiceUrl']).toBe('http://contract-url');
      expect(service['skuadSGId']).toBe('skuad-sg-id');
      expect(service['token']).toBe('user-token');
      expect(service['userToken']).toBe('skuad-token=user-token');
      expect(service['contractServiceGraphqlUrl']).toBe('http://contract-url/api/v1/graphql');
    });
  });

  describe('getAxiosHeader', () => {
    it('should return correct header object', () => {
      const headers = service.getAxiosHeader();

      expect(headers).toEqual({
        'Content-Type': 'application/json',
        'x-auth-legal-entity-id': 'skuad-sg-id',
        cookie: 'skuad-token=user-token',
      });
    });
  });
});

import { Test, TestingModule } from '@nestjs/testing';
import { PayoneerApiRouter } from './payoneer-api-router.utils';
import { AppConfigService } from '../../../../config/services/app.config.service';
import { PayoneerAPINames, Version } from '../../../constants/enums';
import { IParams } from '../../../interfaces/params.interface';

describe('PayoneerApiRouter', () => {
  let payoneerApiRouter: PayoneerApiRouter;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PayoneerApiRouter,
        {
          provide: AppConfigService,
          useValue: {
            getProviderClientId: jest.fn().mockReturnValue('mock-client-id'),
            getRedirectUri: jest.fn().mockReturnValue('https://mock-redirect'),
          },
        },
      ],
    }).compile();
    payoneerApiRouter = module.get<PayoneerApiRouter>(PayoneerApiRouter);
  });

  it('should be defined', () => {
    expect(payoneerApiRouter).toBeDefined();
  });

  it('should return correct API path for GetTransactions with all optional params', () => {
    const params = {
      account_id: '123',
      page_size: '10',
      from_transaction_date: '2023-01-01',
      to_transaction_date: '2023-12-31',
      category_id: 'CAT1',
      status_id: 'STATUS1',
      last_transaction_id: 'TX123',
      last_transaction_date: '2023-12-30',
      include_details: true,
    };
    const result = payoneerApiRouter.getApiPath(Version.V4, PayoneerAPINames.GetTransactions, params as any);
    expect(result).toBe(
      '/v4/accounts/123/transactions?include_details=true&page_size=10&from=2023-01-01&to=2023-12-31&category_id=CAT1&status_id=STATUS1&last_transaction_id=TX123&last_transaction_date=2023-12-30&include_details=true',
    );
  });

  it('should return correct API path for GetTransactions with partial optional params', () => {
    const params = {
      account_id: '123',
      page_size: '10',
      from_transaction_date: '2023-01-01',
      to_transaction_date: '2023-12-31',
      category_id: 'CAT1',
      status_id: 'STATUS1',
    };
    const result = payoneerApiRouter.getApiPath(Version.V4, PayoneerAPINames.GetTransactions, params as any);
    expect(result).toBe(
      '/v4/accounts/123/transactions?include_details=true&page_size=10&from=2023-01-01&to=2023-12-31&category_id=CAT1&status_id=STATUS1',
    );
  });

  it('should return correct API path for RegisterPayee', () => {
    const params = {
      program_id: 'PROG123',
    };
    const result = payoneerApiRouter.getApiPath(Version.V4, PayoneerAPINames.RegisterPayee, params as any);
    expect(result).toBe('/v4/programs/PROG123/payees/register-payee');
  });

  it('should return correct API path for GetRegisterPayeeFormat', () => {
    const params = {
      program_id: 'PROG123',
      payee_type: 'individual',
      country: 'US',
      currency: 'USD',
    };
    const result = payoneerApiRouter.getApiPath(
      Version.V4,
      PayoneerAPINames.GetRegisterPayeeFormat,
      params as any,
    );
    expect(result).toBe(
      '/v4/programs/PROG123/payout-methods/bank/account-types/individual/countries/US/currencies/USD',
    );
  });

  it('should return correct API path for EditProfile', () => {
    const params = {
      program_id: 'PROG123',
      payee_id: 'PAYEE456',
    };
    const result = payoneerApiRouter.getApiPath(Version.V4, PayoneerAPINames.EditProfile, params as any);
    expect(result).toBe('/v4/programs/PROG123/payees/PAYEE456/details');
  });

  it('should return correct API path for EditTransferMethod', () => {
    const params = {
      program_id: 'PROG123',
      payee_id: 'PAYEE456',
    };
    const result = payoneerApiRouter.getApiPath(
      Version.V4,
      PayoneerAPINames.EditTransferMethod,
      params as any,
    );
    expect(result).toBe('/v4/programs/PROG123/payees/PAYEE456/payout-methods');
  });

  it('should return correct API path for GetKYC', () => {
    const params = {
      program_id: 'PROG123',
      payee_id: 'PAYEE456',
    };
    const result = payoneerApiRouter.getApiPath(Version.V4, PayoneerAPINames.GetKYC, params as any);
    expect(result).toBe('/v4/programs/PROG123/payees/PAYEE456/kyc');
  });

  it('should return correct API path for KYCUploadDocuments', () => {
    const params = {
      program_id: 'PROG123',
      payee_id: 'PAYEE456',
    };
    const result = payoneerApiRouter.getApiPath(
      Version.V4,
      PayoneerAPINames.KYCUploadDocuments,
      params as any,
    );
    expect(result).toBe('/v4/programs/PROG123/payees/PAYEE456/documents');
  });

  it('should return correct API path for CancelCharge', () => {
    const params = {
      account_id: 'ACC789',
      payment_id: 'PAY123',
    };
    const result = payoneerApiRouter.getApiPath(Version.V4, PayoneerAPINames.CancelCharge, params as any);
    expect(result).toBe('/v4/accounts/ACC789/payments/PAY123/cancel');
  });

  it('should return correct API path for CancelCommit', () => {
    const params = {
      account_id: 'ACC789',
      commit_id: 'COMMIT123',
    };
    const result = payoneerApiRouter.getApiPath(Version.V4, PayoneerAPINames.CancelCommit, params as any);
    expect(result).toBe('/v4/accounts/ACC789/payments/COMMIT123/cancel?type=commit_id');
  });

  it('should return correct API path for SubmitCommitId', () => {
    const params = {
      account_id: 'ACC001',
      commit_id: 'COMMIT001',
    };
    const result = payoneerApiRouter.getApiPath(Version.V4, PayoneerAPINames.SubmitCommitId, params as any);
    expect(result).toBe('/v4/accounts/ACC001/payments/COMMIT001');
  });

  it('should return correct API path for RevokeApplicationToken', () => {
    const result = payoneerApiRouter.getApiPath(
      Version.V2,
      PayoneerAPINames.RevokeApplicationToken,
      {} as any,
    );
    expect(result).toBe('/api/v2/oauth2/revoke');
  });

  it('should return correct API path for GenerateAccessToken', () => {
    const result = payoneerApiRouter.getApiPath(Version.V2, PayoneerAPINames.GenerateAccessToken, {} as any);
    expect(result).toBe('/api/v2/oauth2/token');
  });

  it('should return correct API path for CreateClientAccountCodeUrl with all params', () => {
    const params = {
      redirectUrl: 'https://custom-redirect',
      scope: 'custom-scope',
      state: 'custom-state',
    };
    const result = payoneerApiRouter.getApiPath(
      Version.V2,
      PayoneerAPINames.CreateClientAccountCodeUrl,
      params as any,
    );

    expect(result).toBe(
      '/api/v2/oauth2/authorize?client_id=mock-client-id&redirect_uri=https://custom-redirect&scope=custom-scope&response_type=code&state=custom-state',
    );
  });

  it('should fallback to default scope and state if not provided', () => {
    const result = payoneerApiRouter.getApiPath(
      Version.V2,
      PayoneerAPINames.CreateClientAccountCodeUrl,
      {} as any,
    );

    expect(result).toBe(
      '/api/v2/oauth2/authorize?client_id=mock-client-id&redirect_uri=https://mock-redirect&scope=read%20write%20openid%20personal-details&response_type=code&state={{apuid}}_{{payoneerid}}_{{sessionid}}',
    );
  });

  it('should return correct API path for GetPayoutStatus', () => {
    const params = {
      program_id: 'PROG789',
      client_reference_id: 'REF456',
    };
    const result = payoneerApiRouter.getApiPath(Version.V4, PayoneerAPINames.GetPayoutStatus, params as any);

    expect(result).toBe('/v4/programs/PROG789/payouts/REF456/status');
  });

  it('should return correct API path for GetFundingAccounts', () => {
    const params = {
      account_id: 'ACC789',
    };
    const result = payoneerApiRouter.getApiPath(
      Version.V4,
      PayoneerAPINames.GetFundingAccounts,
      params as any,
    );

    expect(result).toBe('/v4/accounts/ACC789/receiving_accounts');
  });
  it('should return correct API path for GetPayoutStatus', () => {
    const params = {
      program_id: 'PROG123',
      client_reference_id: 'PAYOUT456',
    };
    const result = payoneerApiRouter.getApiPath(Version.V4, PayoneerAPINames.GetPayoutStatus, params as any);
    expect(result).toBe('/v4/programs/PROG123/payouts/PAYOUT456/status');
  });

  it('should return correct API path for GetFundingAccounts', () => {
    const params = {
      account_id: 'ACC789',
    };
    const result = payoneerApiRouter.getApiPath(
      Version.V4,
      PayoneerAPINames.GetFundingAccounts,
      params as any,
    );
    expect(result).toBe('/v4/accounts/ACC789/receiving_accounts');
  });

  describe('Handling undefined/null parameters object', () => {
    it('should handle undefined params for CreateRegistrationLink', () => {
      const result = payoneerApiRouter.getApiPath(
        Version.V4,
        PayoneerAPINames.CreateRegistrationLink,
        undefined as any,
      );
      expect(result).toBe('/v4/programs/undefined/payees/registration-link');
    });

    it('should handle null params for DetermineRequiredBankingFields', () => {
      const result = payoneerApiRouter.getApiPath(
        Version.V4,
        PayoneerAPINames.DetermineRequiredBankingFields,
        null as any,
      );
      expect(result).toBe(
        '/v4/programs/undefined/payout-methods/bank/account-types/undefined/countries/undefined/currencies/undefined',
      );
    });
  });

  describe('Handling undefined properties', () => {
    it('should handle undefined program_id for TransferFunds', () => {
      const params = {
        program_id: undefined, // explicitly undefined
        otherProp: 'value',
      };
      const result = payoneerApiRouter.getApiPath(Version.V4, PayoneerAPINames.TransferFunds, params as any);
      expect(result).toBe('/v4/programs/undefined/transfer');
    });

    it('should handle undefined payeeEmail for CheckPayeeAccountExists', () => {
      const params = {
        program_id: 'PROG123',
        payeeEmail: undefined, // explicitly undefined
      };
      const result = payoneerApiRouter.getApiPath(
        Version.V4,
        PayoneerAPINames.CheckPayeeAccountExists,
        params as any,
      );
      expect(result).toBe('/v4/programs/PROG123/account-exists?email=undefined');
    });
  });

  describe('special cases', () => {
    it('should handle undefined conditional params', () => {
      const params = {
        account_id: 'ACC123',
        last_transaction_id: undefined, // one of the pair undefined
        last_transaction_date: '2023-01-01',
      };
      const result = payoneerApiRouter.getApiPath(
        Version.V4,
        PayoneerAPINames.GetTransactions,
        params as any,
      );
      expect(result).not.toContain('last_transaction_id');
      expect(result).not.toContain('last_transaction_date');
    });

    it('should handle null values in query params', () => {
      const params = {
        account_id: 'ACC123',
        category_id: null, // explicitly null
      };
      const result = payoneerApiRouter.getApiPath(
        Version.V4,
        PayoneerAPINames.GetTransactions,
        params as any,
      );
      expect(result).toContain(
        `/v4/accounts/${params.account_id}/transactions?include_details=true&page_size=undefined&from=undefined&to=undefined`,
      );
    });

    it('should handle undefined program_id and payee_id', () => {
      const result = payoneerApiRouter.getApiPath(Version.V4, PayoneerAPINames.GetPayeeStatus, {} as any);
      expect(result).toBe('/v4/programs/undefined/payees/undefined/status');
    });

    it('should handle null params object', () => {
      const result = payoneerApiRouter.getApiPath(Version.V4, PayoneerAPINames.GetPayeeStatus, null as any);
      expect(result).toBe('/v4/programs/undefined/payees/undefined/status');
    });

    it('should handle undefined program_id', () => {
      const result = payoneerApiRouter.getApiPath(Version.V4, PayoneerAPINames.SubmitMassPayout, {
        otherProp: 'value',
      } as any);
      expect(result).toBe('/v4/programs/undefined/masspayouts');
    });

    it('should handle empty params', () => {
      const result = payoneerApiRouter.getApiPath(Version.V4, PayoneerAPINames.SubmitMassPayout, {} as any);
      expect(result).toBe('/v4/programs/undefined/masspayouts');
    });

    it('should handle undefined reference_id', () => {
      const params = {
        program_id: 'PROG123',
        client_reference_id: undefined,
      };
      const result = payoneerApiRouter.getApiPath(
        Version.V4,
        PayoneerAPINames.GetPayoutStatus,
        params as any,
      );
      expect(result).toBe('/v4/programs/PROG123/payouts/undefined/status');
    });

    it('should handle missing params object', () => {
      const result = payoneerApiRouter.getApiPath(
        Version.V4,
        PayoneerAPINames.GetPayoutStatus,
        undefined as any,
      );
      expect(result).toBe('/v4/programs/undefined/payouts/undefined/status');
    });

    it('should handle all undefined params', () => {
      const result = payoneerApiRouter.getApiPath(Version.V4, PayoneerAPINames.CancelPayout, {
        random: 'value',
      } as any);
      expect(result).toBe('/v4/programs/undefined/payouts/undefined/cancel');
    });

    it('should handle null reference_id', () => {
      const params = {
        program_id: 'PROG123',
        client_reference_id: null,
      };
      const result = payoneerApiRouter.getApiPath(Version.V4, PayoneerAPINames.CancelPayout, params as any);
      expect(result).toBe('/v4/programs/PROG123/payouts/null/cancel');
    });

    it('should handle undefined params', () => {
      const result = payoneerApiRouter.getApiPath(
        Version.V4,
        PayoneerAPINames.QueryProgramBalance,
        undefined as any,
      );
      expect(result).toBe('/v4/programs/undefined/balance');
    });

    it('should handle explicitly undefined program_id', () => {
      const params = {
        program_id: undefined,
      };
      const result = payoneerApiRouter.getApiPath(
        Version.V4,
        PayoneerAPINames.QueryProgramBalance,
        params as any,
      );
      expect(result).toBe('/v4/programs/undefined/balance');
    });

    it('should handle empty params', () => {
      const result = payoneerApiRouter.getApiPath(Version.V4, PayoneerAPINames.TransferFunds, {} as any);
      expect(result).toBe('/v4/programs/undefined/transfer');
    });

    it('should handle null program_id', () => {
      const params = {
        program_id: null,
      };
      const result = payoneerApiRouter.getApiPath(Version.V4, PayoneerAPINames.TransferFunds, params as any);
      expect(result).toBe('/v4/programs/null/transfer');
    });

    it('should handle undefined email', () => {
      const params = {
        program_id: 'PROG123',
        payeeEmail: undefined,
      };
      const result = payoneerApiRouter.getApiPath(
        Version.V4,
        PayoneerAPINames.CheckPayeeAccountExists,
        params as any,
      );
      expect(result).toBe('/v4/programs/PROG123/account-exists?email=undefined');
    });

    it('should handle null params object', () => {
      const result = payoneerApiRouter.getApiPath(
        Version.V4,
        PayoneerAPINames.CheckPayeeAccountExists,
        null as any,
      );
      expect(result).toBe('/v4/programs/undefined/account-exists?email=undefined');
    });

    it('should handle missing account_id', () => {
      const result = payoneerApiRouter.getApiPath(Version.V4, PayoneerAPINames.GetFundingAccounts, {
        otherProp: 'value',
      } as any);
      expect(result).toBe('/v4/accounts/undefined/receiving_accounts');
    });

    it('should handle undefined params', () => {
      const result = payoneerApiRouter.getApiPath(
        Version.V4,
        PayoneerAPINames.GetFundingAccounts,
        undefined as any,
      );
      expect(result).toBe('/v4/accounts/undefined/receiving_accounts');
    });
  });

  describe('Empty parameters object', () => {
    it('should handle empty object for QueryProgramBalance', () => {
      const result = payoneerApiRouter.getApiPath(
        Version.V4,
        PayoneerAPINames.QueryProgramBalance,
        {} as any,
      );
      expect(result).toBe('/v4/programs/undefined/balance');
    });

    it('should handle empty object for GetPayeeStatus', () => {
      const result = payoneerApiRouter.getApiPath(Version.V4, PayoneerAPINames.GetPayeeStatus, {} as any);
      expect(result).toBe('/v4/programs/undefined/payees/undefined/status');
    });
  });
});

import { PayoneerAPINames, Version } from '../../../constants/enums';
import { IApiRoutes } from '../../../interfaces/api-routes.interface';
import { Injectable } from '@nestjs/common';
import { IParams, IRefreshTokenParams } from '../../../interfaces/params.interface';
import { AppConfigService } from '../../../../config/services/app.config.service';
import { BeneficiaryRegistrationPurpose } from '../../../../beneficiary/dtos/inputs/beneficiary.graphql.input';

@Injectable()
export class PayoneerApiRouter {
  private readonly routes: Record<string, any>;
  constructor(protected readonly config: AppConfigService) {
    this.routes = this.getRoutes();
  }

  getApiPath(version: Version, apiName: string, params: IParams | IRefreshTokenParams): string {
    return this.routes[version][apiName](params);
  }

  private getRoutes(): IApiRoutes {
    return {
      v4: {
        [PayoneerAPINames.CreateRegistrationLink]: (params: IParams) => {
          return `/v4/programs/${params?.program_id}/payees/registration-link`;
        },
        [PayoneerAPINames.DetermineRequiredBankingFields]: (params: IParams) => {
          return `/v4/programs/${params?.program_id}/payout-methods/bank/account-types/${params?.payeeType}/countries/${params?.country}/currencies/${params?.currency}`;
        },
        [PayoneerAPINames.GetPayeeStatus]: (params: IParams) => {
          return `/v4/programs/${params?.program_id}/payees/${params?.payee_id}/status`;
        },
        [PayoneerAPINames.SubmitMassPayout]: (params: IParams) => {
          return `/v4/programs/${params?.program_id}/masspayouts`;
        },
        [PayoneerAPINames.GetPayoutStatus]: (params: IParams) => {
          return `/v4/programs/${params?.program_id}/payouts/${params?.client_reference_id}/status`;
        },
        [PayoneerAPINames.CancelPayout]: (params: IParams) => {
          return `/v4/programs/${params?.program_id}/payouts/${params?.client_reference_id}/cancel`;
        },
        [PayoneerAPINames.QueryProgramBalance]: (params: IParams) => {
          return `/v4/programs/${params?.program_id}/balance`;
        },
        [PayoneerAPINames.TransferFunds]: (params: IParams) => {
          return `/v4/programs/${params?.program_id}/transfer`;
        },
        [PayoneerAPINames.CheckPayeeAccountExists]: (params: IParams) => {
          return `/v4/programs/${params?.program_id}/account-exists?email=${encodeURIComponent(params?.payeeEmail)}`;
        },
        [PayoneerAPINames.GetTransactions]: (params: IParams) => {
          let url = `/v4/accounts/${params?.account_id}/transactions?include_details=true&page_size=${params?.page_size}&from=${params?.from_transaction_date}&to=${params?.to_transaction_date}`;

          if (params?.category_id) {
            url += `&category_id=${params?.category_id}`;
          }
          if (params?.status_id) {
            url += `&status_id=${params?.status_id}`;
          }
          if (params?.last_transaction_id && params?.last_transaction_date) {
            url += `&last_transaction_id=${params?.last_transaction_id}&last_transaction_date=${params?.last_transaction_date}`;
          }

          if (params.include_details) {
            url += `&include_details=true`;
          }

          return url;
        },
        [PayoneerAPINames.GetFundingAccounts]: (params: IParams) => {
          return `/v4/accounts/${params?.account_id}/receiving_accounts`;
        },

        // White label APIs
        [PayoneerAPINames.RegisterPayee]: (params: IParams) => {
          return `/v4/programs/${params.program_id}/payees/register-payee`;
        },
        [PayoneerAPINames.GetRegisterPayeeFormat]: (params: IParams) => {
          return `/v4/programs/${params.program_id}/payout-methods/bank/account-types/${params.payee_type}/countries/${params.country}/currencies/${params.currency}`;
        },
        [PayoneerAPINames.EditProfile]: (params: IParams) => {
          return `/v4/programs/${params.program_id}/payees/${params.payee_id}/details`;
        },
        [PayoneerAPINames.EditTransferMethod]: (params: IParams) => {
          return `/v4/programs/${params.program_id}/payees/${params.payee_id}/payout-methods`;
        },
        [PayoneerAPINames.GetKYC]: (params: IParams) => {
          return `/v4/programs/${params.program_id}/payees/${params.payee_id}/kyc`;
        },
        [PayoneerAPINames.KYCUploadDocuments]: (params: IParams) => {
          return `/v4/programs/${params.program_id}/payees/${params.payee_id}/documents`;
        },
        [PayoneerAPINames.GetPayeeDetailsExtended]: (params: IParams) => {
          return `/v4/programs/${params.program_id}/payees/${params.payee_id}/details?show_payout_method_details=true`;
        },
        [PayoneerAPINames.SubmitQuestionnaires]: (params: IParams) => {
          return `/v4/programs/${params.program_id}/payees/${params.payee_id}/questionnaires`;
        },
        [PayoneerAPINames.GetAccountBalance]: (params: IParams) => {
          return `/v4/accounts/${params.account_id}/balances`;
        },
        [PayoneerAPINames.ChargeAccountByClientDebit]: (params: IParams) => {
          return `/v4/accounts/${params.account_id}/balances/${params?.balance_id}/payments/debit`;
        },
        [PayoneerAPINames.GetPaymentStatus]: (params: IParams) => {
          let url = `/v4/accounts/${params.account_id}/payments/`;
          if (params.payment_id) {
            url += params.payment_id;
          } else if (params.client_reference_id) {
            url += `${params.client_reference_id}?type=client_reference_id`;
          }
          return url;
        },
        [PayoneerAPINames.Commit]: (params: IParams) => {
          return `/v4/accounts/${params.account_id}/payments/${params.commit_id}`;
        },
        [PayoneerAPINames.GetCommitResponseAfterChallenge]: (params: IParams) => {
          return `/${params.response_path}`;
        },
        [PayoneerAPINames.ChargeAccountByClientPartnerDebit]: (params: IParams) => {
          return `/v4/accounts/${params.account_id}/balances/${params.balance_id}/payments/partnerdebit`;
        },
        [PayoneerAPINames.CancelCharge]: (params: IParams) => {
          return `/v4/accounts/${params.account_id}/payments/${params.payment_id}/cancel`;
        },
        [PayoneerAPINames.CancelCommit]: (params: IParams) => {
          return `/v4/accounts/${params.account_id}/payments/${params.commit_id}/cancel?type=commit_id`;
        },
        [PayoneerAPINames.GetMerchantChargeStatus]: (params: IParams) => {
          return `/v4/programs/${params.program_id}/accounts/${params.account_id}/charges/${params.client_reference_id}/status`;
        },
        [PayoneerAPINames.ProcessWithdrawFund]: (params: IParams) => {
          return `/v4/accounts/${params.account_id}/balances/${params.balance_id}/payments/withdraw`;
        },
        [PayoneerAPINames.GetEligibleBanks]: (params: IParams) => {
          return `/v4/accounts/${params.account_id}/eligibility/withdraw`;
        },
        [PayoneerAPINames.SubmitCommitId]: (params: IParams) => {
          return `/v4/accounts/${params.account_id}/payments/${params.commit_id}`;
        },
      },
      v2: {
        [PayoneerAPINames.CreateApplicationToken]: () => {
          return `/api/v2/oauth2/token`;
        },
        [PayoneerAPINames.RevokeApplicationToken]: () => {
          return `/api/v2/oauth2/revoke`;
        },
        [PayoneerAPINames.RefreshAccessToken]: () => {
          return `/api/v2/oauth2/token`;
        },
        [PayoneerAPINames.CreateClientAccountCodeUrl]: (params: IParams) => {
          let clientId = this.config.getProviderClientId();
          return `/api/v2/oauth2/authorize?client_id=${clientId}&redirect_uri=${params.redirectUrl ?? this.config.getRedirectUri()}&scope=${params.scope ?? 'read%20write%20openid%20personal-details'}&response_type=code&state=${params.state ?? '{{apuid}}_{{payoneerid}}_{{sessionid}}'}`;
        },
        [PayoneerAPINames.GenerateAccessToken]: () => {
          return `/api/v2/oauth2/token`;
        },
        [PayoneerAPINames.RevokeAccessToken]: () => {
          return '/api/v2/oauth2/revoke';
        },
      },
    };
  }
}

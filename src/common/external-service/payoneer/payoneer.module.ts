import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '../../../config/config.module';
import { AppConfigService } from '../../../config/services/app.config.service';
import { PayoneerHttpClient } from './services/payoneer.http.client.service';
import { PayoneerApiRouter } from './helpers/payoneer-api-router.utils';
import { ProgramTokensRepository } from '../../repositories/program-token.repository';

@Module({
  imports: [
    HttpModule.registerAsync({
      imports: [ConfigModule],
      inject: [AppConfigService],
      useFactory: (config: AppConfigService) => ({
        timeout: config.getHttpTimeout(),
      }),
    }),
    ConfigModule,
  ],
  providers: [PayoneerHttpClient, PayoneerApiRouter, ProgramTokensRepository],
  exports: [PayoneerHttpClient],
})
export class PayoneerClientModule {}

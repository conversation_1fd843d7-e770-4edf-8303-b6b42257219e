import { Test, TestingModule } from '@nestjs/testing';
import { PayoneerHttpClient } from './payoneer.http.client.service';
import { AppConfigService } from '../../../../config/services/app.config.service';
import { ProgramTokensRepository } from '../../../repositories/program-token.repository';
import { LoggerService } from '../../../services/LoggerService';
import { HttpStatus } from '@nestjs/common';
import {
  PayoneerAPINames,
  Version,
  AuthType,
  CreateApplicationTokenValues,
  HttpMethod,
  TokenType,
  PaymentStatus,
  Currency,
  AccountType,
  PayoutMethodType,
  BankAccountTypeForPayoneerGBT,
  LegalType,
} from '../../../../common/constants/enums';
import { of } from 'rxjs';
import { BeneficiaryRegistrationPurpose } from '../../../../beneficiary/dtos/inputs/beneficiary.graphql.input';
import {
  ChargeAccountByClientDebitRequest,
  RegisterPayeePayoutMethod,
} from '../../../../common/data-types/payoneer-v4.data.types';
import { payoneer } from '@skuad/proto-utils/dist/interface/proto/payoneer/payoneer';

describe('PayoneerHttpClient', () => {
  let service: PayoneerHttpClient;
  let mockConfigService: jest.Mocked<AppConfigService>;
  let mockProgramTokenRepository: jest.Mocked<ProgramTokensRepository>;
  let mockLoggerService: jest.Mocked<LoggerService>;
  const fixedDate = new Date('2023-01-01T00:00:00Z');

  beforeAll(() => {
    // Mock all Date operations
    jest.useFakeTimers();
    jest.setSystemTime(fixedDate);
  });

  afterAll(() => {
    // Restore real timers
    jest.useRealTimers();
  });

  beforeEach(async () => {
    mockConfigService = {
      getAPIBaseUrl: jest.fn().mockReturnValue('https://api.payoneer.com'),
      getAPIAuthUrl: jest.fn().mockReturnValue('https://auth.payoneer.com'),
      getLoginAuthorizationToken: jest.fn().mockReturnValue('basic-auth-token'),
    } as any;

    mockProgramTokenRepository = {
      getToken: jest.fn(),
    } as any;

    mockLoggerService = {
      log: jest.fn(),
      error: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PayoneerHttpClient,
        { provide: AppConfigService, useValue: mockConfigService },
        { provide: ProgramTokensRepository, useValue: mockProgramTokenRepository },
        { provide: LoggerService, useValue: mockLoggerService },
      ],
    }).compile();

    service = module.get<PayoneerHttpClient>(PayoneerHttpClient);
  });

  afterEach(() => {
    jest.clearAllMocks();
    delete process.env.PayoneerApiToken;
    delete process.env.PayoneerApiTokenExpiryDate;
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('withToken', () => {
    it('should use basic auth for token-related APIs', async () => {
      const mockApiCall = jest.fn().mockResolvedValue({
        statusCode: HttpStatus.OK,
        data: { token: 'new-token' },
      });

      const [result, error] = await service['withToken'](
        mockApiCall,
        PayoneerAPINames.RefreshAccessToken,
        'client123',
      );

      expect(mockConfigService.getLoginAuthorizationToken).toHaveBeenCalled();
      expect(mockApiCall).toHaveBeenCalledWith({
        Authorization: 'Basic basic-auth-token',
        'Content-Type': 'application/x-www-form-urlencoded',
      });
      expect(result).toEqual({ token: 'new-token' });
      expect(error).toBeNull();
    });
  });

  describe('fetchOrCreateApiToken', () => {
    it('should create new API token', async () => {
      const mockResponse = {
        statusCode: HttpStatus.OK,
        data: {
          access_token: 'new-api-token',
          expires_in: 3600,
        },
        error: null,
      };

      // Mock Date.now() to return a fixed value
      const mockDateNow = jest.spyOn(Date, 'now').mockReturnValue(1625097600000);

      jest.spyOn(service, 'makeApiCall').mockResolvedValue(mockResponse);

      const [token, error] = await service.fetchOrCreateApiToken();

      expect(service.makeApiCall).toHaveBeenCalledWith(expect.anything(), {
        body: {
          grant_type: CreateApplicationTokenValues.ClientCredentials,
          scope: CreateApplicationTokenValues.Scope,
        },
        method: HttpMethod.Post,
        axios_config: {
          headers: {
            Authorization: 'Basic basic-auth-token',
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      });
      expect(token).toBe('new-api-token');
      expect(error).toBeNull();

      // Clean up the mock
      mockDateNow.mockRestore();
    });
  });

  describe('createRegistrationLinkV4', () => {
    it('should create registration link', async () => {
      jest
        .spyOn(service, 'makeApiCall')
        .mockResolvedValueOnce({
          // Token response
          statusCode: HttpStatus.OK,
          data: { access_token: 'test-token', expires_in: 3600 },
          error: null,
        })
        .mockResolvedValueOnce({
          // Actual API response
          statusCode: HttpStatus.OK,
          data: { registration_link: 'https://registration.link' },
          error: null,
        });

      const [result, error] = await service.createRegistrationLinkV4({ payee_id: 'payee123' }, 'program123');

      expect(service.makeApiCall).toHaveBeenCalledTimes(2);
      expect(service.makeApiCall).toHaveBeenLastCalledWith(
        'https://api.payoneer.com/v4/programs/program123/payees/registration-link', // Corrected endpoint
        {
          body: { payee_id: 'payee123' },
          method: HttpMethod.Post,
          axios_config: {
            headers: {
              Authorization: 'Bearer test-token',
            },
          },
        },
      );
      expect(result).toEqual({ registration_link: 'https://registration.link' });
      expect(error).toBeNull();
    });
  });

  describe('getPayeeStatus', () => {
    it('should get payee status', async () => {
      jest
        .spyOn(service, 'makeApiCall')
        .mockResolvedValueOnce({
          // Token response
          statusCode: HttpStatus.OK,
          data: { access_token: 'test-token', expires_in: 3600 },
          error: null,
        })
        .mockResolvedValueOnce({
          // Actual API response
          statusCode: HttpStatus.OK,
          data: { status: 'ACTIVE' },
          error: null,
        });

      const [result, error] = await service.getPayeeStatus('payee123', 'program123');

      expect(service.makeApiCall).toHaveBeenCalledTimes(2);
      expect(service.makeApiCall).toHaveBeenLastCalledWith(
        'https://api.payoneer.com/v4/programs/program123/payees/payee123/status',
        {
          method: HttpMethod.Get,
          axios_config: {
            headers: {
              Authorization: 'Bearer test-token',
            },
          },
        },
      );
      expect(result).toEqual({ status: 'ACTIVE' });
      expect(error).toBeNull();
    });
  });

  describe('submitMassPayoutV4', () => {
    it('should submit mass payout', async () => {
      jest
        .spyOn(service, 'makeApiCall')
        .mockResolvedValueOnce({
          // Token response
          statusCode: HttpStatus.OK,
          data: { access_token: 'test-token', expires_in: 3600 },
          error: null,
        })
        .mockResolvedValueOnce({
          // Actual API response
          statusCode: HttpStatus.OK,
          data: { payout_id: 'payout123' },
          error: null,
        });

      const payoutRequest = {
        client_reference_id: 'ref123',
        payments: [
          {
            payee_id: 'payee1',
            amount: 100,
            client_reference_id: 'ref123',
            description: 'description',
            currency: 'USD',
          },
        ],
      };

      const [result, error] = await service.submitMassPayoutV4(payoutRequest, 'program123');

      expect(service.makeApiCall).toHaveBeenCalledTimes(2);
      expect(service.makeApiCall).toHaveBeenLastCalledWith(
        'https://api.payoneer.com/v4/programs/program123/masspayouts', // Corrected endpoint
        {
          body: payoutRequest,
          method: HttpMethod.Post,
          axios_config: {
            headers: {
              Authorization: 'Bearer test-token',
            },
          },
        },
      );
      expect(result).toEqual({ payout_id: 'payout123' });
      expect(error).toBeNull();
    });
  });

  describe('getTransactions', () => {
    it('should get transactions', async () => {
      const mockResponse = {
        statusCode: HttpStatus.OK,
        data: { transactions: [{ id: 'txn1', amount: 100 }] },
        error: null,
      };

      jest.spyOn(service, 'makeApiCall').mockResolvedValue(mockResponse);
      mockProgramTokenRepository.getToken.mockResolvedValue({
        accessToken: 'access-token',
        applicationId: 'app123',
        consentedAt: new Date(),
        accountId: 'account123',
        expiresAt: new Date(),
        refreshToken: 'refresh-token',
        createdAt: new Date(),
        updatedAt: new Date(),
        id: 'token123',
        clientId: 'client123',
        tokenType: TokenType.AccessToken,
        scope: 'scope',
        deletedAt: new Date(),
        deletedBy: 'deletedBy',
        createdBy: 'createdBy',
        updatedBy: 'updatedBy',
        idToken: 'idToken',
        refreshTokenExpiresAt: new Date(),
        hasId: () => true,
        save: jest.fn(),
        remove: jest.fn(),
        softRemove: jest.fn(),
        recover: jest.fn(),
        reload: jest.fn(),
      });

      const fromDate = new Date('2023-01-01');
      const toDate = new Date('2023-01-31');
      const [result, error] = await service.getTransactions(
        'client123',
        'account123',
        fromDate,
        toDate,
        50,
        1,
        2,
        'lastTxn123',
        new Date('2023-01-30'),
      );

      expect(service.makeApiCall).toHaveBeenCalledWith(
        'https://api.payoneer.com/v4/accounts/account123/transactions?include_details=true&page_size=50&from=2023-01-01&to=2023-01-31&category_id=1&status_id=2&last_transaction_id=lastTxn123&last_transaction_date=2023-01-30&include_details=true',
        {
          method: HttpMethod.Get,
          axios_config: {
            headers: {
              Authorization: 'Bearer access-token',
            },
          },
        },
      );
      expect(result).toEqual({ transactions: [{ id: 'txn1', amount: 100 }] });
      expect(error).toBeNull();
    });
  });

  describe('refreshAccessToken', () => {
    it('should refresh access token', async () => {
      const mockResponse = {
        statusCode: HttpStatus.OK,
        data: { access_token: 'new-token', expires_in: 3600 },
        error: null,
      };

      jest.spyOn(service, 'makeApiCall').mockResolvedValue(mockResponse);
      mockProgramTokenRepository.getToken.mockResolvedValue({
        accessToken: 'old-token',
        applicationId: 'app123',
        consentedAt: new Date(),
        accountId: 'account123',
        expiresAt: new Date(),
        refreshToken: 'refresh-token',
        createdAt: new Date(),
        updatedAt: new Date(),
        id: 'token123',
        clientId: 'client123',
        tokenType: TokenType.AccessToken,
        scope: 'scope',
        deletedAt: new Date(),
        deletedBy: 'deletedBy',
        createdBy: 'createdBy',
        updatedBy: 'updatedBy',
        idToken: 'idToken',
        refreshTokenExpiresAt: new Date(),
        hasId: () => true,
        save: jest.fn(),
        remove: jest.fn(),
        softRemove: jest.fn(),
        recover: jest.fn(),
        reload: jest.fn(),
      });

      const [result, error] = await service.refreshAccessToken(
        'refresh-token',
        'client123',
        BeneficiaryRegistrationPurpose.PAYIN,
      );

      expect(service.makeApiCall).toHaveBeenCalledWith('https://auth.payoneer.com/api/v2/oauth2/token', {
        body: {
          grant_type: 'refresh_token',
          refresh_token: 'refresh-token',
        },
        method: HttpMethod.Post,
        axios_config: {
          headers: {
            Authorization: 'Basic basic-auth-token',
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      });
      expect(result).toEqual({ access_token: 'new-token', expires_in: 3600 });
      expect(error).toBeNull();
    });
  });

  describe('PayoneerHttpClient withToken error handling', () => {
    it('should return error if clientId is missing for AccessToken API', async () => {
      const mockApiCall = jest.fn();

      const [result, error] = await service['withToken'](
        mockApiCall,
        PayoneerAPINames.GetTransactions, // This is an AccessToken API
        null, // clientId missing
      );

      expect(result).toBeNull();
      expect(error).toEqual(
        expect.objectContaining({
          error: 'clientId is required',
        }),
      );
    });

    it('should return error if programToken is not found for AccessToken API', async () => {
      mockProgramTokenRepository.getToken.mockResolvedValue(null);
      const mockApiCall = jest.fn();

      const [result, error] = await service['withToken'](
        mockApiCall,
        PayoneerAPINames.GetTransactions, // AccessToken API
        'client123',
      );

      expect(mockProgramTokenRepository.getToken).toHaveBeenCalledWith({
        tokenType: TokenType.AccessToken,
        clientId: 'client123',
      });

      expect(result).toBeNull();
      expect(error).toEqual(
        expect.objectContaining({
          error: 'Program Token not found',
        }),
      );
    });
  });
  describe('cancelPayoutV4', () => {
    it('should cancel payout with correct headers and URL', async () => {
      jest
        .spyOn(service, 'makeApiCall')
        .mockResolvedValueOnce({
          // Token response
          statusCode: HttpStatus.OK,
          data: { access_token: 'test-token', expires_in: 3600 },
          error: null,
        })
        .mockResolvedValueOnce({
          // Actual API response
          statusCode: HttpStatus.OK,
          data: null,
          error: null,
        });

      const clientReferenceId = 'ref123';
      const programId = 'program123';

      const [result, error] = await service.cancelPayoutV4(clientReferenceId, programId);

      expect(service.makeApiCall).toHaveBeenCalledTimes(2);

      expect(service.makeApiCall).toHaveBeenLastCalledWith(
        `https://api.payoneer.com/v4/programs/${programId}/payouts/${clientReferenceId}/cancel`,
        {
          method: HttpMethod.Put,
          axios_config: {
            headers: {
              Authorization: 'Bearer test-token',
            },
          },
        },
      );

      expect(result).toBeNull();
      expect(error).toBeNull();
    });
  });

  describe('checkPayeeAccountExists', () => {
    it('should check if payee account exists', async () => {
      jest
        .spyOn(service, 'makeApiCall')
        .mockResolvedValueOnce({
          // Token response
          statusCode: HttpStatus.OK,
          data: { access_token: 'test-token', expires_in: 3600 },
          error: null,
        })
        .mockResolvedValueOnce({
          // Actual API response
          statusCode: HttpStatus.OK,
          data: { exists: true },
          error: null,
        });

      const payeeEmail = '<EMAIL>';
      const programId = 'program123';

      const [result, error] = await service.checkPayeeAccountExists(payeeEmail, programId);

      expect(service.makeApiCall).toHaveBeenCalledTimes(2);

      expect(service.makeApiCall).toHaveBeenLastCalledWith(
        `https://api.payoneer.com/v4/programs/${programId}/account-exists?email=${encodeURIComponent(payeeEmail)}`,
        {
          method: HttpMethod.Get,
          axios_config: {
            headers: {
              Authorization: 'Bearer test-token',
            },
          },
        },
      );

      expect(result).toEqual({ exists: true });
      expect(error).toBeNull();
    });
  });
  describe('cancelCharge', () => {
    const clientId = 'client123';
    const accountId = 'account123';
    const paymentId = 'payment456';
    const fixedDate = '2024-01-01';

    it('should return error if getPaymentStatus fails', async () => {
      jest.spyOn(service, 'getPaymentStatus').mockResolvedValue([null, { error: 'error', errors: [] }]);

      const [result, error] = await service.cancelCharge(clientId, accountId, paymentId);

      expect(result).toBeNull();
      expect(error).toEqual({ error: 'error', errors: [] });
    });

    it('should return error if payment is already completed', async () => {
      jest.spyOn(service, 'getPaymentStatus').mockResolvedValue([
        {
          result: {
            status_description: PaymentStatus.completed,
            status: 0,
            payment_id: '',
          },
        },
        null,
      ]);

      const [result, error] = await service.cancelCharge(clientId, accountId, paymentId);

      expect(result).toBeNull();
      expect(error).toEqual(new Error('Payment is already completed'));
    });

    it('should return cancellation result if already canceled', async () => {
      const fixedDate = new Date('2023-01-01T00:00:00.000Z');

      // beforeEach or inside specific test
      jest.useFakeTimers().setSystemTime(fixedDate);
      jest.spyOn(service, 'getPaymentStatus').mockResolvedValue([
        {
          result: {
            status_description: PaymentStatus.canceled,
            status: 0,
            payment_id: '',
          },
        },
        null,
      ]);

      const [result, error] = await service.cancelCharge(clientId, accountId, paymentId);

      expect(error).toBeNull();
      expect(result).toEqual({
        result: {
          cancel_date: fixedDate,
          cancelPaymentId: paymentId,
        },
      });
    });

    it('should make API call to cancel charge if eligible', async () => {
      const mockHeaders = { Authorization: 'Bearer test-token' };
      const mockResponse = [{ result: { success: true } }, null];

      jest.spyOn(service, 'getPaymentStatus').mockResolvedValue([
        {
          result: {
            status_description: PaymentStatus.Pending,
            status: 0,
            payment_id: '',
          },
        },
        null,
      ]);

      jest
        .spyOn(service, 'getApiUrl')
        .mockReturnValue('https://api.payoneer.com/v4/accounts/account123/payments/payment456/cancel');

      jest.spyOn(service, 'makeApiCall').mockResolvedValue(mockResponse as any);

      jest.spyOn(service as any, 'withToken').mockImplementation(async (fn: any) => fn(mockHeaders));

      const [result, error] = await service.cancelCharge(clientId, accountId, paymentId);

      expect(service.makeApiCall).toHaveBeenCalledWith(
        'https://api.payoneer.com/v4/accounts/account123/payments/payment456/cancel',
        {
          body: {},
          method: HttpMethod.Put,
          axios_config: {
            headers: mockHeaders,
          },
        },
      );

      expect(result).toEqual({ result: { success: true } });
      expect(error).toBeNull();
    });
  });
  describe('generateAccessToken', () => {
    const code = 'auth-code-123';
    const redirectUrl = 'https://myapp.com/redirect';
    const purpose = BeneficiaryRegistrationPurpose.PAYIN;

    const accessTokenResponse = {
      result: {
        access_token: 'access-token-xyz',
        expires_in: 3600,
      },
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should call makeApiCall with correct data', async () => {
      const code = 'auth-code';
      const redirectUrl = 'https://yourapp.com/redirect';
      const purpose = BeneficiaryRegistrationPurpose.PAYIN;
      const expectedHeaders = { Authorization: 'Bearer test-token' };
      const mockResponse = {
        result: { access_token: 'abc', expires_in: 3600 },
      };

      jest.spyOn(service, 'getCreateOrRevokeApplicationTokenUrl').mockReturnValue('mock-url');

      jest.spyOn(service as any, 'withToken').mockImplementation(async (fn: any) => {
        return [await fn({ Authorization: 'Bearer test-token' }), null];
      });

      const makeApiCallSpy = jest.spyOn(service, 'makeApiCall').mockResolvedValue(mockResponse as any);

      const [result, error] = await service.generateAccessToken(code, redirectUrl, purpose);

      expect(makeApiCallSpy).toHaveBeenCalledWith('mock-url', {
        body: {
          grant_type: 'authorization_code',
          code,
          redirect_uri: redirectUrl,
        },
        method: HttpMethod.Post,
        axios_config: {
          headers: {
            ...expectedHeaders,
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      });

      expect(result).toEqual(mockResponse);
      expect(error).toBeNull();
    });
  });
  describe('getMerchantChargeStatus', () => {
    const clientId = 'client123';
    const programId = 'program456';
    const clientReferenceId = 'ref789';
    const accountId = 'account999';

    it('should call makeApiCall with correct parameters and return result', async () => {
      const mockHeaders = { Authorization: 'Bearer test-token' };

      const mockResponse = {
        status: 'COMPLETED',
        referenceId: clientReferenceId,
      };

      const makeApiCallSpy = jest.spyOn(service, 'makeApiCall').mockResolvedValue(mockResponse as any);

      jest.spyOn(service as any, 'withToken').mockImplementation(async (fn: any) => {
        return [await fn(mockHeaders), null];
      });

      const [result, error] = await service.getMerchantChargeStatus(
        clientId,
        programId,
        clientReferenceId,
        accountId,
      );

      expect(makeApiCallSpy).toHaveBeenCalledWith(
        expect.stringContaining(
          `https://api.payoneer.com/v4/programs/${programId}/accounts/${accountId}/charges/${clientReferenceId}/status`,
        ),
        {
          body: {},
          method: 'get',
          axios_config: {
            headers: mockHeaders,
          },
        },
      );

      expect(result).toEqual(mockResponse);
      expect(error).toBeNull();
    });
  });
  describe('getCommitResponseAfterChallenge', () => {
    const clientId = 'client123';
    const responsePath = 'challenge/response/xyz';

    it('should call makeApiCall with correct parameters and return result', async () => {
      const mockHeaders = { Authorization: 'Bearer test-token' };

      const mockResponse = {
        status: 'SUCCESS',
        reference_id: 'ref-123',
      };

      const makeApiCallSpy = jest.spyOn(service, 'makeApiCall').mockResolvedValue(mockResponse as any);

      jest.spyOn(service as any, 'withToken').mockImplementation(async (fn: any) => {
        return [await fn(mockHeaders), null];
      });

      const [result, error] = await service.getCommitResponseAfterChallenge(clientId, responsePath);

      expect(makeApiCallSpy).toHaveBeenCalledWith(
        expect.stringContaining(`https://api.payoneer.com/${responsePath}`),
        {
          body: {},
          method: 'get',
          axios_config: {
            headers: mockHeaders,
          },
        },
      );

      expect(result).toEqual(mockResponse);
      expect(error).toBeNull();
    });
  });
  describe('commit', () => {
    const clientId = 'client123';
    const accountId = 'account456';
    const commitId = 'commit789';

    it('should call makeApiCall with correct parameters and return result', async () => {
      const mockHeaders = { Authorization: 'Bearer test-token' };

      const mockResponse = {
        status: 'SUCCESS',
        transaction_id: 'txn-001',
      };

      const makeApiCallSpy = jest.spyOn(service, 'makeApiCall').mockResolvedValue(mockResponse as any);

      jest.spyOn(service as any, 'withToken').mockImplementation(async (fn: any) => {
        return [await fn(mockHeaders), null];
      });

      const [result, error] = await service.commit(clientId, accountId, commitId);

      expect(makeApiCallSpy).toHaveBeenCalledWith(
        expect.stringContaining(`https://api.payoneer.com/v4/accounts/${accountId}/payments/${commitId}`), // adjust if URL pattern differs
        {
          body: {},
          method: 'put',
          axios_config: {
            headers: mockHeaders,
          },
        },
      );

      expect(result).toEqual(mockResponse);
      expect(error).toBeNull();
    });
  });
  describe('getPaymentStatus', () => {
    const clientId = 'client123';
    const accountId = 'account456';
    const paymentId = 'payment789';
    const clientReferenceId = 'ref001';

    it('should call makeApiCall with correct parameters and return result', async () => {
      const mockHeaders = { Authorization: 'Bearer test-token' };

      const mockResponse = {
        status: 'PENDING',
        payment_id: paymentId,
      };

      const makeApiCallSpy = jest.spyOn(service, 'makeApiCall').mockResolvedValue(mockResponse as any);

      jest.spyOn(service as any, 'withToken').mockImplementation(async (fn: any) => {
        return [await fn(mockHeaders), null];
      });

      const [result, error] = await service.getPaymentStatus(
        clientId,
        accountId,
        paymentId,
        clientReferenceId,
      );

      expect(makeApiCallSpy).toHaveBeenCalledWith(
        expect.stringContaining(`https://api.payoneer.com/v4/accounts/${accountId}/payments/${paymentId}`), // adjust if the actual URL differs
        {
          body: {},
          method: 'get',
          axios_config: {
            headers: mockHeaders,
          },
        },
      );

      expect(result).toEqual(mockResponse);
      expect(error).toBeNull();
    });
  });
  describe('chargeAccountByClientPartnerDebit', () => {
    const clientId = 'client123';
    const accountId = 'account456';
    const balanceId = 'balance789';

    const input: ChargeAccountByClientDebitRequest = {
      amount: 100,
      client_reference_id: 'ref-001',
      description: 'Test charge',
      currency: 'USD',
      to: {
        type: 'sd',
        id: '123',
      },
    };

    const mockHeaders = { Authorization: 'Bearer test-token' };

    const mockResponse = {
      charge_id: 'charge-123',
      status: 'success',
    };

    it('should call makeApiCall with correct URL and payload', async () => {
      const makeApiCallSpy = jest.spyOn(service, 'makeApiCall').mockResolvedValue(mockResponse as any);

      jest.spyOn(service as any, 'withToken').mockImplementation(async (fn: any) => {
        return [await fn(mockHeaders), null];
      });

      const [result, error] = await service.chargeAccountByClientPartnerDebit(
        input,
        clientId,
        accountId,
        balanceId,
      );

      expect(makeApiCallSpy).toHaveBeenCalledWith(
        expect.stringContaining(
          `https://api.payoneer.com/v4/accounts/${accountId}/balances/${balanceId}/payments/partnerdebit`,
        ),
        {
          body: input,
          method: 'post',
          axios_config: {
            headers: mockHeaders,
          },
        },
      );

      expect(result).toEqual(mockResponse);
      expect(error).toBeNull();
    });
  });
  describe('chargeAccountByClientDebit', () => {
    const clientId = 'client123';
    const accountId = 'account456';
    const balanceId = 'balance789';

    const input: ChargeAccountByClientDebitRequest = {
      amount: 100,
      client_reference_id: 'ref-001',
      description: 'Test charge',
      currency: 'USD',
      to: {
        type: 'sd',
        id: '123',
      },
    };

    const mockHeaders = { Authorization: 'Bearer test-token' };

    const mockResponse = {
      charge_id: 'charge-001',
      status: 'initiated',
    };

    it('should call makeApiCall with correct URL and request data', async () => {
      const makeApiCallSpy = jest.spyOn(service, 'makeApiCall').mockResolvedValue(mockResponse as any);

      jest.spyOn(service as any, 'withToken').mockImplementation(async (fn: any) => {
        return [await fn(mockHeaders), null];
      });

      const [result, error] = await service.chargeAccountByClientDebit(input, clientId, accountId, balanceId);

      expect(makeApiCallSpy).toHaveBeenCalledWith(
        expect.stringContaining(
          `https://api.payoneer.com/v4/accounts/${accountId}/balances/${balanceId}/payments/debit`,
        ), // adjust if exact URL differs
        {
          body: input,
          method: 'post',
          axios_config: {
            headers: mockHeaders,
          },
        },
      );

      expect(result).toEqual(mockResponse);
      expect(error).toBeNull();
    });
  });

  describe('revokeClientAccessToken', () => {
    const accessToken = 'access-token-123';
    const tokenTypeHint = TokenType.AccessToken;
    const clientId = 'client-123';
    const purpose = BeneficiaryRegistrationPurpose.PAYIN;

    const mockHeaders = { Authorization: 'Bearer test-token' };

    it('should call makeApiCall with correct revoke token data', async () => {
      const makeApiCallSpy = jest.spyOn(service, 'makeApiCall').mockResolvedValue([null, null] as any);

      jest.spyOn(service as any, 'withToken').mockImplementation(async (fn: any) => {
        return [await fn(mockHeaders), null];
      });

      const [result, error] = await service.revokeClientAccessToken(
        accessToken,
        tokenTypeHint,
        clientId,
        purpose,
      );

      expect(makeApiCallSpy).toHaveBeenCalledWith(
        expect.stringContaining('https://auth.payoneer.com/api/v2/oauth2/revoke'),
        {
          body: {
            token_type_hint: tokenTypeHint.toLowerCase(),
            token: accessToken,
          },
          method: 'post',
          axios_config: {
            headers: {
              ...mockHeaders,
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          },
        },
      );

      expect(result).toEqual([null, null]);
      expect(error).toBeNull();
    });
  });

  describe('getAccountBalances', () => {
    const clientId = 'client-456';
    const accountId = 'account-789';

    const mockHeaders = { Authorization: 'Bearer test-token' };
    const mockResponse = {
      balances: [
        { currency: 'USD', value: 100 },
        { currency: 'EUR', value: 50 },
      ],
    };

    it('should call makeApiCall with correct account balance URL', async () => {
      const makeApiCallSpy = jest.spyOn(service, 'makeApiCall').mockResolvedValue(mockResponse as any);

      jest.spyOn(service as any, 'withToken').mockImplementation(async (fn: any) => {
        return [await fn(mockHeaders), null];
      });

      const [result, error] = await service.getAccountBalances(clientId, accountId);

      expect(makeApiCallSpy).toHaveBeenCalledWith(
        expect.stringContaining(`/v4/accounts/${accountId}/balances`),
        {
          body: {},
          method: 'get',
          axios_config: {
            headers: mockHeaders,
          },
        },
      );

      expect(result).toEqual(mockResponse);
      expect(error).toBeNull();
    });
  });
  describe('getEligibleBanks', () => {
    const clientId = 'client-123';
    const accountId = 'account-456';
    const mockHeaders = { Authorization: 'Bearer test-token' };
    const mockResponse = { banks: ['Bank1', 'Bank2'] };

    it('should call makeApiCall with correct data and return banks list', async () => {
      const makeApiCallSpy = jest.spyOn(service, 'makeApiCall').mockResolvedValue(mockResponse as any);

      jest.spyOn(service as any, 'withToken').mockImplementation(async (fn: any) => {
        return [await fn(mockHeaders), null];
      });

      const [result, error] = await service.getEligibleBanks(clientId, accountId);

      expect(makeApiCallSpy).toHaveBeenCalledWith(
        expect.stringContaining(`https://api.payoneer.com/v4/accounts/${accountId}/eligibility/withdraw`),
        {
          body: {},
          method: 'get',
          axios_config: {
            headers: mockHeaders,
          },
        },
      );

      expect(result).toEqual(mockResponse);
      expect(error).toBeNull();
    });
  });
  describe('ProcessWithdrawFund', () => {
    const clientId = 'client-123';
    const accountId = 'account-456';
    const balanceId = 'balance-789';
    const mockHeaders = { Authorization: 'Bearer test-token' };
    const reqBody = { amount: 100, currency: 'USD' };
    const mockResponse = { withdrawalId: 'withdraw-123' };

    it('should call makeApiCall with correct withdrawal data', async () => {
      const makeApiCallSpy = jest.spyOn(service, 'makeApiCall').mockResolvedValue(mockResponse as any);

      jest.spyOn(service as any, 'withToken').mockImplementation(async (fn: any) => {
        return [await fn(mockHeaders), null];
      });

      const [result, error] = await service.ProcessWithdrawFund(clientId, accountId, balanceId, reqBody);

      expect(makeApiCallSpy).toHaveBeenCalledWith(
        expect.stringContaining(
          `https://api.payoneer.com/v4/accounts/${accountId}/balances/${balanceId}/payments/withdraw`,
        ),
        {
          body: reqBody,
          method: 'post',
          axios_config: {
            headers: mockHeaders,
          },
        },
      );

      expect(result).toEqual(mockResponse);
      expect(error).toBeNull();
    });
  });

  describe('submitQuestionnaires', () => {
    const programId = 'program-123';
    const payeeId = 'payee-456';
    const reqBody = {
      RequirementId: '124',
      SubRequirementTypeId: 124,
      SubmitQuestionnaireInput: '1234',
      Questionnaire: {
        SubmittedQuestions: [
          {
            QuestionId: 124,
            Answers: ['answer-1', 'answer-2'],
          },
        ],
      },
    };
    const mockHeaders = { Authorization: 'Bearer test-token' };

    it('should call makeApiCall with correct questionnaire data', async () => {
      const makeApiCallSpy = jest.spyOn(service, 'makeApiCall').mockResolvedValue([null, null] as any);

      jest.spyOn(service as any, 'withToken').mockImplementation(async (fn: any) => {
        return [await fn(mockHeaders), null];
      });

      const [result, error] = await service.submitQuestionnaires(reqBody, programId, payeeId);

      expect(makeApiCallSpy).toHaveBeenCalledWith(
        expect.stringContaining(
          `https://api.payoneer.com/v4/programs/${programId}/payees/${payeeId}/questionnaires`,
        ),
        {
          body: reqBody,
          method: 'post',
          axios_config: {
            headers: mockHeaders,
          },
        },
      );

      expect(result).toEqual([null, null]);
      expect(error).toBeNull();
    });
  });

  describe('getPayeeExtendedDetails', () => {
    const programId = 'program-123';
    const payeeId = 'payee-456';
    const purpose = BeneficiaryRegistrationPurpose.PAYIN;
    const mockHeaders = { Authorization: 'Bearer test-token' };
    const mockResponse = { details: { name: 'John Doe', address: '123 Street' } };

    it('should call makeApiCall with correct parameters and return extended details', async () => {
      const makeApiCallSpy = jest.spyOn(service, 'makeApiCall').mockResolvedValue(mockResponse as any);

      jest.spyOn(service as any, 'withToken').mockImplementation(async (fn: any) => {
        return [await fn(mockHeaders), null];
      });

      const [result, error] = await service.getPayeeExtendedDetails(programId, payeeId, purpose);

      expect(makeApiCallSpy).toHaveBeenCalledWith(
        expect.stringContaining(
          `https://api.payoneer.com/v4/programs/${programId}/payees/${payeeId}/details?show_payout_method_details=true`,
        ),
        {
          method: 'get',
          axios_config: {
            headers: mockHeaders,
          },
        },
      );

      expect(result).toEqual(mockResponse);
      expect(error).toBeNull();
    });
  });

  describe('getBankFieldsForPayee', () => {
    const programId = 'program-123';
    const input: payoneer.GetBankFieldsForPayeeInput = {
      currency: payoneer.Currency.AED,
      country: payoneer.Country.AE,
      payeeType: payoneer.PayeeType.COMPANY,
    };

    const mockHeaders = { Authorization: 'Bearer test-token' };
    const mockResponse = {
      fields: [
        {
          name: 'bank_account',
          required: true,
        },
      ],
    };

    it('should call makeApiCall with correct URL and return bank fields', async () => {
      const makeApiCallSpy = jest.spyOn(service, 'makeApiCall').mockResolvedValue(mockResponse as any);

      jest.spyOn(service as any, 'withToken').mockImplementation(async (fn: any) => {
        return [await fn(mockHeaders), null];
      });

      const [result, error] = await service.getBankFieldsForPayee(input, programId);

      const expectedUrl = `https://api.payoneer.com/v4/programs/${programId}/payout-methods/bank/account-types/${input.payeeType}/countries/${input.country}/currencies/${input.currency}`;

      expect(makeApiCallSpy).toHaveBeenCalledWith(expectedUrl, {
        method: 'get',
        axios_config: { headers: mockHeaders },
      });

      expect(result).toEqual(mockResponse);
      expect(error).toBeNull();
    });
  });
  describe('queryProgramBalanceV4', () => {
    const programId = 'program-123';
    const mockHeaders = { Authorization: 'Bearer test-token' };
    const mockResponse = {
      balances: [
        {
          currency: 'USD',
          amount: 1000,
        },
      ],
    };

    it('should call makeApiCall with correct URL and return balances', async () => {
      const makeApiCallSpy = jest.spyOn(service, 'makeApiCall').mockResolvedValue(mockResponse as any);

      jest.spyOn(service as any, 'withToken').mockImplementation(async (fn: any) => {
        return [await fn(mockHeaders), null];
      });

      const [result, error] = await service.queryProgramBalanceV4(programId);

      const expectedUrl = `https://api.payoneer.com/v4/programs/${programId}/balance`;

      expect(makeApiCallSpy).toHaveBeenCalledWith(expectedUrl, {
        method: 'get',
        axios_config: { headers: mockHeaders },
      });

      expect(result).toEqual(mockResponse);
      expect(error).toBeNull();
    });
  });
  describe('transferFundsV4', () => {
    const programId = 'program-123';
    const mockHeaders = { Authorization: 'Bearer test-token' };
    const requestBody = {
      source_token: 'source-token',
      target_token: 'target-token',
      amount: 200,
      currency: 'USD',
      target_partner_id: 'target-partner-id',
      description: 'test description',
    };
    const mockResponse = {
      transfer_id: 'transfer-456',
      status: 'SUCCESS',
    };

    it('should call makeApiCall with correct URL and body and return response', async () => {
      const makeApiCallSpy = jest.spyOn(service, 'makeApiCall').mockResolvedValue(mockResponse as any);

      jest.spyOn(service as any, 'withToken').mockImplementation(async (fn: any) => {
        return [await fn(mockHeaders), null];
      });

      const [result, error] = await service.transferFundsV4(requestBody, programId);

      const expectedUrl = `https://api.payoneer.com/v4/programs/${programId}/transfer`;

      expect(makeApiCallSpy).toHaveBeenCalledWith(expectedUrl, {
        body: requestBody,
        method: 'post',
        axios_config: { headers: mockHeaders },
      });

      expect(result).toEqual(mockResponse);
      expect(error).toBeNull();
    });
  });

  describe('getKyc', () => {
    it('should fetch KYC details for given program and payee ID', async () => {
      // First, mock token generation
      jest
        .spyOn(service, 'makeApiCall')
        .mockResolvedValueOnce({
          statusCode: HttpStatus.OK,
          data: { access_token: 'test-token', expires_in: 3600 },
          error: null,
        })
        .mockResolvedValueOnce({
          statusCode: HttpStatus.OK,
          data: { kyc_status: 'PENDING', fields: [] },
          error: null,
        });

      const [result, error] = await service.getKyc('program123', 'payee456');

      expect(service.makeApiCall).toHaveBeenCalledTimes(2);
      expect(service.makeApiCall).toHaveBeenLastCalledWith(
        'https://api.payoneer.com/v4/programs/program123/payees/payee456/kyc',
        {
          method: HttpMethod.Post,
          axios_config: {
            headers: {
              Authorization: 'Bearer test-token',
            },
          },
        },
      );

      expect(result).toEqual({ kyc_status: 'PENDING', fields: [] });
      expect(error).toBeNull();
    });
  });

  describe('editTransferMethod', () => {
    it('should edit transfer method for a payee', async () => {
      // Mock token generation
      jest
        .spyOn(service, 'makeApiCall')
        .mockResolvedValueOnce({
          statusCode: HttpStatus.OK,
          data: { access_token: 'test-token', expires_in: 3600 },
          error: null,
        })
        .mockResolvedValueOnce({
          statusCode: HttpStatus.OK,
          data: { status: 'UPDATED' },
          error: null,
        });

      const requestPayload: RegisterPayeePayoutMethod = {
        type: PayoutMethodType.BANK,
        bank_account_type: BankAccountTypeForPayoneerGBT.Company,
        country: 'US',
        currency: 'USD',
        bank_field_details: {
          length: 10,
          pop: function (): Record<string, string> {
            throw new Error('Function not implemented.');
          },
          push: function (...items: Record<string, string>[]): number {
            throw new Error('Function not implemented.');
          },
          concat: function (...items: ConcatArray<Record<string, string>>[]): Record<string, string>[] {
            throw new Error('Function not implemented.');
          },
          join: function (separator?: string): string {
            throw new Error('Function not implemented.');
          },
          reverse: function (): Record<string, string>[] {
            throw new Error('Function not implemented.');
          },
          shift: function (): Record<string, string> {
            throw new Error('Function not implemented.');
          },
          slice: function (start?: number, end?: number): Record<string, string>[] {
            throw new Error('Function not implemented.');
          },
          sort: function (
            compareFn?: (a: Record<string, string>, b: Record<string, string>) => number,
          ): Record<string, string>[] {
            throw new Error('Function not implemented.');
          },
          splice: function (start: number, deleteCount?: number): Record<string, string>[] {
            throw new Error('Function not implemented.');
          },
          unshift: function (...items: Record<string, string>[]): number {
            throw new Error('Function not implemented.');
          },
          indexOf: function (searchElement: Record<string, string>, fromIndex?: number): number {
            throw new Error('Function not implemented.');
          },
          lastIndexOf: function (searchElement: Record<string, string>, fromIndex?: number): number {
            throw new Error('Function not implemented.');
          },
          every: function <S extends Record<string, string>>(
            predicate: (
              value: Record<string, string>,
              index: number,
              array: Record<string, string>[],
            ) => value is S,
            thisArg?: any,
          ): this is S[] {
            throw new Error('Function not implemented.');
          },
          some: function (
            predicate: (
              value: Record<string, string>,
              index: number,
              array: Record<string, string>[],
            ) => unknown,
            thisArg?: any,
          ): boolean {
            throw new Error('Function not implemented.');
          },
          forEach: function (
            callbackfn: (
              value: Record<string, string>,
              index: number,
              array: Record<string, string>[],
            ) => void,
            thisArg?: any,
          ): void {
            throw new Error('Function not implemented.');
          },
          map: function <U>(
            callbackfn: (value: Record<string, string>, index: number, array: Record<string, string>[]) => U,
            thisArg?: any,
          ): U[] {
            throw new Error('Function not implemented.');
          },
          filter: function <S extends Record<string, string>>(
            predicate: (
              value: Record<string, string>,
              index: number,
              array: Record<string, string>[],
            ) => value is S,
            thisArg?: any,
          ): S[] {
            throw new Error('Function not implemented.');
          },
          reduce: function (
            callbackfn: (
              previousValue: Record<string, string>,
              currentValue: Record<string, string>,
              currentIndex: number,
              array: Record<string, string>[],
            ) => Record<string, string>,
          ): Record<string, string> {
            throw new Error('Function not implemented.');
          },
          reduceRight: function (
            callbackfn: (
              previousValue: Record<string, string>,
              currentValue: Record<string, string>,
              currentIndex: number,
              array: Record<string, string>[],
            ) => Record<string, string>,
          ): Record<string, string> {
            throw new Error('Function not implemented.');
          },
          find: function <S extends Record<string, string>>(
            predicate: (
              value: Record<string, string>,
              index: number,
              obj: Record<string, string>[],
            ) => value is S,
            thisArg?: any,
          ): S {
            throw new Error('Function not implemented.');
          },
          findIndex: function (
            predicate: (
              value: Record<string, string>,
              index: number,
              obj: Record<string, string>[],
            ) => unknown,
            thisArg?: any,
          ): number {
            throw new Error('Function not implemented.');
          },
          fill: function (
            value: Record<string, string>,
            start?: number,
            end?: number,
          ): Record<string, string>[] {
            throw new Error('Function not implemented.');
          },
          copyWithin: function (target: number, start: number, end?: number): Record<string, string>[] {
            throw new Error('Function not implemented.');
          },
          entries: function (): ArrayIterator<[number, Record<string, string>]> {
            throw new Error('Function not implemented.');
          },
          keys: function (): ArrayIterator<number> {
            throw new Error('Function not implemented.');
          },
          values: function (): ArrayIterator<Record<string, string>> {
            throw new Error('Function not implemented.');
          },
          includes: function (searchElement: Record<string, string>, fromIndex?: number): boolean {
            throw new Error('Function not implemented.');
          },
          flatMap: function <U, This = undefined>(
            callback: (
              this: This,
              value: Record<string, string>,
              index: number,
              array: Record<string, string>[],
            ) => U | readonly U[],
            thisArg?: This,
          ): U[] {
            throw new Error('Function not implemented.');
          },
          flat: function <A, D extends number = 1>(this: A, depth?: D): FlatArray<A, D>[] {
            throw new Error('Function not implemented.');
          },
          [Symbol.iterator]: function (): ArrayIterator<Record<string, string>> {
            throw new Error('Function not implemented.');
          },
          [Symbol.unscopables]: undefined,
          at: function (index: number): Record<string, string> {
            throw new Error('Function not implemented.');
          },
        },
      };

      const [result, error] = await service.editTransferMethod(requestPayload, 'program123', 'payee456');

      expect(service.makeApiCall).toHaveBeenLastCalledWith(
        'https://api.payoneer.com/v4/programs/program123/payees/payee456/payout-methods',
        {
          body: requestPayload,
          method: HttpMethod.Post,
          axios_config: {
            headers: {
              Authorization: 'Bearer test-token',
            },
          },
        },
      );
    });
  });
});

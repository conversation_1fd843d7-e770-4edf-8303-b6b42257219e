import { AppConfigService } from '../../../../config/services/app.config.service';
import {
  AuthType,
  CreateApplicationTokenValues,
  HttpMethod,
  PayeeType,
  PaymentStatus,
  PayoneerAPINames,
  TokenType,
  Version,
} from '../../../../common/constants/enums';
import { BadRequestException, HttpStatus, Injectable, NotFoundException } from '@nestjs/common';
import { APIResponse } from '../../../../common/data-types/common.data-type';
import {
  AccessTokenDto,
  ChargeAccountByClientDebitRequest,
  ChargeAccountByClientDebitResponse,
  CommitResponse,
  CommitResponseAfterChallengeResponse,
  EditPayeeProfileInput,
  EditPayeeResponse,
  GetAccountBalanceOutput,
  GetFundingsAccountsResponse,
  GetKYCResponse,
  GetPayeeExtendedDetailsResponse,
  GetPaymentStatusResponse,
  IAccountBalanceResponse,
  ICheckAccountExistsResponse,
  ICreateApplicationTokenRequest,
  ICreateApplicationTokenResponse,
  ICreateRegistrationLinkResponse,
  IGetPayoutStatusResponse,
  IPayeeStatusResponse,
  ISubmitMassPayoutRequest,
  ISubmitMassPayoutResponse,
  ITransferFundsRequest,
  ITransferFundsResponse,
  MerchantChargeStatusResponse,
  RegisterPayeeFormatOutput,
  RegisterPayeeInput,
  RegisterPayeePayoutMethod,
  SubmitQuestionnaireInput,
  TransactionResult,
} from '../../../../common/data-types/payoneer-v4.data.types';
import { PayoneerBaseClient } from './payoneer-base-client.service';
import { HTTP_CONTENT_TYPE_JSON } from '../../../constants/keys';
import { payoneer } from '@skuad/proto-utils/dist/interface/proto/payoneer/payoneer';
import { ProgramTokensRepository } from '../../../repositories/program-token.repository';
import { BeneficiaryRegistrationPurpose } from '../../../../beneficiary/dtos/inputs/beneficiary.graphql.input';
import { LoggerService } from '../../../services/LoggerService';

export interface IPayoneerHeaders {
  Authorization: string;
  'Content-Type'?: string;
}

export interface ClientErrorV2 {
  error: string;
  errors: Record<any, any>;
}

const AccessTokenAPIs = [
  PayoneerAPINames.GetTransactions,
  PayoneerAPINames.GetFundingAccounts,
  PayoneerAPINames.GetAccountBalance,
  PayoneerAPINames.ChargeAccountByClientDebit,
  PayoneerAPINames.ChargeAccountByClientPartnerDebit,
  PayoneerAPINames.GetPaymentStatus,
  PayoneerAPINames.Commit,
  PayoneerAPINames.GetCommitResponseAfterChallenge,
  PayoneerAPINames.GetMerchantChargeStatus,
  PayoneerAPINames.ProcessWithdrawFund,
  PayoneerAPINames.GetEligibleBanks,
  PayoneerAPINames.SubmitCommitId,
];

@Injectable()
export class PayoneerHttpClient extends PayoneerBaseClient {
  constructor(
    protected readonly config: AppConfigService,
    protected readonly programTokenRepository: ProgramTokensRepository,
    private readonly _logger: LoggerService,
  ) {
    super(config, _logger);
    this.baseURI = this.config.getAPIBaseUrl();
  }

  // Wrapper to handle token generation around API calls
  private async withToken<T>(
    apiCall: (headers: IPayoneerHeaders) => Promise<APIResponse<T>>,
    apiCallName: PayoneerAPINames = null,
    clientId: string = null,
    purpose?: BeneficiaryRegistrationPurpose,
  ): Promise<[T, ClientErrorV2]> {
    let tokenResponse, tokenError;

    if (AccessTokenAPIs.includes(apiCallName)) {
      if (!clientId) {
        return [null, this.getErrorDetails(new BadRequestException('clientId is required'))];
      }
      const programToken = await this.programTokenRepository.getToken({
        tokenType: TokenType.AccessToken,
        clientId: clientId,
      });

      if (!programToken) {
        return [null, this.getErrorDetails(new NotFoundException('Program Token not found'))];
      }
      tokenResponse = programToken.accessToken;
    } else if (
      [
        PayoneerAPINames.RefreshAccessToken,
        PayoneerAPINames.GenerateAccessToken,
        PayoneerAPINames.RevokeAccessToken,
      ].includes(apiCallName)
    ) {
      let applicationId: string;
      if (
        [PayoneerAPINames.RefreshAccessToken, PayoneerAPINames.RevokeAccessToken].includes(apiCallName) &&
        clientId
      ) {
        const programToken = await this.programTokenRepository.getToken({
          tokenType: TokenType.AccessToken,
          clientId: clientId,
        });
        applicationId = programToken?.applicationId;
      }

      tokenResponse = this.config.getLoginAuthorizationToken(applicationId);
    } else {
      [tokenResponse, tokenError] = await this.fetchOrCreateApiToken();
    }

    if (tokenError) {
      return [null, tokenError];
    }

    let headers;
    switch (apiCallName) {
      case PayoneerAPINames.RefreshAccessToken:
      case PayoneerAPINames.GenerateAccessToken:
      case PayoneerAPINames.RevokeAccessToken: {
        headers = this.createReqHeaders(tokenResponse, AuthType.Basic);
        headers[HTTP_CONTENT_TYPE_JSON] = 'application/x-www-form-urlencoded';
        break;
      }
      default: {
        headers = this.createReqHeaders(tokenResponse);
      }
    }
    const apiResponse = await apiCall(headers);

    if (apiResponse.statusCode === HttpStatus.OK) {
      return [apiResponse.data, null];
    } else {
      return [null, this.getErrorDetails(apiResponse.error)];
    }
  }

  // ======================================================= V4 APIs =======================================================

  async fetchOrCreateApiToken(purpose?: BeneficiaryRegistrationPurpose): Promise<[string, ClientErrorV2]> {
    const req: ICreateApplicationTokenRequest = {
      grant_type: CreateApplicationTokenValues.ClientCredentials,
      scope: CreateApplicationTokenValues.Scope,
    };

    const response = await this.makeApiCall<ICreateApplicationTokenResponse>(
      this.getCreateOrRevokeApplicationTokenUrl(PayoneerAPINames.CreateApplicationToken, Version.V2),
      {
        body: req,
        method: HttpMethod.Post,
        axios_config: {
          headers: {
            ...this.createReqHeaders(this.config.getLoginAuthorizationToken(purpose), AuthType.Basic),
            [HTTP_CONTENT_TYPE_JSON]: 'application/x-www-form-urlencoded',
          },
        },
      },
    );

    if (response.statusCode === 200) {
      process.env.PayoneerApiToken = response.data.access_token;
      process.env.PayoneerApiTokenExpiryDate = new Date(
        Date.now() + response.data.expires_in * 1000,
      ).toISOString();
      return [process.env.PayoneerApiToken, null];
    } else {
      return [null, this.getErrorDetails(response.error)];
    }
  }

  // V4 Payee Onboarding APIs
  async createRegistrationLinkV4(
    req: Record<string, any>,
    programId: string,
    purpose?: BeneficiaryRegistrationPurpose,
  ): Promise<[ICreateRegistrationLinkResponse, ClientErrorV2]> {
    return this.withToken(
      async (headers) => {
        return this.makeApiCall<ICreateRegistrationLinkResponse>(
          this.getApiUrl(PayoneerAPINames.CreateRegistrationLink, Version.V4, {
            program_id: programId,
          }),
          {
            body: req,
            method: HttpMethod.Post,
            axios_config: { headers },
          },
        );
      },
      PayoneerAPINames.CreateRegistrationLink,
      null,
      purpose,
    );
  }

  async getBankFieldsForPayee(
    input: payoneer.GetBankFieldsForPayeeInput,
    programId: string,
  ): Promise<[payoneer.GetBankFieldsForPayeeOutput, ClientErrorV2]> {
    return this.withToken(async (headers) => {
      return this.makeApiCall<payoneer.GetBankFieldsForPayeeOutput>(
        this.getApiUrl(PayoneerAPINames.DetermineRequiredBankingFields, Version.V4, {
          program_id: programId,
          ...(input as any),
        }),
        {
          method: HttpMethod.Get,
          axios_config: { headers },
        },
      );
    });
  }

  // Payee Management API
  async getPayeeStatus(payeeId: string, programId: string): Promise<[IPayeeStatusResponse, ClientErrorV2]> {
    return this.withToken(async (headers) => {
      return this.makeApiCall<IPayeeStatusResponse>(
        this.getApiUrl(PayoneerAPINames.GetPayeeStatus, Version.V4, {
          payee_id: payeeId,
          program_id: programId,
        }),
        {
          method: HttpMethod.Get,
          axios_config: { headers },
        },
      );
    });
  }

  // Payee exist API
  async checkPayeeAccountExists(
    payeeEmail: string,
    programId: string,
  ): Promise<[ICheckAccountExistsResponse, ClientErrorV2]> {
    return this.withToken(async (headers) => {
      return this.makeApiCall<ICheckAccountExistsResponse>(
        this.getApiUrl(PayoneerAPINames.CheckPayeeAccountExists, Version.V4, {
          payeeEmail,
          program_id: programId,
        }),
        {
          method: HttpMethod.Get,
          axios_config: { headers },
        },
      );
    });
  }

  // Payouts APIs
  async submitMassPayoutV4(
    req: ISubmitMassPayoutRequest,
    programId: string,
  ): Promise<[ISubmitMassPayoutResponse, ClientErrorV2]> {
    return this.withToken(async (headers) => {
      return this.makeApiCall<ISubmitMassPayoutResponse>(
        this.getApiUrl(PayoneerAPINames.SubmitMassPayout, Version.V4, {
          program_id: programId,
        }),
        {
          body: req,
          method: HttpMethod.Post,
          axios_config: { headers },
        },
      );
    });
  }

  async getPayoutStatus(
    clientReferenceId: string,
    programId: string,
  ): Promise<[IGetPayoutStatusResponse, ClientErrorV2]> {
    return this.withToken(async (headers) => {
      return this.makeApiCall<IGetPayoutStatusResponse>(
        this.getApiUrl(PayoneerAPINames.GetPayoutStatus, Version.V4, {
          client_reference_id: clientReferenceId,
          program_id: programId,
        }),
        {
          method: HttpMethod.Get,
          axios_config: { headers },
        },
      );
    });
  }

  async cancelPayoutV4(clientReferenceId: string, programId: string): Promise<[null, ClientErrorV2]> {
    return this.withToken(async (headers) => {
      return this.makeApiCall<null>(
        this.getApiUrl(PayoneerAPINames.CancelPayout, Version.V4, {
          client_reference_id: clientReferenceId,
          program_id: programId,
        }),
        {
          method: HttpMethod.Put,
          axios_config: { headers },
        },
      );
    });
  }

  //withdraw APIs

  async getEligibleBanks(clientId: string, accountId: string): Promise<[any, ClientErrorV2]> {
    return this.withToken(
      async (headers) => {
        return this.makeApiCall<null>(
          this.getApiUrl(PayoneerAPINames.GetEligibleBanks, Version.V4, {
            account_id: accountId,
          }),
          {
            body: {},
            method: HttpMethod.Get,
            axios_config: {
              headers: {
                ...headers,
              },
            },
          },
        );
      },
      PayoneerAPINames.GetEligibleBanks,
      clientId,
    );
  }

  async ProcessWithdrawFund(
    clientId: string,
    accountId: string,
    balanceId: string,
    req: any,
  ): Promise<[any, ClientErrorV2]> {
    return this.withToken(
      async (headers) => {
        return this.makeApiCall<any>(
          this.getApiUrl(PayoneerAPINames.ProcessWithdrawFund, Version.V4, {
            account_id: accountId,
            balance_id: balanceId,
          }),
          {
            body: req,
            method: HttpMethod.Post,
            axios_config: { headers },
          },
        );
      },
      PayoneerAPINames.ProcessWithdrawFund,
      clientId,
    );
  }

  async submitCommitId(clientId: string, accountId: string, commitId: string): Promise<[any, ClientErrorV2]> {
    return this.withToken(
      async (headers) => {
        return this.makeApiCall<null>(
          this.getApiUrl(PayoneerAPINames.SubmitCommitId, Version.V4, {
            account_id: accountId,
            commit_id: commitId,
          }),
          {
            method: HttpMethod.Put,
            axios_config: {
              headers: {
                ...headers,
              },
            },
          },
        );
      },
      PayoneerAPINames.GetEligibleBanks,
      clientId,
    );
  }

  // Utility APIs
  async queryProgramBalanceV4(programId: string): Promise<[IAccountBalanceResponse, ClientErrorV2]> {
    return this.withToken(async (headers) => {
      return this.makeApiCall<IAccountBalanceResponse>(
        this.getApiUrl(PayoneerAPINames.QueryProgramBalance, Version.V4, {
          program_id: programId,
        }),
        {
          method: HttpMethod.Get,
          axios_config: { headers },
        },
      );
    });
  }

  async transferFundsV4(
    req: ITransferFundsRequest,
    programId: string,
  ): Promise<[ITransferFundsResponse, ClientErrorV2]> {
    return this.withToken(async (headers) => {
      return this.makeApiCall<ITransferFundsResponse>(
        this.getApiUrl(PayoneerAPINames.TransferFunds, Version.V4, {
          program_id: programId,
        }),
        {
          body: req,
          method: HttpMethod.Post,
          axios_config: { headers },
        },
      );
    });
  }

  async getTransactions(
    clientId: string,
    accountId: string,
    fromTransactionDate: Date,
    toTransactionDate: Date,
    pageSize?: number,
    categoryId?: number,
    statusId?: number,
    lastTransactionId?: string,
    lastTransactionDate?: Date,
  ): Promise<[TransactionResult, ClientErrorV2]> {
    return this.withToken(
      async (headers) => {
        return this.makeApiCall<TransactionResult>(
          this.getApiUrl(PayoneerAPINames.GetTransactions, Version.V4, {
            program_id: null,
            account_id: accountId,
            from_transaction_date: fromTransactionDate
              ? fromTransactionDate?.toISOString().split('T')[0]
              : null,
            to_transaction_date: toTransactionDate ? toTransactionDate?.toISOString().split('T')[0] : null,
            page_size: pageSize || 100, // TODO Add this to the config.
            category_id: categoryId,
            status_id: statusId,
            last_transaction_id: lastTransactionId,
            last_transaction_date: lastTransactionDate?.toISOString().split('T')[0] || null,
            include_details: true,
          }),
          {
            method: HttpMethod.Get,
            axios_config: { headers },
          },
        );
      },
      PayoneerAPINames.GetTransactions,
      clientId,
    );
  }

  async refreshAccessToken(refreshToken: string, clientId: string, purpose: BeneficiaryRegistrationPurpose) {
    return this.withToken(
      async (headers) => {
        return this.makeApiCall<AccessTokenDto>(
          this.getCreateOrRevokeApplicationTokenUrl(PayoneerAPINames.RefreshAccessToken, Version.V2),
          {
            body: {
              grant_type: 'refresh_token',
              refresh_token: refreshToken,
            },
            method: HttpMethod.Post,
            axios_config: { headers },
          },
        );
      },
      PayoneerAPINames.RefreshAccessToken,
      clientId,
      purpose,
    );
  }

  async getFundingAccounts(
    clientId: string,
    accountId: string,
  ): Promise<[GetFundingsAccountsResponse, ClientErrorV2]> {
    return this.withToken(
      async (headers) => {
        return this.makeApiCall<GetFundingsAccountsResponse>(
          this.getApiUrl(PayoneerAPINames.GetFundingAccounts, Version.V4, {
            program_id: null,
            account_id: accountId,
          }),
          {
            method: HttpMethod.Get,
            axios_config: { headers },
          },
        );
      },
      PayoneerAPINames.GetFundingAccounts,
      clientId,
    );
  }

  // ==================================== Payoneer GBT Apis ============================================================

  async getRegisterPayeeFormat(
    programId: string,
    payeeType: PayeeType,
    country: string,
    currency: string,
  ): Promise<[RegisterPayeeFormatOutput, ClientErrorV2]> {
    const [result, error] = await this.withToken(async (headers) => {
      return this.makeApiCall<RegisterPayeeFormatOutput>(
        this.getApiUrl(PayoneerAPINames.GetRegisterPayeeFormat, Version.V4, {
          program_id: programId,
          payee_type: payeeType,
          country: country == 'GB' ? 'UK' : country,
          currency: currency,
        }),
        {
          method: HttpMethod.Get,
          axios_config: { headers },
        },
      );
    }, PayoneerAPINames.GetRegisterPayeeFormat);

    if (result?.result?.payout_method?.country == 'UK') {
      result.result.payout_method.country = 'GB';
    }
    return [result, error];
  }

  async registerPayee(
    req: RegisterPayeeInput,
    programId: string,
    purpose?: BeneficiaryRegistrationPurpose,
  ): Promise<[EditPayeeResponse, ClientErrorV2]> {
    return this.withToken(
      async (headers) => {
        return this.makeApiCall<EditPayeeResponse>(
          this.getApiUrl(PayoneerAPINames.RegisterPayee, Version.V4, {
            program_id: programId,
          }),
          {
            body: req,
            method: HttpMethod.Post,
            axios_config: { headers },
          },
        );
      },
      PayoneerAPINames.RegisterPayee,
      null,
      purpose,
    );
  }

  async editPayeeProfile(
    req: EditPayeeProfileInput,
    programId: string,
    payeeId: string,
  ): Promise<[EditPayeeResponse, ClientErrorV2]> {
    return this.withToken(async (headers) => {
      return this.makeApiCall<EditPayeeResponse>(
        this.getApiUrl(PayoneerAPINames.EditProfile, Version.V4, {
          program_id: programId,
          payee_id: payeeId,
        }),
        {
          body: req,
          method: HttpMethod.Put,
          axios_config: { headers },
        },
      );
    }, PayoneerAPINames.EditProfile);
  }

  async editTransferMethod(
    req: RegisterPayeePayoutMethod,
    programId: string,
    payeeId: string,
  ): Promise<[EditPayeeResponse, ClientErrorV2]> {
    return this.withToken(async (headers) => {
      return this.makeApiCall<EditPayeeResponse>(
        this.getApiUrl(PayoneerAPINames.EditTransferMethod, Version.V4, {
          program_id: programId,
          payee_id: payeeId,
        }),
        {
          body: req,
          method: HttpMethod.Post,
          axios_config: { headers },
        },
      );
    }, PayoneerAPINames.EditTransferMethod);
  }

  async getKyc(programId: string, payeeId: string): Promise<[GetKYCResponse, ClientErrorV2]> {
    return this.withToken(async (headers) => {
      return this.makeApiCall<GetKYCResponse>(
        this.getApiUrl(PayoneerAPINames.GetKYC, Version.V4, {
          program_id: programId,
          payee_id: payeeId,
        }),
        {
          method: HttpMethod.Post,
          axios_config: { headers },
        },
      );
    }, PayoneerAPINames.GetKYC);
  }

  async submitQuestionnaires(
    req: SubmitQuestionnaireInput,
    programId: string,
    payeeId: string,
  ): Promise<[null, ClientErrorV2]> {
    return this.withToken(async (headers) => {
      return this.makeApiCall<null>(
        this.getApiUrl(PayoneerAPINames.SubmitQuestionnaires, Version.V4, {
          program_id: programId,
          payee_id: payeeId,
        }),
        {
          body: req,
          method: HttpMethod.Post,
          axios_config: { headers },
        },
      );
    }, PayoneerAPINames.SubmitQuestionnaires);
  }

  async getPayeeExtendedDetails(
    programId: string,
    payeeId: string,
    purpose?: BeneficiaryRegistrationPurpose,
  ): Promise<[GetPayeeExtendedDetailsResponse, ClientErrorV2]> {
    return this.withToken(
      async (headers) => {
        return this.makeApiCall<GetPayeeExtendedDetailsResponse>(
          this.getApiUrl(PayoneerAPINames.GetPayeeDetailsExtended, Version.V4, {
            program_id: programId,
            payee_id: payeeId,
          }),
          {
            method: HttpMethod.Get,
            axios_config: { headers },
          },
        );
      },
      PayoneerAPINames.GetPayeeDetailsExtended,
      null,
      purpose,
    );
  }

  getClientAccountCodeUrl(params: {
    redirectUrl: string;
    scope: string;
    state?: string;
    purpose: BeneficiaryRegistrationPurpose;
  }): string {
    return this.getCreateOrRevokeApplicationTokenUrl(
      PayoneerAPINames.CreateClientAccountCodeUrl,
      Version.V2,
      {
        redirectUrl: params.redirectUrl,
        scope: params.scope,
        state: params.state,
        program_id: null,
        purpose: params.purpose,
      },
    );
  }

  async generateAccessToken(
    code: string,
    redirectUrl: string,
    purpose: BeneficiaryRegistrationPurpose,
  ): Promise<[AccessTokenDto, ClientErrorV2]> {
    return this.withToken(
      async (headers) => {
        return this.makeApiCall<AccessTokenDto>(
          this.getCreateOrRevokeApplicationTokenUrl(PayoneerAPINames.GenerateAccessToken, Version.V2, {
            purpose,
          }),
          {
            body: {
              grant_type: 'authorization_code',
              code: code,
              redirect_uri: redirectUrl ?? this.config.getRedirectUri(),
            },
            method: HttpMethod.Post,
            axios_config: {
              headers: {
                ...headers,
                [HTTP_CONTENT_TYPE_JSON]: 'application/x-www-form-urlencoded',
              },
            },
          },
        );
      },
      PayoneerAPINames.GenerateAccessToken,
      undefined,
      purpose,
    );
  }

  async revokeClientAccessToken(
    accessToken: string,
    tokenTypeHint: TokenType,
    clientId: string,
    purpose: BeneficiaryRegistrationPurpose,
  ): Promise<[null, ClientErrorV2]> {
    return this.withToken(
      async (headers) => {
        return this.makeApiCall<null>(
          this.getCreateOrRevokeApplicationTokenUrl(PayoneerAPINames.RevokeAccessToken, Version.V2),
          {
            body: {
              token_type_hint: tokenTypeHint?.toLowerCase() ?? 'access_token',
              token: accessToken,
            },
            method: HttpMethod.Post,
            axios_config: {
              headers: {
                ...headers,
                [HTTP_CONTENT_TYPE_JSON]: 'application/x-www-form-urlencoded',
              },
            },
          },
        );
      },
      PayoneerAPINames.RevokeAccessToken,
      clientId,
      purpose,
    );
  }

  async getAccountBalances(
    clientId: string,
    accountId: string,
  ): Promise<[GetAccountBalanceOutput, ClientErrorV2]> {
    return this.withToken(
      async (headers) => {
        return this.makeApiCall<null>(
          this.getApiUrl(PayoneerAPINames.GetAccountBalance, Version.V4, {
            account_id: accountId,
          }),
          {
            body: {},
            method: HttpMethod.Get,
            axios_config: {
              headers: {
                ...headers,
              },
            },
          },
        );
      },
      PayoneerAPINames.GetAccountBalance,
      clientId,
    );
  }

  async chargeAccountByClientDebit(
    input: ChargeAccountByClientDebitRequest,
    clientId: string,
    accountId: string,
    balanceId: string,
  ): Promise<[ChargeAccountByClientDebitResponse, ClientErrorV2]> {
    return this.withToken(
      async (headers) => {
        return this.makeApiCall<null>(
          this.getApiUrl(PayoneerAPINames.ChargeAccountByClientDebit, Version.V4, {
            account_id: accountId,
            balance_id: balanceId,
          }),
          {
            body: {
              ...(input ?? {}),
            },
            method: HttpMethod.Post,
            axios_config: {
              headers: {
                ...headers,
              },
            },
          },
        );
      },
      PayoneerAPINames.ChargeAccountByClientDebit,
      clientId,
    );
  }

  async chargeAccountByClientPartnerDebit(
    input: ChargeAccountByClientDebitRequest,
    clientId: string,
    accountId: string,
    balanceId: string,
  ): Promise<[ChargeAccountByClientDebitResponse, ClientErrorV2]> {
    return this.withToken(
      async (headers) => {
        return this.makeApiCall<null>(
          this.getApiUrl(PayoneerAPINames.ChargeAccountByClientPartnerDebit, Version.V4, {
            account_id: accountId,
            balance_id: balanceId,
          }),
          {
            body: {
              ...(input ?? {}),
            },
            method: HttpMethod.Post,
            axios_config: {
              headers: {
                ...headers,
              },
            },
          },
        );
      },
      PayoneerAPINames.ChargeAccountByClientPartnerDebit,
      clientId,
    );
  }

  async getPaymentStatus(
    clientId: string,
    accountId: string,
    paymentId?: string,
    clientReferenceId?: string,
  ): Promise<[GetPaymentStatusResponse, ClientErrorV2]> {
    return this.withToken(
      async (headers) => {
        return this.makeApiCall<null>(
          this.getApiUrl(PayoneerAPINames.GetPaymentStatus, Version.V4, {
            account_id: accountId,
            payment_id: paymentId,
            client_reference_id: clientReferenceId,
          }),
          {
            body: {},
            method: HttpMethod.Get,
            axios_config: {
              headers: {
                ...headers,
              },
            },
          },
        );
      },
      PayoneerAPINames.GetPaymentStatus,
      clientId,
    );
  }

  async commit(
    clientId: string,
    accountId: string,
    commitId: string,
  ): Promise<[CommitResponse, ClientErrorV2]> {
    return this.withToken(
      async (headers) => {
        return this.makeApiCall<null>(
          this.getApiUrl(PayoneerAPINames.Commit, Version.V4, {
            account_id: accountId,
            commit_id: commitId,
          }),
          {
            body: {},
            method: HttpMethod.Put,
            axios_config: {
              headers: {
                ...headers,
              },
            },
          },
        );
      },
      PayoneerAPINames.Commit,
      clientId,
    );
  }

  async getCommitResponseAfterChallenge(
    clientId: string,
    responsePath: string,
  ): Promise<[CommitResponseAfterChallengeResponse, ClientErrorV2]> {
    return this.withToken(
      async (headers) => {
        return this.makeApiCall<null>(
          this.getApiUrl(PayoneerAPINames.GetCommitResponseAfterChallenge, Version.V4, {
            response_path: responsePath,
          }),
          {
            body: {},
            method: HttpMethod.Get,
            axios_config: {
              headers: {
                ...headers,
              },
            },
          },
        );
      },
      PayoneerAPINames.GetCommitResponseAfterChallenge,
      clientId,
    );
  }

  async cancelCharge(clientId: string, accountId: string, paymentId: string) {
    const [currentStatus, fetchError] = await this.getPaymentStatus(clientId, accountId, paymentId);
    if (fetchError) {
      return [null, fetchError];
    }

    if (currentStatus?.result?.status_description === PaymentStatus.completed) {
      return [null, new Error('Payment is already completed')];
    }

    if (currentStatus?.result?.status_description === PaymentStatus.canceled) {
      return [
        {
          result: {
            cancel_date: new Date(),
            cancelPaymentId: paymentId,
          },
        },
        null,
      ];
    }

    return this.withToken(
      async (headers) => {
        return this.makeApiCall<null>(
          this.getApiUrl(PayoneerAPINames.CancelCharge, Version.V4, {
            account_id: accountId,
            payment_id: paymentId,
          }),
          {
            body: {},
            method: HttpMethod.Put,
            axios_config: {
              headers: {
                ...headers,
              },
            },
          },
        );
      },
      PayoneerAPINames.CancelCharge,
      clientId,
    );
  }

  async getMerchantChargeStatus(
    clientId: string,
    programId: string,
    clientReferenceId: string,
    accountId: string,
  ): Promise<[MerchantChargeStatusResponse, ClientErrorV2]> {
    return this.withToken(
      async (headers) => {
        return this.makeApiCall<null>(
          this.getApiUrl(PayoneerAPINames.GetMerchantChargeStatus, Version.V4, {
            program_id: programId,
            client_reference_id: clientReferenceId,
            account_id: accountId,
          }),
          {
            body: {},
            method: HttpMethod.Get,
            axios_config: {
              headers: {
                ...headers,
              },
            },
          },
        );
      },
      PayoneerAPINames.GetMerchantChargeStatus,
      clientId,
    );
  }
}

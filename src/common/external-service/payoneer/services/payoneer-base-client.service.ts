import { AppConfigService } from '../../../../config/services/app.config.service';
import { AuthType, HttpMethod, Version } from '../../../constants/enums';
import { APIResponse, APIResponseDataType } from '../../../../common/data-types/common.data-type';
import { HttpStatus, Injectable } from '@nestjs/common';
import { PayoneerApiRouter } from '../helpers/payoneer-api-router.utils';
import { IOptions } from '../../../interfaces/options.interface';
import { HTTP_CONTENT_TYPE_JSON } from '../../../constants/keys';
import { IParams, IRefreshTokenParams } from '../../../interfaces/params.interface';
import { LoggerService } from '../../../services/LoggerService';
import axios from 'axios';

interface IPayoneerHeaders {
  Authorization: string;
  'Content-Type'?: string;
}

@Injectable()
export class PayoneerBaseClient {
  protected readonly apiRouter: PayoneerApiRouter;
  protected baseURI: string;
  protected authURI: string;

  constructor(
    protected readonly config: AppConfigService,
    protected readonly logger: LoggerService,
  ) {
    this.apiRouter = new PayoneerApiRouter(this.config);
    this.baseURI = this.config.getAPIBaseUrl();
    this.authURI = this.config.getAPIAuthUrl();
  }

  /**
   * Initialize the structure of the API response object.
   */
  protected initApiResponse<T>(): APIResponse<T> {
    return { statusCode: 200, data: {} as T, error: null };
  }

  /**
   * Handle errors during the API call and populate the API response accordingly.
   */
  protected handleError<T>(res: APIResponse<T>, error: any) {
    if (!error) {
      error = {
        message: 'Unknown error occurred',
      };
    }

    const errorDetails = this.getErrorDetails(error);
    this.logger.error({ message: errorDetails.error, stack: error?.stack });
    res.data = null;
    res.statusCode = error?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR;
    res.error = errorDetails.error;
  }

  /**
   * Set the success response with the data received from the API call.
   */
  protected setSuccessResponse<T>(res: APIResponse<T>, axiosRes: Record<string, any>) {
    res.statusCode = HttpStatus.OK;
    res.data = axiosRes?.data;
    res.error = null;
  }

  /**
   * Execute the HTTP request based on the provided options and return the result.
   * Applies the Strategy pattern to handle different HTTP methods.
   */
  private async executeRequest(url: string, options: IOptions): Promise<any> {
    const { method = HttpMethod.Get, axios_config = {} } = options;

    if (!axios_config?.headers?.[HTTP_CONTENT_TYPE_JSON]) {
      axios_config.headers = {
        ...axios_config?.headers,
        [HTTP_CONTENT_TYPE_JSON]: 'application/json',
      };
    }

    switch (method) {
      case HttpMethod.Post:
        return await axios.post(url, options.body, axios_config);
      case HttpMethod.Put:
        return await axios.put(url, options.body, axios_config);
      case HttpMethod.Delete:
        return await axios.delete(url, axios_config);
      case HttpMethod.Patch:
        return await axios.patch(url, options.body, axios_config);
      default:
        axios_config.params = options?.params;
        return await axios.get(url, axios_config);
    }
  }

  /**
   * Make an API call using the specified URL and options, handling both success and error scenarios.
   * Includes logging of the request and response.
   */
  async makeApiCall<T extends APIResponseDataType>(
    url: string,
    options: IOptions = {},
  ): Promise<APIResponse<T>> {
    const apiResponse = this.initApiResponse<T>();
    try {
      const response = await this.executeRequest(url, options);
      if ([HttpStatus.OK, HttpStatus.CREATED, HttpStatus.ACCEPTED].includes(response?.status)) {
        this.setSuccessResponse<T>(apiResponse, response);
        this.logAPIReqResp(url, options, response);
      } else {
        this.handleError<T>(apiResponse, response.data);
        this.logAPIErrorReqResp(url, options, response);
      }
    } catch (error) {
      this.handleError<T>(apiResponse, error?.response?.data);
      this.logAPIErrorReqResp(url, options, { apiResponse, error });
    }

    return apiResponse;
  }

  /**
   * Log API request and response data.
   */

  private prepareLogData(apiName: string, apiRequestOptions: IOptions, responseBody: Record<string, any>) {
    const apiRequest = {
      headers: structuredClone(apiRequestOptions?.axios_config?.headers),
      params: apiRequestOptions?.params,
      body: structuredClone(apiRequestOptions?.body),
      method: apiRequestOptions?.method,
      url: responseBody?.config?.url,
    };
    const apiResponse = {
      status: responseBody?.status,
      statusText: responseBody?.statusText,
      data: structuredClone(responseBody?.data),
    };

    if (apiRequest?.headers?.Authorization) {
      apiRequest.headers.Authorization = 'Bearer <token>';
    }

    if (apiResponse?.data?.access_token) {
      apiResponse.data.access_token = '<token>';
    }

    if (apiResponse?.data?.refresh_token) {
      apiResponse.data.refresh_token = '<token>';
    }

    return {
      api: 'payoneer',
      apiName,
      apiRequest,
      apiResponse,
    };
  }

  protected logAPIReqResp(apiName: string, options: IOptions, resBody: Record<string, any>): void {
    this.logger.log(this.prepareLogData(apiName, options, resBody));
  }

  protected logAPIErrorReqResp(apiName: string, options: IOptions, resBody: Record<string, any>): void {
    this.logger.error(this.prepareLogData(apiName, options, resBody));
  }

  /**
   * Extract error details from an error object.
   */
  protected getErrorDetails(error: any) {
    const isDataError = (!!error && !!error?.code && error.code > 0) || error?.description;
    return {
      error: isDataError ? (error?.description ?? error) : (error?.message ?? error),
      errors: isDataError
        ? error?.['validation_mismatches'] || error?.description
        : (error?.message ?? error),
    };
  }

  /**
   * Helper method to construct a fully qualified API URL using the API router and base URI.
   */
  getApiUrl(apiName: string, version: Version, params?: IParams): string {
    const apiPath = this.apiRouter.getApiPath(version, apiName, params);
    return this.baseURI.concat(apiPath);
  }

  /**
   * Helper method to construct a fully qualified login API URL using the API router and login URI.
   */
  getCreateOrRevokeApplicationTokenUrl(
    apiName: string,
    version: Version,
    params?: IParams | IRefreshTokenParams,
  ): string {
    const apiPath = this.apiRouter.getApiPath(version, apiName, params);
    return this.authURI.concat(apiPath);
  }

  /**
   * Helper method to create request headers with optional authorization type.
   */
  createReqHeaders(payoneerApiToken: string, authType: AuthType = AuthType.Bearer): IPayoneerHeaders {
    return {
      Authorization: `${authType} ${payoneerApiToken}`,
    };
  }
}

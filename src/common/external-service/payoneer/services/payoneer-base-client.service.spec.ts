import { Test, TestingModule } from '@nestjs/testing';
import { PayoneerBaseClient } from './payoneer-base-client.service';
import { AppConfigService } from '../../../../config/services/app.config.service';
import { HttpStatus } from '@nestjs/common';
import { AuthType, HttpMethod, Version } from '../../../constants/enums';
import { AxiosResponse } from 'axios';
import { IParams } from '../../../interfaces/params.interface';
import axios from 'axios';
import { LoggerService } from '../../../../common/services/LoggerService';
import { IOptions } from 'src/common/interfaces/options.interface';

jest.mock('axios');

class TestPayoneerBaseClient extends PayoneerBaseClient {
  public testInitApiResponse<T>() {
    return this.initApiResponse<T>();
  }
  public testHandleError<T>(res: any, error: any) {
    return this.handleError<T>(res, error);
  }
  public testSetSuccessResponse<T>(res: any, axiosRes: any) {
    return this.setSuccessResponse<T>(res, axiosRes);
  }
  public testLogAPIReqResp(apiName: string, reqBody: any, resBody: any) {
    return this.logAPIReqResp(apiName, reqBody, resBody);
  }
  public testGetErrorDetails(error: any) {
    return this.getErrorDetails(error);
  }
}

describe('PayoneerBaseClient', () => {
  let service: TestPayoneerBaseClient;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TestPayoneerBaseClient,
        {
          provide: AppConfigService,
          useValue: {
            getAPIBaseUrl: jest.fn().mockReturnValue('https://api.example.com'),
            getAPIAuthUrl: jest.fn().mockReturnValue('https://auth.example.com'),
          },
        },
        {
          provide: LoggerService,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<TestPayoneerBaseClient>(TestPayoneerBaseClient);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('initApiResponse', () => {
    it('should initialize an API response', () => {
      const response = service.testInitApiResponse<any>();
      expect(response).toEqual({ statusCode: 200, data: {}, error: null });
    });
  });

  describe('handleError', () => {
    it('should handle error with status', () => {
      const res = service.testInitApiResponse<any>();
      const error = { response: { status: 500 }, message: 'fail' };

      service.testHandleError(res, error);

      expect(res.statusCode).toBe(500);
      expect(res.data).toBeNull();
      expect(res.error).toBeDefined();
    });

    it('should handle error without status', () => {
      const res = service.testInitApiResponse<any>();
      const error = { message: 'fail' };

      service.testHandleError(res, error);

      expect(res.statusCode).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(res.data).toBeNull();
      expect(res.error).toBeDefined();
    });
  });

  describe('setSuccessResponse', () => {
    it('should set success response', () => {
      const res = service.testInitApiResponse<any>();
      const axiosRes = { data: { foo: 'bar' } };

      service.testSetSuccessResponse(res, axiosRes);

      expect(res.statusCode).toBe(HttpStatus.OK);
      expect(res.data).toEqual({ foo: 'bar' });
      expect(res.error).toBeNull();
    });
  });

  describe('executeRequest', () => {
    it('should execute GET request', async () => {
      (axios.get as jest.Mock).mockResolvedValue({ data: 'foo', status: 200 });
      const result = await service['executeRequest']('http://example.com', { method: HttpMethod.Get });
      expect(result).toEqual({ data: 'foo', status: 200 });
      expect(axios.get).toHaveBeenCalled();
    });

    it('should execute POST request', async () => {
      (axios.post as jest.Mock).mockResolvedValue({ data: 'bar', status: 200 });
      const result = await service['executeRequest']('http://example.com', {
        method: HttpMethod.Post,
        body: { test: true },
      });
      expect(result).toEqual({ data: 'bar', status: 200 });
      expect(axios.post).toHaveBeenCalled();
    });

    it('should throw on failure', async () => {
      (axios.get as jest.Mock).mockRejectedValue(new Error('fail'));
      await expect(
        service['executeRequest']('http://example.com', { method: HttpMethod.Get }),
      ).rejects.toThrow('fail');
    });

    it('should execute PUT request', async () => {
      (axios.put as jest.Mock).mockResolvedValue({ data: 'put-ok', status: 200 });
      const result = await service['executeRequest']('http://example.com', {
        method: HttpMethod.Put,
        body: { key: 'value' },
      });
      expect(result).toEqual({ data: 'put-ok', status: 200 });
      expect(axios.put).toHaveBeenCalledWith('http://example.com', { key: 'value' }, expect.any(Object));
    });

    it('should execute DELETE request', async () => {
      (axios.delete as jest.Mock).mockResolvedValue({ data: 'deleted', status: 200 });
      const result = await service['executeRequest']('http://example.com', {
        method: HttpMethod.Delete,
      });
      expect(result).toEqual({ data: 'deleted', status: 200 });
      expect(axios.delete).toHaveBeenCalledWith('http://example.com', expect.any(Object));
    });

    it('should execute PATCH request', async () => {
      (axios.patch as jest.Mock).mockResolvedValue({ data: 'patched', status: 200 });
      const result = await service['executeRequest']('http://example.com', {
        method: HttpMethod.Patch,
        body: { update: true },
      });
      expect(result).toEqual({ data: 'patched', status: 200 });
      expect(axios.patch).toHaveBeenCalledWith('http://example.com', { update: true }, expect.any(Object));
    });
  });

  describe('prepareLogData', () => {
    it('should mask sensitive tokens in headers and response', () => {
      const apiName = 'TestAPI';
      const options: IOptions = {
        method: HttpMethod.Post,
        body: { key: 'value' },
        axios_config: {
          headers: {
            Authorization: 'Bearer secret_token',
          },
        },
        params: { id: 123 },
      };

      const responseBody = {
        status: 200,
        statusText: 'OK',
        config: { url: 'http://api.com/endpoint' },
        data: {
          result: 'success',
          access_token: 'real_token',
          refresh_token: 'refresh_secret',
        },
      };

      const result = service['prepareLogData'](apiName, options, responseBody);

      expect(result.api).toBe('payoneer');
      expect(result.apiName).toBe(apiName);

      expect(result.apiRequest.headers.Authorization).toBe('Bearer <token>');

      expect(result.apiResponse.data.access_token).toBe('<token>');

      expect(result.apiResponse.data.refresh_token).toBe('<token>');

      expect(result.apiResponse.status).toBe(200);
      expect(result.apiRequest.method).toBe(HttpMethod.Post);
    });

    it('should handle absence of headers and tokens gracefully', () => {
      const apiName = 'NoAuthAPI';
      const options: IOptions = {
        method: HttpMethod.Get,
        axios_config: {}, // no headers
      };

      const responseBody = {
        status: 404,
        statusText: 'Not Found',
        config: { url: 'http://api.com/notfound' },
        data: { error: 'Not Found' },
      };

      const result = service['prepareLogData'](apiName, options, responseBody);

      expect(result.api).toBe('payoneer');
      expect(result.apiName).toBe(apiName);
      expect(result.apiRequest.headers?.Authorization).toBeUndefined();
      expect(result.apiResponse.data.access_token).toBeUndefined();
      expect(result.apiResponse.data.refresh_token).toBeUndefined();
      expect(result.apiResponse.status).toBe(404);
    });
  });

  describe('makeApiCall', () => {
    it('should handle success', async () => {
      (axios.get as jest.Mock).mockResolvedValue({ data: { foo: 'bar' }, status: 200 });
      const res = await service.makeApiCall('http://example.com');
      expect(res.data).toEqual({ foo: 'bar' });
      expect(res.statusCode).toBe(HttpStatus.OK);
    });

    it('should handle failure', async () => {
      (axios.get as jest.Mock).mockRejectedValue({ response: { data: { message: 'fail' } } });
      const res = await service.makeApiCall('http://example.com');
      expect(res.data).toBeNull();
      expect(res.statusCode).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
    });

    it('should handle non-2xx response and log API error', async () => {
      const response = {
        status: 400,
        data: { message: 'Bad Request' },
      };

      jest.spyOn(service as any, 'executeRequest').mockResolvedValue(response);
      const handleErrorSpy = jest.spyOn(service as any, 'handleError');
      const logErrorSpy = jest.spyOn(service as any, 'logAPIErrorReqResp');

      const result = await service.makeApiCall('http://api.com/bad-request');

      expect(result.statusCode).toBe(HttpStatus.INTERNAL_SERVER_ERROR); // because handleError sets this
      expect(handleErrorSpy).toHaveBeenCalledWith(expect.any(Object), response.data);
      expect(logErrorSpy).toHaveBeenCalledWith('http://api.com/bad-request', {}, response);
    });
  });

  describe('getApiUrl', () => {
    it('should return correct API URL', () => {
      jest.spyOn(service['apiRouter'], 'getApiPath').mockReturnValue('/v4/foo');
      const result = service.getApiUrl('TestAPI', Version.V4, {});
      expect(result).toBe('https://api.example.com/v4/foo');
    });
  });

  describe('getCreateOrRevokeApplicationTokenUrl', () => {
    it('should return correct Auth URL', () => {
      jest.spyOn(service['apiRouter'], 'getApiPath').mockReturnValue('/v4/auth');
      const result = service.getCreateOrRevokeApplicationTokenUrl('TestAPI', Version.V4, {});
      expect(result).toBe('https://auth.example.com/v4/auth');
    });
  });

  describe('createReqHeaders', () => {
    it('should return Bearer header', () => {
      const res = service.createReqHeaders('abc');
      expect(res.Authorization).toBe('Bearer abc');
    });

    it('should return Basic header', () => {
      const res = service.createReqHeaders('abc', AuthType.Basic);
      expect(res.Authorization).toBe('Basic abc');
    });
  });
  describe('getErrorDetails', () => {
    it('should handle error with code and description', () => {
      const error = {
        code: 400,
        description: 'Bad Request',
        validation_mismatches: ['field1', 'field2'],
      };
      const result = service.testGetErrorDetails(error);
      expect(result).toEqual({
        error: 'Bad Request',
        errors: ['field1', 'field2'],
      });
    });

    it('should handle non-object error', () => {
      const error = 'Simple error message';
      const result = service.testGetErrorDetails(error);
      expect(result).toEqual({
        error: 'Simple error message',
        errors: 'Simple error message',
      });
    });

    it('should handle non-object error', () => {
      const error = 'Simple error message';
      const result = service.testGetErrorDetails(error);
      expect(result).toEqual({
        error: 'Simple error message',
        errors: 'Simple error message',
      });
    });

    it('should handle error with code and description', () => {
      const error = {
        code: 400,
        description: 'Bad Request',
        validation_mismatches: ['field1', 'field2'],
      };
      const result = service.testGetErrorDetails(error);
      expect(result).toEqual({
        error: 'Bad Request',
        errors: ['field1', 'field2'],
      });
    });

    it('should handle error with description but no code', () => {
      const error = {
        description: 'Validation Failed',
      };
      const result = service.testGetErrorDetails(error);
      expect(result).toEqual({
        error: 'Validation Failed',
        errors: 'Validation Failed',
      });
    });

    it('should handle error with message only', () => {
      const error = {
        message: 'Something went wrong',
      };
      const result = service.testGetErrorDetails(error);
      expect(result).toEqual({
        error: 'Something went wrong',
        errors: 'Something went wrong',
      });
    });

    it('should handle empty error object', () => {
      const error = {};
      const result = service.testGetErrorDetails(error);
      expect(result).toEqual({
        error: error,
        errors: {},
      });
    });

    it('should handle non-object error', () => {
      const error = 'Simple error message';
      const result = service.testGetErrorDetails(error);
      expect(result).toEqual({
        error: 'Simple error message',
        errors: 'Simple error message',
      });
    });

    it('should handle null/undefined error', () => {
      const resultNull = service.testGetErrorDetails(null);
      expect(resultNull).toEqual({
        error: null,
        errors: null,
      });

      const resultUndefined = service.testGetErrorDetails(undefined);
      expect(resultUndefined).toEqual({
        error: undefined,
        errors: undefined,
      });
    });
  });
});

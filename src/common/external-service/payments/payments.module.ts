import { Module } from "@nestjs/common";

import { BeneficiaryGrpcClient } from "./services/beneficiary-grpc-client.service";
import { BeneficiaryGrpcClient as ExternalBeneficiaryGrpcClient } from '@skuad/proto-utils/dist/payments/beneficiary/providers/beneficiary.grpc.client';

import { PaymentRequestGrpcClient } from "./services/payment-request-grpc-client.service";
import { PaymentRequestGrpcClient as ExternalPaymentRequestGrpcClient } from '@skuad/proto-utils/dist/payments/payment-requests/providers/payment-requests.grpc.client';

import { UtilsGrpcClient } from "./services/utils-grpc-client.service";
import { UtilsGrpcClient as ExternalUtilsGrpcClient } from '@skuad/proto-utils/dist/payments/utils/providers/utils.grpc.client';

import { ClientGrpc } from "@nestjs/microservices";
import { createClient } from "./common/create-client-grpc.option";

@Module({
  imports: [],
  providers: [
    BeneficiaryGrpcClient,
    ExternalBeneficiaryGrpcClient,

    PaymentRequestGrpcClient,
    ExternalPaymentRequestGrpcClient,

    UtilsGrpcClient,
    ExternalUtilsGrpcClient,

    {
      provide: "PAYMENT_SERVICE",
      useFactory: (): ClientGrpc => {
        return createClient();
      },
    },
  ],
  exports: [
    BeneficiaryGrpcClient,
    PaymentRequestGrpcClient,
    UtilsGrpcClient,
  ],
})
export class PaymentGrpcClientModule { }

import { Test, TestingModule } from '@nestjs/testing';
import { PaymentRequestGrpcClient } from './payment-request-grpc-client.service';
import { PaymentRequestGrpcClient as GrpcClient } from '@skuad/proto-utils/dist/payments/payment-requests/providers/payment-requests.grpc.client';

describe('PaymentRequestGrpcClient', () => {
  let service: PaymentRequestGrpcClient;
  let mockGrpcClient: jest.Mocked<GrpcClient>;

  beforeEach(async () => {
    mockGrpcClient = {
      findMany: jest.fn(),
      updateOne: jest.fn(),
      updateMany: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaymentRequestGrpcClient,
        {
          provide: GrpcClient,
          useValue: mockGrpcClient,
        },
      ],
    }).compile();

    service = module.get<PaymentRequestGrpcClient>(PaymentRequestGrpcClient);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findMany', () => {
    it('should call client findMany with correct params', async () => {
      const queryDto = { id: '123' };
      const expectedResponse = { success: true };
      mockGrpcClient.findMany.mockResolvedValue(expectedResponse as any);

      const result = await service.findMany(queryDto as any);

      expect(mockGrpcClient.findMany).toHaveBeenCalledWith(queryDto);
      expect(result).toEqual(expectedResponse);
    });
  });

  describe('updateOne', () => {
    it('should call client updateOne with correct params', async () => {
      const updateDto = { id: '123', status: 'COMPLETED' };
      const expectedResponse = { success: true };
      mockGrpcClient.updateOne.mockResolvedValue(expectedResponse as any);

      const result = await service.updateOne(updateDto as any);

      expect(mockGrpcClient.updateOne).toHaveBeenCalledWith(updateDto);
      expect(result).toEqual(expectedResponse);
    });
  });

  describe('updateMany', () => {
    it('should call client updateMany with correct params', async () => {
      const updateDto = { ids: ['123', '456'], status: 'COMPLETED' };
      const expectedResponse = { success: true };
      mockGrpcClient.updateMany.mockResolvedValue(expectedResponse as any);

      const result = await service.updateMany(updateDto as any);

      expect(mockGrpcClient.updateMany).toHaveBeenCalledWith(updateDto);
      expect(result).toEqual(expectedResponse);
    });
  });
});

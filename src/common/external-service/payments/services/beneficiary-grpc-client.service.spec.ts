import { Test, TestingModule } from '@nestjs/testing';
import { BeneficiaryGrpcClient } from './beneficiary-grpc-client.service';
import { BeneficiaryGrpcClient as GrpcClient } from '@skuad/proto-utils/dist/payments/beneficiary/providers/beneficiary.grpc.client';
import { BadRequestException } from '@nestjs/common';
import { IsString } from 'class-validator';

class MockRequestDto {
  @IsString()
  testField: string;
}

describe('BeneficiaryGrpcClient', () => {
  let service: BeneficiaryGrpcClient;
  let grpcClient: GrpcClient;

  const mockGrpcResponse = { data: 'test' };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BeneficiaryGrpcClient,
        {
          provide: GrpcClient,
          useValue: {
            createOne: jest.fn().mockResolvedValue(mockGrpcResponse),
            updateOne: jest.fn().mockResolvedValue(mockGrpcResponse),
            deleteOne: jest.fn().mockResolvedValue(mockGrpcResponse),
            findOne: jest.fn().mockResolvedValue(mockGrpcResponse),
            fetchBankDiff: jest.fn().mockResolvedValue(mockGrpcResponse),
          },
        },
      ],
    }).compile();

    service = module.get<BeneficiaryGrpcClient>(BeneficiaryGrpcClient);
    grpcClient = module.get<GrpcClient>(GrpcClient);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createOne', () => {
    it('should call grpcClient.createOne with valid request', async () => {
      const validRequest = new MockRequestDto();
      validRequest.testField = 'test';
      const result = await service.createOne(validRequest as any);
      expect(grpcClient.createOne).toHaveBeenCalledWith(validRequest);
      expect(result).toEqual(mockGrpcResponse);
    });

    it('should throw BadRequestException for invalid request', async () => {
      const invalidRequest = new MockRequestDto();
      await expect(service.createOne(invalidRequest as any)).rejects.toThrow(BadRequestException);
    });
  });

  describe('updateOne', () => {
    const meta = { userId: '123' };

    it('should call grpcClient.updateOne with valid request and meta', async () => {
      const validRequest = new MockRequestDto();
      validRequest.testField = 'test';
      const result = await service.updateOne(validRequest as any, meta);
      expect(grpcClient.updateOne).toHaveBeenCalledWith(validRequest, meta);
      expect(result).toEqual(mockGrpcResponse);
    });

    it('should throw BadRequestException for invalid request', async () => {
      const invalidRequest = new MockRequestDto();
      await expect(service.updateOne(invalidRequest as any, meta)).rejects.toThrow(BadRequestException);
    });
  });

  describe('deleteOne', () => {
    it('should call grpcClient.deleteOne', async () => {
      const request = { id: '123' };
      const result = await service.deleteOne(request as any);
      expect(grpcClient.deleteOne).toHaveBeenCalledWith(request);
      expect(result).toEqual(mockGrpcResponse);
    });
  });

  describe('findOne', () => {
    it('should call grpcClient.findOne', async () => {
      const request = { id: '123' };
      const result = await service.findOne(request as any);
      expect(grpcClient.findOne).toHaveBeenCalledWith(request);
      expect(result).toEqual(mockGrpcResponse);
    });
  });

  describe('fetchBankDiff', () => {
    it('should call grpcClient.fetchBankDiff', async () => {
      const request = { bankId: '123' };
      const result = await service.fetchBankDiff(request as any);
      expect(grpcClient.fetchBankDiff).toHaveBeenCalledWith(request);
      expect(result).toEqual(mockGrpcResponse);
    });
  });
});

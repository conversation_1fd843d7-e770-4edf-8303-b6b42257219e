import { Test, TestingModule } from '@nestjs/testing';
import { UtilsGrpcClient } from './utils-grpc-client.service';
import { UtilsGrpcClient as GrpcClient } from '@skuad/proto-utils/dist/payments/utils/providers/utils.grpc.client';

describe('UtilsGrpcClient', () => {
  let service: UtilsGrpcClient;
  let grpcClient: jest.Mocked<GrpcClient>;

  beforeEach(async () => {
    grpcClient = {
      ping: jest.fn(),
      getPostalCodeValidationRegex: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UtilsGrpcClient,
        {
          provide: GrpcClient,
          useValue: grpcClient,
        },
      ],
    }).compile();

    service = module.get<UtilsGrpcClient>(UtilsGrpcClient);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('ping', () => {
    it('should call grpc client ping method', async () => {
      const mockResponse = { success: true };
      const mockRequest = { message: 'test' };
      grpcClient.ping.mockResolvedValue(mockResponse as any);

      const result = await service.ping(mockRequest);

      expect(grpcClient.ping).toHaveBeenCalledWith(mockRequest);
      expect(result).toEqual(mockResponse);
    });

    it('should handle ping error', async () => {
      const mockError = new Error('Ping failed');
      const mockRequest = { message: 'test' };
      grpcClient.ping.mockRejectedValue(mockError);

      await expect(service.ping(mockRequest)).rejects.toThrow(mockError);
    });
  });

  describe('getPostalCodeValidationRegex', () => {
    it('should call grpc client getPostalCodeValidationRegex method', async () => {
      const mockResponse = { success: true };
      const mockRequest = { countryCode: 'US' };
      grpcClient.getPostalCodeValidationRegex.mockResolvedValue(mockResponse as any);

      const result = await service.getPostalCodeValidationRegex(mockRequest);

      expect(grpcClient.getPostalCodeValidationRegex).toHaveBeenCalledWith(mockRequest);
      expect(result).toEqual(mockResponse);
    });

    it('should handle getPostalCodeValidationRegex error', async () => {
      const mockError = new Error('Validation failed');
      const mockRequest = { countryCode: 'US' };
      grpcClient.getPostalCodeValidationRegex.mockRejectedValue(mockError);

      await expect(service.getPostalCodeValidationRegex(mockRequest)).rejects.toThrow(mockError);
    });
  });
});

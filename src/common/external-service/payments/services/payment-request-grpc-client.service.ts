import {
  PaymentRequestQueryDto,
  PaymentRequestUpdateRequestDto,
} from '@skuad/proto-utils/dist/payments/payment-requests/types/types';

import { PaymentRequestGrpcClient as GrpcClient } from '@skuad/proto-utils/dist/payments/payment-requests/providers/payment-requests.grpc.client';
import { GrpcHttpResponse } from "../../../data-types/common.data-type";
import { Injectable } from "@nestjs/common";

@Injectable()
export class PaymentRequestGrpcClient {
  constructor(private readonly client: GrpcClient) { }

  async findMany(
    paymentRequestQueryDto: PaymentRequestQueryDto
  ): Promise<GrpcHttpResponse> {
    return this.client.findMany(paymentRequestQueryDto);
  }

  async updateOne(
    paymentRequestUpdateRequestDto: PaymentRequestUpdateRequestDto
  ): Promise<GrpcHttpResponse> {
    return this.client.updateOne(paymentRequestUpdateRequestDto);
  }

  async updateMany(
    paymentRequestUpdateRequestDto: PaymentRequestUpdateRequestDto
  ): Promise<GrpcHttpResponse> {
    return this.client.updateMany(paymentRequestUpdateRequestDto);
  }
}

import { BadRequestException, Injectable } from '@nestjs/common';
import {
  BeneficiaryBankDiffReqProtoDto,
  CreateBeneficiaryRequestProtoDto,
  DeleteBeneficiaryRequestProtoDto,
  FindBeneficiaryRequestProtoDto,
  UpdateBeneficiaryRequestProtoDto,
} from '@skuad/proto-utils/dist/payments/beneficiary/types/types';

import { BeneficiaryGrpcClient as GrpcClient } from '@skuad/proto-utils/dist/payments/beneficiary/providers/beneficiary.grpc.client';
import { GrpcHttpResponse } from '../../../data-types/common.data-type';
import { ValidationError } from 'class-validator/types/validation/ValidationError';
import { validateSync } from 'class-validator';

@Injectable()
export class BeneficiaryGrpcClient {
  constructor(private readonly client: GrpcClient) {}

  async createOne(protoReqDto: CreateBeneficiaryRequestProtoDto): Promise<GrpcHttpResponse> {
    this.throwErrorIfInvalidProto(protoReqDto);
    return this.client.createOne(protoReqDto);
  }

  async updateOne(
    protoReqDto: UpdateBeneficiaryRequestProtoDto,
    meta: Record<string, any>,
  ): Promise<GrpcHttpResponse> {
    this.throwErrorIfInvalidProto(protoReqDto);
    return this.client.updateOne(protoReqDto, meta);
  }

  async deleteOne(protoReqDto: DeleteBeneficiaryRequestProtoDto): Promise<GrpcHttpResponse> {
    return this.client.deleteOne(protoReqDto);
  }

  async findOne(protoReqDto: FindBeneficiaryRequestProtoDto): Promise<GrpcHttpResponse> {
    return this.client.findOne(protoReqDto);
  }

  async fetchBankDiff(protoReqDto: BeneficiaryBankDiffReqProtoDto): Promise<GrpcHttpResponse> {
    return this.client.fetchBankDiff(protoReqDto);
  }

  private validateReq(req: any): ValidationError[] {
    return validateSync(req, { forbidUnknownValues : false });
  }

  private throwErrorIfInvalidProto(req: any) {
    const errors = this.validateReq(req);
    if (errors.length > 0) throw new BadRequestException('client proto request validation failed');
  }
}

import { PingRequestProto, PostalCodeRequestProto } from '@skuad/proto-utils/dist/payments/utils/types/types';

import { UtilsGrpcClient as GrpcClient } from '@skuad/proto-utils/dist/payments/utils/providers/utils.grpc.client';
import { GrpcHttpResponse } from "../../../data-types/common.data-type";
import { Injectable } from "@nestjs/common";

@Injectable()
export class UtilsGrpcClient {
  constructor(private readonly client: GrpcClient) { }

  async ping(data: PingRequestProto): Promise<GrpcHttpResponse> {
    return this.client.ping(data);
  }

  async getPostalCodeValidationRegex(
    data: PostalCodeRequestProto
  ): Promise<GrpcHttpResponse> {
    return this.client.getPostalCodeValidationRegex(data);
  }
}

import { IClientProviderConfig } from '../data-types/common.data-type';
import { IsNotEmpty, IsUUID } from 'class-validator';
import { Expose } from 'class-transformer';

export class ClientConfigDto implements IClientProviderConfig {
  @IsNotEmpty()
  @IsUUID()
  @Expose()
  customer_id: string;

  @IsNotEmpty()
  @IsUUID()
  @Expose()
  contact_id: string;

  @IsNotEmpty()
  @Expose()
  bank_account_hash_id: string;

  @IsNotEmpty()
  @Expose()
  account_id: string;
}

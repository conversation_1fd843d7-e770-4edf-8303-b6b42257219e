import { validateSync } from 'class-validator';
import { CustomValidationError, DataValidatorUtils } from '../helpers/data-validator.utils';

export class BaseDto {
  isValid(): boolean {
    const errors = validateSync(this);
    if (errors.length > 0) {
      return false;
    } else {
      return true;
    }
  }

  validate(): CustomValidationError[] {
    return DataValidatorUtils.validate(this);
  }
}

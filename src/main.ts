import * as dotenv from 'dotenv';
dotenv.config();
import * as htSdk from '@hypertestco/node-sdk';

if (process.env.HYPT_SERVICEID) {
  htSdk.initialize({
    apiKey: '<your-api-key>',
    serviceId: process.env.HYPT_SERVICEID,
    serviceName: 'skuad:payoneer',
    exporterUrl: process.env.HYPT_URL,

    // serviceId: '5a1635a8-b336-4a8f-8908-616dffdfbfe2',
  });
}
const secrets = (process.env.secrets && JSON.parse(process.env.secrets)) || {};

(global as any)['Config'] = { ...secrets, services: {} };

Object.assign(global, {
  Config: {
    services: {},
  },
});

import * as bodyParser from 'body-parser';
import * as cookieParser from 'cookie-parser';

import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { ClientOptions, MicroserviceOptions, Server } from '@nestjs/microservices';
import { useContainer } from 'class-validator';
import { AppModule } from './app.module';
import { grpcServiceOptions } from './grpc.service.options';
import { AppConfigService } from './config/services/app.config.service';
import { AppDataSource } from './common/database/data-source';
import { runMigrations } from './common/helpers/utils';

async function bootstrap() {
  // todo: add grpc auth
  // todo: add health check endpoint
  const app = await NestFactory.create(AppModule);
  useContainer(app.select(AppModule), { fallbackOnErrors: true });
  app.use(bodyParser.json({ limit: '100mb' }));
  app.enableCors({
    origin: [/^(.*)/],
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    preflightContinue: false,
    optionsSuccessStatus: 200,
    credentials: true,
    exposedHeaders: 'Content-Disposition',
    allowedHeaders:
      'Origin,X-Requested-With,Content-Type,Accept,Authorization,authorization,X-Forwarded-for, X-Auth-Legal-Entity-ID, traceparent, x-is-test-only',
  });
  app.use(cookieParser());
  const appConfig = app.get<AppConfigService>(AppConfigService);

  app.connectMicroservice<MicroserviceOptions>(grpcServiceOptions(appConfig) as ClientOptions);

  const logger = new Logger('gRPC');
  runMigrations();
  await AppDataSource.initialize().then(async () => {
    app.listen(appConfig.getServicePort(), () =>
      logger.log(`HTTP server started on port ${appConfig.getServicePort()}`),
    );

    await app.startAllMicroservices();
    logger.log(`gRPC service started on port ${appConfig.getGrpcServicePort()}`);
  });
  htSdk.markAppAsReady();
}
bootstrap();

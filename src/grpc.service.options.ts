import * as path from 'path';
import { GrpcOptions, Transport } from '@nestjs/microservices';
import { AppConfigService } from './config/services/app.config.service';
import { addReflectionToGrpcConfig } from 'nestjs-grpc-reflection';

export const grpcServiceOptions = (config?: AppConfigService): GrpcOptions => {
  return addReflectionToGrpcConfig({
    transport: Transport.GRPC,
    options: {
      keepalive: {
        keepaliveTimeMs: parseInt(process.env.GRPC_KEEPALIVE_TIME_IN_MS) || 15000,
        keepaliveTimeoutMs: parseInt(process.env.GRPC_KEEPALIVE_TIMEOUT_IN_MS) || 5000,
        keepalivePermitWithoutCalls: 1,
        http2MaxPingsWithoutData: 10000,
        http2MinTimeBetweenPingsMs: 5000,
        http2MaxPingStrikes: 0,
      },
      url: '0.0.0.0'.concat(':').concat(config.getGrpcServicePort().toString()),
      package: 'payoneer',
      protoPath: [path.join(process.cwd(), 'node_modules/@skuad/proto-utils/src/proto/payoneer/payoneer.proto')],

      loader: {
        includeDirs: [path.join(process.cwd(), 'node_modules/@skuad/proto-utils/src/proto')],
        enums: String,
        json: true,
      },
    },
  });
};

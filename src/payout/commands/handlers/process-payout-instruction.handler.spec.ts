import { Test, TestingModule } from '@nestjs/testing';
import { ProcessPayoutInstructionHandler } from './process-payout-instruction.handler';
import { ProcessPayoutInstructionCommand } from '../impl/process-payout-instruction.command';
import { PaymentRequestService } from '../../services/payment-request.service';
import { PayoneerHttpClient } from '../../../common/external-service/payoneer/services/payoneer.http.client.service';
import {
  PaymentBeneficiary,
  PaymentRequestDto,
  PaymentRequestMeta,
  ProviderClientConfig,
} from '../../../payout/dtos/payment-request.dto';
import {
  Currency,
  PaymentRequestStatus,
  PaymentRequestType,
  PaymentType,
  PayoneerProvider,
} from '../../../common/constants/enums';
import { PaymentProcessorDto } from '../../dtos/payment-processor.dto';
import { PayoutInstructionBuilder } from '../../builders/payout.instruction.builder';
import { Logger } from '@nestjs/common';
import { ISubmitMassPayoutRequest } from '../../../common/data-types/payoneer-v4.data.types';

describe('ProcessPayoutInstructionHandler', () => {
  let handler: ProcessPayoutInstructionHandler;
  let paymentRequestService: jest.Mocked<PaymentRequestService>;
  let payoneerHttpClient: jest.Mocked<PayoneerHttpClient>;

  const mockPaymentRequestDto: PaymentRequestDto = {
    unique_request_id: 'req_123',
    amount: 100,
    provider: PayoneerProvider.Payoneer,
    validate: jest.fn().mockReturnValue([]),
    beneficiary_legal_entity_id: '123',
    beneficiary_payout_profile_id: '123',
    client_legal_entity_id: '123',
    client_provider_profile_id: '123',
    provider_system_reference_number: '123',
    currency: '123',
    sourceCurrency: Currency.AED,
    status: PaymentRequestStatus.Initiated,
    checksum: '',
    purpose_of_payment: '',
    reference_number: '',
    payment_date: '',
    beneficiary: new PaymentBeneficiary(),
    client_config: new ProviderClientConfig(),
    payment_type: PaymentType.Priority,
    is_valid: false,
    type: PaymentRequestType.PayIn,
    meta: new PaymentRequestMeta(),
    isValid: function (): boolean {
      throw new Error('Function not implemented.');
    },
  };

  const mockCommand = new ProcessPayoutInstructionCommand(
    [mockPaymentRequestDto],
    'program_123',
    PayoneerProvider.Payoneer,
  );

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProcessPayoutInstructionHandler,
        {
          provide: PaymentRequestService,
          useValue: {
            findOneAndUpdatePaymentRequest: jest.fn(),
          },
        },
        {
          provide: PayoneerHttpClient,
          useValue: {
            queryProgramBalanceV4: jest.fn(),
            submitMassPayoutV4: jest.fn(),
          },
        },
      ],
    }).compile();

    handler = module.get<ProcessPayoutInstructionHandler>(ProcessPayoutInstructionHandler);
    paymentRequestService = module.get(PaymentRequestService);
    payoneerHttpClient = module.get(PayoneerHttpClient);

    jest.spyOn(Logger.prototype, 'error').mockImplementation();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('execute', () => {
    it('should return empty results if no payment requests are provided', async () => {
      const emptyCommand = new ProcessPayoutInstructionCommand([], 'program_123', PayoneerProvider.Payoneer);
      const result = await handler.execute(emptyCommand);

      expect(result).toEqual({
        successfulPaymentRequests: [],
        failedPaymentRequests: [],
      });
    });

    it('should handle validation errors for payment requests', async () => {
      mockPaymentRequestDto.validate = jest.fn().mockReturnValue(['Invalid amount']);
      paymentRequestService.findOneAndUpdatePaymentRequest.mockResolvedValue(undefined);

      const result = await handler.execute(mockCommand);

      expect(result).toEqual({
        successfulPaymentRequests: [],
        failedPaymentRequests: [mockPaymentRequestDto],
      });
      expect(paymentRequestService.findOneAndUpdatePaymentRequest).toHaveBeenCalledWith(
        { unique_request_id: 'req_123' },
        expect.objectContaining({ status: PaymentRequestStatus.Failed }),
        PayoneerProvider.Payoneer,
      );
    });

    it('should handle balance retrieval error', async () => {
      payoneerHttpClient.queryProgramBalanceV4.mockResolvedValue([
        null,
        { error: 'Balance fetch failed', errors: [] },
      ]);
      paymentRequestService.findOneAndUpdatePaymentRequest.mockResolvedValue(undefined);

      const result = await handler.execute(mockCommand);

      expect(result).toEqual({
        successfulPaymentRequests: [],
        failedPaymentRequests: [mockPaymentRequestDto],
      });
      expect(paymentRequestService.findOneAndUpdatePaymentRequest).toHaveBeenCalled();
    });

    it('should handle payout submission errors', async () => {
      payoneerHttpClient.queryProgramBalanceV4.mockResolvedValue([
        {
          result: { balance: 200, currency: 'USD', fees_due: 0 },
          error: '1',
          error_description: '',
          error_details: {
            code: 0,
          },
        },
        null,
      ]);
      payoneerHttpClient.submitMassPayoutV4.mockResolvedValue([null, { error: 'Payout failed', errors: [] }]);
      paymentRequestService.findOneAndUpdatePaymentRequest.mockResolvedValue(undefined);
      jest.spyOn(PayoutInstructionBuilder.prototype, 'checkAndBuild').mockReturnValue({
        payload: {
          payments: [
            {
              client_reference_id: 'req_123',
              amount: 100,
              currency: 'USD',
              payee_id: '123',
              description: '123',
            },
          ],
        },
        error: null,
      });

      const result = await handler.execute(mockCommand);

      expect(result).toEqual({
        successfulPaymentRequests: [],
        failedPaymentRequests: [mockPaymentRequestDto],
      });
      expect(paymentRequestService.findOneAndUpdatePaymentRequest).toHaveBeenCalledWith(
        { unique_request_id: 'req_123' },
        expect.objectContaining({ status: PaymentRequestStatus.Failed }),
        PayoneerProvider.Payoneer,
      );
    });

    it('should return early if no validated payment requests', async () => {
      const command = new ProcessPayoutInstructionCommand([], 'program_123', PayoneerProvider.Payoneer);
      const result = await handler.execute(command);
      expect(result.successfulPaymentRequests).toEqual([]);
      expect(result.failedPaymentRequests).toEqual([]);
    });

    it('should return early if no payments after building payloads', async () => {
      mockPaymentRequestDto.validate = jest.fn().mockReturnValue([]);
      paymentRequestService.findOneAndUpdatePaymentRequest.mockResolvedValue(undefined);
      payoneerHttpClient.queryProgramBalanceV4.mockResolvedValue([
        {
          result: {
            balance: 1000,
            currency: 'USD',
            fees_due: 0,
          },
          error: '',
          error_description: '',
          error_details: {
            code: 0,
          },
        },
        null,
      ]);

      jest.spyOn(PayoutInstructionBuilder.prototype, 'checkAndBuild').mockReturnValue({
        payload: { payments: [] },
        error: null,
      });

      const result = await handler.execute(mockCommand);

      expect(result.successfulPaymentRequests).toEqual([]);
      expect(result.failedPaymentRequests).toEqual([mockPaymentRequestDto]);
    });

    it('should handle error thrown due to balanceError in queryProgramBalanceV4', async () => {
      mockPaymentRequestDto.validate = jest.fn().mockReturnValue([]);
      paymentRequestService.findOneAndUpdatePaymentRequest.mockResolvedValue(undefined);

      // Force balanceError to simulate error
      payoneerHttpClient.queryProgramBalanceV4.mockResolvedValue([
        null,
        { error: 'Simulated balance fetch failure', errors: [] },
      ]);

      const result = await handler.execute(mockCommand);

      expect(result).toEqual({
        successfulPaymentRequests: [],
        failedPaymentRequests: [mockPaymentRequestDto],
      });

      expect(paymentRequestService.findOneAndUpdatePaymentRequest).toHaveBeenCalledWith(
        { unique_request_id: 'req_123' },
        expect.objectContaining({ status: PaymentRequestStatus.Failed }),
        PayoneerProvider.Payoneer,
      );
    });

    it('should handle error thrown during payout instruction build and return early if no payments', async () => {
      mockPaymentRequestDto.validate = jest.fn().mockReturnValue([]);
      paymentRequestService.findOneAndUpdatePaymentRequest.mockResolvedValue(undefined);

      payoneerHttpClient.queryProgramBalanceV4.mockResolvedValue([
        {
          result: {
            balance: 1000,
            currency: 'USD',
            fees_due: 0,
          },
          error: '',
          error_description: '',
          error_details: {
            code: 0,
          },
        },
        null,
      ]);

      // Force builder to throw
      jest.spyOn(PayoutInstructionBuilder.prototype, 'checkAndBuild').mockImplementation(() => {
        throw new Error('build failed');
      });

      const spyLogger = jest.spyOn(Logger.prototype, 'error').mockImplementation();

      const result = await handler.execute(mockCommand);

      expect(result).toEqual({
        successfulPaymentRequests: [],
        failedPaymentRequests: [mockPaymentRequestDto],
      });

      expect(spyLogger).toHaveBeenCalledWith(
        expect.stringContaining(
          `Error building payout instruction for ${mockPaymentRequestDto.unique_request_id}: build failed`,
        ),
      );

      expect(paymentRequestService.findOneAndUpdatePaymentRequest).toHaveBeenCalledWith(
        { unique_request_id: 'req_123' },
        expect.objectContaining({ status: PaymentRequestStatus.Failed }),
        PayoneerProvider.Payoneer,
      );
    });

    it('should catch and handle exception thrown from validateAndPreparePaymentRequest', async () => {
      const errorMessage = 'unexpected validation error';
      const spyLogger = jest.spyOn(Logger.prototype, 'error').mockImplementation();

      jest.spyOn(handler, 'validateAndPreparePaymentRequest').mockRejectedValueOnce(new Error(errorMessage));

      paymentRequestService.findOneAndUpdatePaymentRequest.mockResolvedValue(undefined);

      const result = await handler.execute(mockCommand);

      expect(result).toEqual({
        successfulPaymentRequests: [],
        failedPaymentRequests: [mockPaymentRequestDto],
      });

      expect(spyLogger).toHaveBeenCalledWith(
        expect.stringContaining(
          `Error processing payment request ${mockPaymentRequestDto.unique_request_id}: ${errorMessage}`,
        ),
      );

      expect(paymentRequestService.findOneAndUpdatePaymentRequest).toHaveBeenCalledWith(
        { unique_request_id: 'req_123' },
        expect.objectContaining({ status: PaymentRequestStatus.Failed }),
        PayoneerProvider.Payoneer,
      );
    });

    it('should catch and log error if updatePaymentRequestStatus fails during insufficient balance handling', async () => {
      // Setup: query balance returns less than payment amount
      mockPaymentRequestDto.validate = jest.fn().mockReturnValue([]);
      payoneerHttpClient.queryProgramBalanceV4.mockResolvedValue([
        {
          result: {
            balance: 50, // lower than amount (100)
            currency: 'USD',
            fees_due: 0,
          },
          error: '',
          error_description: '',
          error_details: { code: 0 },
        },
        null,
      ]);

      // Spy and force updatePaymentRequestStatus to throw
      const updateError = new Error('DB update failed');
      jest.spyOn(handler, 'updatePaymentRequestStatus').mockRejectedValueOnce(updateError);

      const spyLogger = jest.spyOn(Logger.prototype, 'error').mockImplementation();

      const result = await handler.execute(mockCommand);

      expect(result.successfulPaymentRequests).toEqual([]);
      expect(result.failedPaymentRequests).toEqual([]); // because update failed, not pushed
      expect(spyLogger).toHaveBeenCalledWith(
        expect.stringContaining(
          `Error updating payment request ${mockPaymentRequestDto.unique_request_id}: DB update failed`,
        ),
      );
    });

    it('should mark requests as failed when balance is insufficient and update succeeds', async () => {
      // validate passes
      mockPaymentRequestDto.validate = jest.fn().mockReturnValue([]);

      // balance is less than the amount, so request goes to "remainingRequests"
      payoneerHttpClient.queryProgramBalanceV4.mockResolvedValue([
        {
          result: {
            balance: 50, // less than amount (100)
            currency: 'USD',
            fees_due: 0,
          },
          error: '',
          error_description: '',
          error_details: {
            code: 0,
          },
        },
        null,
      ]);

      // checkAndBuild returns a valid payment payload (won't be used)
      jest.spyOn(PayoutInstructionBuilder.prototype, 'checkAndBuild').mockReturnValue({
        payload: {
          payments: [],
        },
        error: null,
      });

      // ensure update status doesn't throw
      paymentRequestService.findOneAndUpdatePaymentRequest.mockResolvedValue(undefined);

      const result = await handler.execute(mockCommand);

      expect(result.successfulPaymentRequests).toEqual([]);
      expect(result.failedPaymentRequests).toEqual([mockPaymentRequestDto]); // This is the key assertion
      expect(paymentRequestService.findOneAndUpdatePaymentRequest).toHaveBeenCalledWith(
        { unique_request_id: 'req_123' },
        expect.objectContaining({ status: PaymentRequestStatus.Failed }),
        PayoneerProvider.Payoneer,
      );
    });

    it('should push to failedPaymentRequests if checkAndBuild returns error', async () => {
      mockPaymentRequestDto.validate = jest.fn().mockReturnValue([]);

      // mock balance sufficient
      payoneerHttpClient.queryProgramBalanceV4.mockResolvedValue([
        {
          result: { balance: 1000, currency: 'USD', fees_due: 0 },
          error: '',
          error_description: '',
          error_details: { code: 0 },
        },
        null,
      ]);

      // checkAndBuild returns an error
      const buildError = new Error('build-error');
      jest.spyOn(PayoutInstructionBuilder.prototype, 'checkAndBuild').mockReturnValue({
        payload: { payments: [] },
        error: buildError,
      });

      // make sure updatePaymentRequestStatus works
      paymentRequestService.findOneAndUpdatePaymentRequest.mockResolvedValue(undefined);

      const result = await handler.execute(mockCommand);

      expect(result.successfulPaymentRequests).toEqual([]);
      expect(result.failedPaymentRequests).toEqual([mockPaymentRequestDto]);

      expect(paymentRequestService.findOneAndUpdatePaymentRequest).toHaveBeenCalledWith(
        { unique_request_id: 'req_123' },
        expect.objectContaining({ status: PaymentRequestStatus.Failed }),
        PayoneerProvider.Payoneer,
      );
    });

    it('should catch and log error if updatePaymentRequestStatus fails during payout submission failure', async () => {
      mockPaymentRequestDto.validate = jest.fn().mockReturnValue([]);

      payoneerHttpClient.queryProgramBalanceV4.mockResolvedValue([
        {
          result: { balance: 1000, currency: 'USD', fees_due: 0 },
          error: '',
          error_description: '',
          error_details: { code: 0 },
        },
        null,
      ]);

      jest.spyOn(PayoutInstructionBuilder.prototype, 'checkAndBuild').mockReturnValue({
        payload: {
          payments: [
            {
              client_reference_id: 'req_123',
              amount: 100,
              currency: 'USD',
              payee_id: '123',
              description: 'desc',
            },
          ],
        },
        error: null,
      });

      payoneerHttpClient.submitMassPayoutV4.mockResolvedValue([
        null,
        { error: 'Simulated payout error', errors: [] },
      ]);

      // Force updatePaymentRequestStatus to throw inside catch block
      paymentRequestService.findOneAndUpdatePaymentRequest.mockRejectedValue(new Error('DB update failed'));

      const spyLogger = jest.spyOn(Logger.prototype, 'error').mockImplementation();

      const result = await handler.execute(mockCommand);

      expect(result.successfulPaymentRequests).toEqual([]);
      expect(result.failedPaymentRequests).toEqual([mockPaymentRequestDto]);

      expect(spyLogger).toHaveBeenCalledWith(
        expect.stringContaining('Failed to update payment request req_123: DB update failed'),
      );
    });

    it('should mark payment requests as completed on successful payout', async () => {
      mockPaymentRequestDto.validate = jest.fn().mockReturnValue([]);

      payoneerHttpClient.queryProgramBalanceV4.mockResolvedValue([
        {
          result: { balance: 1000, currency: 'USD', fees_due: 0 },
          error: '',
          error_description: '',
          error_details: { code: 0 },
        },
        null,
      ]);

      payoneerHttpClient.submitMassPayoutV4.mockResolvedValue([
        {
          result: '',
          error: '',
          error_description: '',
          error_details: {
            code: 0,
          },
        },
        null,
      ]);

      paymentRequestService.findOneAndUpdatePaymentRequest.mockResolvedValue(undefined);

      jest.spyOn(PayoutInstructionBuilder.prototype, 'checkAndBuild').mockReturnValue({
        payload: {
          payments: [
            {
              client_reference_id: 'req_123',
              amount: 100,
              currency: 'USD',
              payee_id: '123',
              description: '123',
            },
          ],
        },
        error: null,
      });

      const result = await handler.execute(mockCommand);

      // 🧠 Wait for map's inner promises to resolve
      await new Promise((r) => setImmediate(r));

      expect(result.successfulPaymentRequests).toEqual([mockPaymentRequestDto]);
      expect(result.failedPaymentRequests).toEqual([]);
      expect(paymentRequestService.findOneAndUpdatePaymentRequest).toHaveBeenCalledWith(
        { unique_request_id: 'req_123' },
        expect.objectContaining({ status: PaymentRequestStatus.Completed }),
        PayoneerProvider.Payoneer,
      );
    });

    it('should return empty result if paymentRequestDtos is null', async () => {
      const command = new ProcessPayoutInstructionCommand(
        null as any,
        'program_123',
        PayoneerProvider.Payoneer,
      );

      const result = await handler.execute(command);

      expect(result).toEqual({
        successfulPaymentRequests: [],
        failedPaymentRequests: [],
      });
    });

    it('should treat validateAndPreparePaymentRequest result with error as failure', async () => {
      mockPaymentRequestDto.validate = jest.fn().mockReturnValue([]);

      // Spy on method and force it to return error object
      jest.spyOn(handler, 'validateAndPreparePaymentRequest').mockResolvedValue({
        value: null,
        error: new Error('manual error'),
      });

      const result = await handler.execute(mockCommand);

      expect(result.successfulPaymentRequests).toEqual([]);
      expect(result.failedPaymentRequests).toEqual([mockPaymentRequestDto]);
    });

    it('should handle undefined provider field in payment request DTO', async () => {
      const dtoWithMissingProvider = {
        ...mockPaymentRequestDto,
        provider: undefined,
        validate: jest.fn().mockReturnValue(['missing provider']),
      };

      const command = new ProcessPayoutInstructionCommand(
        [dtoWithMissingProvider as any],
        'program_123',
        PayoneerProvider.Payoneer,
      );

      const result = await handler.execute(command);

      expect(result.successfulPaymentRequests).toEqual([]);
      expect(result.failedPaymentRequests).toEqual([dtoWithMissingProvider]);
      expect(paymentRequestService.findOneAndUpdatePaymentRequest).toHaveBeenCalledWith(
        { unique_request_id: 'req_123' },
        expect.objectContaining({ status: PaymentRequestStatus.Failed }),
        undefined, // provider
      );
    });
  });

  describe('selectPaymentRequestsWithinBalance', () => {
    it('should select all requests for PayoneerGBT provider', () => {
      const requests = [
        { ...mockPaymentRequestDto, amount: 100, isValid: jest.fn(), validate: jest.fn() },
        {
          ...mockPaymentRequestDto,
          unique_request_id: 'req_124',
          amount: 200,
          isValid: jest.fn(),
          validate: jest.fn(),
        },
      ];
      const result = handler.selectPaymentRequestsWithinBalance(requests, 150, PayoneerProvider.PayoneerGBT);

      expect(result).toEqual({
        selectedRequests: requests,
        remainingRequests: [],
      });
    });

    it('should select requests within balance for non-PayoneerGBT provider', () => {
      const requests = [
        { ...mockPaymentRequestDto, amount: 100, isValid: jest.fn(), validate: jest.fn() },
        {
          ...mockPaymentRequestDto,
          unique_request_id: 'req_124',
          amount: 200,
          isValid: jest.fn(),
          validate: jest.fn(),
        },
      ];
      const result = handler.selectPaymentRequestsWithinBalance(requests, 150, PayoneerProvider.Payoneer);

      expect(result).toEqual({
        selectedRequests: [requests[0]],
        remainingRequests: [requests[1]],
      });
    });
  });

  describe('validateAndPreparePaymentRequest', () => {
    it('should return error for invalid payment request', async () => {
      mockPaymentRequestDto.validate = jest.fn().mockReturnValue(['Invalid amount']);
      paymentRequestService.findOneAndUpdatePaymentRequest.mockResolvedValue(undefined);

      const result = await handler.validateAndPreparePaymentRequest(mockPaymentRequestDto);

      expect(result).toEqual({
        value: null,
        error: expect.any(Error),
      });
      expect(paymentRequestService.findOneAndUpdatePaymentRequest).toHaveBeenCalled();
    });

    it('should return success for valid payment request', async () => {
      mockPaymentRequestDto.validate = jest.fn().mockReturnValue([]);

      const result = await handler.validateAndPreparePaymentRequest(mockPaymentRequestDto);

      expect(result).toEqual({
        value: undefined,
        error: undefined,
      });
    });
    it('should catch error and update status when exception is thrown', async () => {
      // Force `toDto` or validate to throw
      const badDto = {
        ...mockPaymentRequestDto,
        unique_request_id: 'bad_id',
        validate: jest.fn().mockImplementation(() => {
          throw new Error('unexpected exception');
        }),
        isValid: jest.fn().mockImplementation(() => {
          throw new Error('unexpected exception');
        }),
        beneficiary: new PaymentBeneficiary(),
        client_config: new ProviderClientConfig(),
        payment_type: PaymentType.Priority,
        is_valid: false,
        type: PaymentRequestType.PayIn,
        meta: new PaymentRequestMeta(),
      };

      const errorMessage = 'unexpected exception';
      const spyLogger = jest.spyOn(Logger.prototype, 'error').mockImplementation();

      // Force the toDto method (or first line) to throw
      jest.spyOn<any, any>(handler, 'updatePaymentRequestStatus').mockResolvedValue(undefined);
      jest.spyOn(PaymentProcessorDto.prototype, 'setValidationErrors').mockImplementation(() => {
        throw new Error(errorMessage);
      });

      const result = await handler.validateAndPreparePaymentRequest(badDto);

      expect(result).toEqual({
        value: null,
        error: expect.any(Error),
      });

      expect(spyLogger).toHaveBeenCalledWith(
        expect.stringContaining(`Error in validateAndPreparePaymentRequest for bad_id: ${errorMessage}`),
      );
    });
  });

  describe('handleBalanceError', () => {
    it('should update all payment requests to failed status', async () => {
      paymentRequestService.findOneAndUpdatePaymentRequest.mockResolvedValue(undefined);

      await handler.handleBalanceError([mockPaymentRequestDto], [], new Error('Balance error'));

      expect(paymentRequestService.findOneAndUpdatePaymentRequest).toHaveBeenCalledWith(
        { unique_request_id: 'req_123' },
        expect.objectContaining({ status: PaymentRequestStatus.Failed }),
        PayoneerProvider.Payoneer,
      );
    });
  });

  describe('updatePaymentRequestStatus', () => {
    it('should update payment request status successfully', async () => {
      paymentRequestService.findOneAndUpdatePaymentRequest.mockResolvedValue(undefined);

      await handler.updatePaymentRequestStatus(
        mockPaymentRequestDto,
        PaymentRequestStatus.Completed,
        undefined,
        'ref_123',
        null,
        PayoneerProvider.Payoneer,
      );

      expect(paymentRequestService.findOneAndUpdatePaymentRequest).toHaveBeenCalledWith(
        { unique_request_id: 'req_123' },
        expect.objectContaining({
          status: PaymentRequestStatus.Completed,
          provider_system_reference_number: 'ref_123',
        }),
        PayoneerProvider.Payoneer,
      );
    });

    it('should log error on update failure', async () => {
      paymentRequestService.findOneAndUpdatePaymentRequest.mockRejectedValue(new Error('Update failed'));

      await handler.updatePaymentRequestStatus(
        mockPaymentRequestDto,
        PaymentRequestStatus.Failed,
        new Error('Some error'),
        null,
        null,
        PayoneerProvider.Payoneer,
      );

      expect(Logger.prototype.error).toHaveBeenCalled();
    });
  });

  describe('updatePaymentRequestStatus edge cases', () => {
    it('should handle case with no error or paymentError provided', async () => {
      paymentRequestService.findOneAndUpdatePaymentRequest.mockResolvedValue(undefined);
      await handler.updatePaymentRequestStatus(
        mockPaymentRequestDto,
        PaymentRequestStatus.Completed,
        undefined,
        'ref_456',
        undefined,
        PayoneerProvider.Payoneer,
      );

      expect(paymentRequestService.findOneAndUpdatePaymentRequest).toHaveBeenCalledWith(
        { unique_request_id: 'req_123' },
        expect.objectContaining({
          status: PaymentRequestStatus.Completed,
          provider_system_reference_number: 'ref_456',
          payment_error: undefined,
        }),
        PayoneerProvider.Payoneer,
      );
    });
  });

  describe('waitForPaymentProcessing', () => {
    it('should wait for specified time', async () => {
      const spy = jest.spyOn(global, 'setTimeout');
      await handler.waitForPaymentProcessing(100);
      expect(spy).toHaveBeenCalled();
    });
    it('should wait for default time', async () => {
      const spy = jest.spyOn(global, 'setTimeout');
      await handler.waitForPaymentProcessing();
      expect(spy).toHaveBeenCalled();
    });
  });
});

import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PayoneerHttpClient } from '../../../common/external-service/payoneer/services/payoneer.http.client.service';
import { PayoneerProvider, TokenType } from '../../../common/constants/enums';
import { Logger } from '@nestjs/common';
import { ProcessAutoDebitPaymentRequestCommand } from '../impl/process-auto-debit-payment-request.command';
import { ProgramTokensRepository } from '../../../common/repositories/program-token.repository';
import { ProgramCurrencyMappingRepository } from '../../../common/repositories/program-currency-mapping.repository';

@CommandHandler(ProcessAutoDebitPaymentRequestCommand)
export class ProcessAutoDebitPaymentRequestHandler
  implements ICommandHandler<ProcessAutoDebitPaymentRequestCommand>
{
  private readonly logger = new Logger(ProcessAutoDebitPaymentRequestHandler.name);

  constructor(
    private readonly client: PayoneerHttpClient,
    private readonly programTokenRepository: ProgramTokensRepository,
    private readonly programCurrencyMappingRepository: ProgramCurrencyMappingRepository,
  ) {}

  async execute(command: ProcessAutoDebitPaymentRequestCommand) {
    const { paymentRequestDto } = command;

    const amount = paymentRequestDto.amount;
    const currency = paymentRequestDto.currency;
    const clientId = paymentRequestDto.client_legal_entity_id;
    const balanceId = paymentRequestDto.meta.balanceId;

    try {
      const targetProgramId = await this.programCurrencyMappingRepository.getProgramIdUsingCurrency(
        currency,
        PayoneerProvider.PayoneerAutoDebit,
      );

      const clientAccount = await this.programTokenRepository.findOne({
        where: { clientId, tokenType: TokenType.AccessToken },
      });

      if (!clientAccount) {
        throw new Error('Client account not found');
      }

      const [balanceResp, balanceError] = await this.client.getAccountBalances(
        clientId,
        clientAccount.accountId,
      );

      if (balanceError) {
        throw new Error(balanceError.error);
      }
      const targetBalance = balanceResp.result.balances.items.find((balance) => balance.id === balanceId);

      if (!Number(targetBalance?.available_balance)) {
        throw new Error('Target currency balance is not sufficient');
      }

      const payload = {
        body: {
          client_reference_id: paymentRequestDto.unique_request_id,
          amount: Number(amount.toFixed(2)),
          currency: currency,
          description: `Payment order ${paymentRequestDto.unique_request_id}`,
          to: {
            type: 'partner',
            id: targetProgramId,
          },
        },
        clientId,
        clientAccountId: clientAccount.accountId,
        balanceId,
      };

      const [result, chargeError] = await this.client.chargeAccountByClientPartnerDebit(
        payload.body,
        clientId,
        clientAccount.accountId,
        balanceId,
      );

      if (chargeError) {
        throw new Error(chargeError.error);
      }

      this.logger.log('Charge account response:', result);
      return [payload, null];
    } catch (error) {
      this.logger.error(error);
      return [null, error];
    }
  }
}

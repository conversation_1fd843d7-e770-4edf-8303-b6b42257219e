import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PayoneerHttpClient } from '../../../common/external-service/payoneer/services/payoneer.http.client.service';
import { PaymentRequestStatus, PayoneerProvider } from '../../../common/constants/enums';
import { IPaymentRequestUpdate } from '../../../common/data-types/common.data-type';
import { Logger } from '@nestjs/common';
import { toDto } from '../../../common/helpers/utils';
import { PayoutInstructionBuilder } from '../../builders/payout.instruction.builder';
import { PaymentProcessorDto } from '../../dtos/payment-processor.dto';
import { PaymentRequestService } from '../../services/payment-request.service';
import { ProcessPayoutInstructionCommand } from '../impl/process-payout-instruction.command';
import {
  IAccountBalanceResponse,
  ISubmitMassPayoutRequest,
} from '../../../common/data-types/payoneer-v4.data.types';
import { PaymentRequestDto } from '../../../payout/dtos/payment-request.dto';

// Define types
type Result<T> = { value: T | null; error?: Error };

@CommandHandler(ProcessPayoutInstructionCommand)
export class ProcessPayoutInstructionHandler implements ICommandHandler<ProcessPayoutInstructionCommand> {
  private readonly logger = new Logger(ProcessPayoutInstructionHandler.name);

  constructor(
    private readonly service: PaymentRequestService,
    private readonly client: PayoneerHttpClient,
  ) {}

  async execute(command: ProcessPayoutInstructionCommand): Promise<{
    successfulPaymentRequests: PaymentRequestDto[];
    failedPaymentRequests: PaymentRequestDto[];
  }> {
    const { paymentRequestDtos, programId, provider } = command;
    const aggregatedPayload: ISubmitMassPayoutRequest = { payments: [] };
    const failedPaymentRequests: PaymentRequestDto[] = [];
    const validatedPaymentRequests: PaymentRequestDto[] = [];

    // Process each payment request individually with error handling
    await Promise.all(
      (paymentRequestDtos ?? []).map(async (paymentRequestDto) => {
        try {
          const result = await this.validateAndPreparePaymentRequest(paymentRequestDto);
          if (result?.error) {
            failedPaymentRequests.push(paymentRequestDto);
          } else {
            validatedPaymentRequests.push(paymentRequestDto);
          }
        } catch (error) {
          this.logger.error(
            `Error processing payment request ${paymentRequestDto.unique_request_id}: ${error.message}`,
          );
          failedPaymentRequests.push(paymentRequestDto);
          // Update the payment request status to 'Failed' in the database
          await this.updatePaymentRequestStatus(
            paymentRequestDto,
            PaymentRequestStatus.Failed,
            error,
            null,
            null,
            paymentRequestDto?.provider,
          );
        }
      }),
    );

    // No valid payment requests to process
    if (!validatedPaymentRequests?.length) {
      return {
        successfulPaymentRequests: [],
        failedPaymentRequests,
      };
    }

    // Get program balance
    let balanceResponse: IAccountBalanceResponse;
    try {
      const [balanceResp, balanceError] = await this.client.queryProgramBalanceV4(programId);
      if (balanceError) {
        throw new Error(balanceError.error);
      }
      balanceResponse = balanceResp;
    } catch (error) {
      // Handle balance retrieval error
      await this.handleBalanceError(validatedPaymentRequests, failedPaymentRequests, error);
      return {
        successfulPaymentRequests: [],
        failedPaymentRequests,
      };
    }

    let programBalance = balanceResponse.result.balance;

    // Select payment requests within the program balance
    const { selectedRequests, remainingRequests } = this.selectPaymentRequestsWithinBalance(
      validatedPaymentRequests,
      programBalance,
      provider,
    );

    // Update failed payment requests
    await Promise.all(
      remainingRequests.map(async (paymentRequestDto) => {
        try {
          await this.updatePaymentRequestStatus(
            paymentRequestDto,
            PaymentRequestStatus.Failed,
            new Error('Insufficient program balance for this payment request.'),
            null,
            null,
            paymentRequestDto?.provider,
          );
          failedPaymentRequests.push(paymentRequestDto);
        } catch (error) {
          this.logger.error(
            `Error updating payment request ${paymentRequestDto.unique_request_id}: ${error.message}`,
          );
        }
      }),
    );

    // Update the list of payment requests to process
    validatedPaymentRequests.length = 0;
    validatedPaymentRequests.push(...selectedRequests);

    // Build aggregated payload
    for (const paymentRequestDto of validatedPaymentRequests) {
      try {
        const buildResult = new PayoutInstructionBuilder(paymentRequestDto).checkAndBuild();
        if (buildResult.error) {
          await this.updatePaymentRequestStatus(
            paymentRequestDto,
            PaymentRequestStatus.Failed,
            buildResult.error,
            null,
            null,
            paymentRequestDto?.provider,
          );
          failedPaymentRequests.push(paymentRequestDto);
        } else {
          aggregatedPayload.payments.push(buildResult.payload.payments[0]);
        }
      } catch (error) {
        this.logger.error(
          `Error building payout instruction for ${paymentRequestDto.unique_request_id}: ${error.message}`,
        );
        await this.updatePaymentRequestStatus(
          paymentRequestDto,
          PaymentRequestStatus.Failed,
          error,
          null,
          null,
          paymentRequestDto?.provider,
        );
        failedPaymentRequests.push(paymentRequestDto);
      }
    }

    if (aggregatedPayload?.payments?.length === 0) {
      // No payments to process after building payloads
      return {
        successfulPaymentRequests: [],
        failedPaymentRequests,
      };
    }

    // Submit the aggregated payload
    try {
      const [, payoutErr] = await this.client.submitMassPayoutV4(aggregatedPayload, programId);
      if (payoutErr) {
        throw new Error(payoutErr.error);
      }
    } catch (error) {
      // Handle errors in the payout submission process
      await Promise.all(
        validatedPaymentRequests.map(async (paymentRequestDto) => {
          try {
            await this.updatePaymentRequestStatus(
              paymentRequestDto,
              PaymentRequestStatus.Failed,
              error,
              null,
              null,
              paymentRequestDto?.provider,
            );
            failedPaymentRequests.push(paymentRequestDto);
          } catch (updateError) {
            this.logger.error(
              `Error updating payment request ${paymentRequestDto.unique_request_id}: ${updateError.message}`,
            );
          }
        }),
      );
      return {
        successfulPaymentRequests: [],
        failedPaymentRequests,
      };
    }

    // Handle the response for each payment request
    const successfulPaymentRequests: PaymentRequestDto[] = [];
    validatedPaymentRequests.map(async (paymentRequestDto) => {
      await this.updatePaymentRequestStatus(
        paymentRequestDto,
        PaymentRequestStatus.Completed,
        undefined,
        paymentRequestDto.unique_request_id,
        null,
        paymentRequestDto?.provider,
      );
      successfulPaymentRequests.push(paymentRequestDto);
    });

    return {
      successfulPaymentRequests,
      failedPaymentRequests,
    };
  }

  async validateAndPreparePaymentRequest(paymentRequestDto: PaymentRequestDto): Promise<Result<void>> {
    try {
      const paymentProcessorDTO = toDto<PaymentProcessorDto>(PaymentProcessorDto, {
        unique_request_id: paymentRequestDto.unique_request_id,
      });

      // Validate payment request
      const validationErrors = paymentRequestDto.validate();
      if (validationErrors.length > 0) {
        paymentProcessorDTO.setValidationErrors(validationErrors);
        await this.updatePaymentRequestStatus(
          paymentRequestDto,
          PaymentRequestStatus.Failed,
          new Error('Validation errors'),
          undefined,
          paymentProcessorDTO.getError(),
          paymentRequestDto?.provider,
        );
        return { value: null, error: new Error('Validation errors') };
      }

      return { value: undefined };
    } catch (error) {
      this.logger.error(
        `Error in validateAndPreparePaymentRequest for ${paymentRequestDto.unique_request_id}: ${error.message}`,
      );
      await this.updatePaymentRequestStatus(
        paymentRequestDto,
        PaymentRequestStatus.Failed,
        error,
        null,
        null,
        paymentRequestDto?.provider,
      );
      return { value: null, error };
    }
  }

  selectPaymentRequestsWithinBalance(
    paymentRequests: PaymentRequestDto[],
    availableBalance: number,
    provider: PayoneerProvider,
  ): {
    selectedRequests: PaymentRequestDto[];
    remainingRequests: PaymentRequestDto[];
  } {
    if (provider === PayoneerProvider.PayoneerGBT) {
      return {
        selectedRequests: [...(paymentRequests ?? [])],
        remainingRequests: [],
      };
    }
    const selectedRequests: PaymentRequestDto[] = [];
    const remainingRequests: PaymentRequestDto[] = [];

    // Sort payment requests by amount (ascending)
    const sortedRequests = (paymentRequests?.slice() ?? []).sort((a, b) => a.amount - b.amount);

    let accumulatedAmount = 0;

    for (const request of sortedRequests) {
      if (accumulatedAmount + request.amount <= availableBalance) {
        selectedRequests.push(request);
        accumulatedAmount += request.amount;
      } else {
        remainingRequests.push(request);
      }
    }

    return { selectedRequests, remainingRequests };
  }

  async handleBalanceError(
    validatedRequests: PaymentRequestDto[],
    failedRequests: PaymentRequestDto[],
    error: Error,
  ): Promise<void> {
    await Promise.all(
      validatedRequests.map(async (paymentRequestDto) => {
        try {
          await this.updatePaymentRequestStatus(
            paymentRequestDto,
            PaymentRequestStatus.Failed,
            error,
            null,
            null,
            paymentRequestDto?.provider,
          );
          failedRequests.push(paymentRequestDto);
        } catch (updateError) {
          this.logger.error(
            `Error updating payment request ${paymentRequestDto.unique_request_id}: ${updateError.message}`,
          );
        }
      }),
    );
  }

  async updatePaymentRequestStatus(
    paymentRequestDto: PaymentRequestDto,
    status: PaymentRequestStatus,
    error?: Error,
    providerReferenceNumber?: string,
    paymentError?: any,
    provider?: string,
  ): Promise<void> {
    const paymentProcessorDTO = toDto<PaymentProcessorDto>(PaymentProcessorDto, {
      unique_request_id: paymentRequestDto.unique_request_id,
    });

    if (error) {
      paymentProcessorDTO.setPaymentCreateError(error);
    }

    const updateParams: IPaymentRequestUpdate = {
      status,
      provider_system_reference_number: providerReferenceNumber,
      payment_error: paymentError || paymentProcessorDTO.getError(),
    };

    try {
      await this.service.findOneAndUpdatePaymentRequest(
        paymentProcessorDTO.getFilterQueryV1(),
        updateParams,
        provider,
      );
    } catch (updateError) {
      this.logger.error(
        `Failed to update payment request ${paymentRequestDto.unique_request_id}: ${updateError.message}`,
      );
    }
  }

  async waitForPaymentProcessing(delayMs: number = 5000): Promise<void> {
    await new Promise((resolve) => setTimeout(resolve, delayMs));
  }
}

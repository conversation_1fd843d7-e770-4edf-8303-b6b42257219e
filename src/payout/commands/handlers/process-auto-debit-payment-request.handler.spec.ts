import { Test, TestingModule } from '@nestjs/testing';
import { ProcessAutoDebitPaymentRequestHandler } from './process-auto-debit-payment-request.handler';
import { PayoneerHttpClient } from '../../../common/external-service/payoneer/services/payoneer.http.client.service';
import { ProgramTokensRepository } from '../../../common/repositories/program-token.repository';
import { ProgramCurrencyMappingRepository } from '../../../common/repositories/program-currency-mapping.repository';
import { ProcessAutoDebitPaymentRequestCommand } from '../impl/process-auto-debit-payment-request.command';
import {
  PaymentRequestStatus,
  Currency,
  PayoneerProvider,
  TokenType,
  BeneficiaryRole,
  SwiftChargeType,
  PaymentType,
  PaymentRequestType,
} from '../../../common/constants/enums';
import { PaymentRequestDto } from '../../../payout/dtos/payment-request.dto';

const mockPaymentRequestDto: PaymentRequestDto = {
  amount: 100,
  currency: 'USD',
  client_legal_entity_id: 'client-123',
  beneficiary_legal_entity_id: 'beneficiary-123',
  beneficiary_payout_profile_id: 'payout-profile-123',
  client_provider_profile_id: 'provider-profile-123',
  provider_system_reference_number: 'sys-ref-123',
  unique_request_id: 'req-456',
  meta: { balanceId: 'bal-123', geography: 'US' },
  provider: 'payoneer',
  checksum: '**********',
  sourceCurrency: Currency.USD,
  status: PaymentRequestStatus.Pending,
  purpose_of_payment: 'PAYOUT',
  reference_number: 'ref-123',
  payment_date: new Date().toISOString(),
  beneficiary: {
    beneficiary_id: 'beneficiary-123',
    currency: 'USD',
    bank_country: 'US',
    beneficiary_role: BeneficiaryRole.Contractor,
    swift_charge_type: SwiftChargeType.Ours,
  },
  client_config: {
    account_id: 'acc-123',
  },
  payment_type: PaymentType.External,
  is_valid: true,
  type: PaymentRequestType.PayOut,
  isValid: jest.fn(),
  validate: jest.fn(),
};

describe('ProcessAutoDebitPaymentRequestHandler', () => {
  let handler: ProcessAutoDebitPaymentRequestHandler;
  let client: PayoneerHttpClient;
  let tokenRepo: ProgramTokensRepository;
  let mappingRepo: ProgramCurrencyMappingRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProcessAutoDebitPaymentRequestHandler,
        {
          provide: PayoneerHttpClient,
          useValue: {
            getAccountBalances: jest.fn(),
            chargeAccountByClientPartnerDebit: jest.fn(),
          },
        },
        {
          provide: ProgramTokensRepository,
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: ProgramCurrencyMappingRepository,
          useValue: {
            getProgramIdUsingCurrency: jest.fn(),
          },
        },
      ],
    }).compile();

    handler = module.get(ProcessAutoDebitPaymentRequestHandler);
    client = module.get(PayoneerHttpClient);
    tokenRepo = module.get(ProgramTokensRepository);
    mappingRepo = module.get(ProgramCurrencyMappingRepository);
  });

  it('should process payment successfully', async () => {
    const command = new ProcessAutoDebitPaymentRequestCommand(mockPaymentRequestDto);

    (mappingRepo.getProgramIdUsingCurrency as jest.Mock).mockResolvedValue('program-789');
    (tokenRepo.findOne as jest.Mock).mockResolvedValue({
      clientId: 'client-123',
      accountId: 'acc-123',
    });
    (client.getAccountBalances as jest.Mock).mockResolvedValue([
      {
        result: {
          balances: {
            items: [{ id: 'bal-123', available_balance: 200 }],
          },
        },
      },
      null,
    ]);
    (client.chargeAccountByClientPartnerDebit as jest.Mock).mockResolvedValue([{ result: 'success' }, null]);

    const [payload, error] = await handler.execute(command);

    expect(error).toBeNull();
    expect(payload).toBeDefined();
    expect(client.chargeAccountByClientPartnerDebit).toHaveBeenCalled();
  });

  it('should fail if client account not found', async () => {
    const command = new ProcessAutoDebitPaymentRequestCommand(mockPaymentRequestDto);

    (mappingRepo.getProgramIdUsingCurrency as jest.Mock).mockResolvedValue('program-789');
    (tokenRepo.findOne as jest.Mock).mockResolvedValue(null);

    const [payload, error] = await handler.execute(command);

    expect(payload).toBeNull();
    expect(error).toBeInstanceOf(Error);
    expect(error.message).toBe('Client account not found');
  });

  it('should fail if balance error occurs', async () => {
    const command = new ProcessAutoDebitPaymentRequestCommand(mockPaymentRequestDto);

    (mappingRepo.getProgramIdUsingCurrency as jest.Mock).mockResolvedValue('program-789');
    (tokenRepo.findOne as jest.Mock).mockResolvedValue({
      clientId: 'client-123',
      accountId: 'acc-123',
    });
    (client.getAccountBalances as jest.Mock).mockResolvedValue([null, { error: 'Balance error' }]);

    const [payload, error] = await handler.execute(command);

    expect(payload).toBeNull();
    expect(error).toBeInstanceOf(Error);
    expect(error.message).toBe('Balance error');
  });

  it('should fail if charge account returns error', async () => {
    const command = new ProcessAutoDebitPaymentRequestCommand(mockPaymentRequestDto);

    (mappingRepo.getProgramIdUsingCurrency as jest.Mock).mockResolvedValue('program-789');
    (tokenRepo.findOne as jest.Mock).mockResolvedValue({
      clientId: 'client-123',
      accountId: 'acc-123',
    });
    (client.getAccountBalances as jest.Mock).mockResolvedValue([
      {
        result: {
          balances: {
            items: [{ id: 'bal-123', available_balance: 200 }],
          },
        },
      },
      null,
    ]);
    (client.chargeAccountByClientPartnerDebit as jest.Mock).mockResolvedValue([
      null,
      { error: 'Charge failed' },
    ]);

    const [payload, error] = await handler.execute(command);

    expect(payload).toBeNull();
    expect(error).toBeInstanceOf(Error);
    expect(error.message).toBe('Charge failed');
  });

  it('should fail if balance is insufficient', async () => {
    const command = new ProcessAutoDebitPaymentRequestCommand(mockPaymentRequestDto);

    (mappingRepo.getProgramIdUsingCurrency as jest.Mock).mockResolvedValue('program-789');
    (tokenRepo.findOne as jest.Mock).mockResolvedValue({
      clientId: 'client-123',
      accountId: 'acc-123',
    });
    (client.getAccountBalances as jest.Mock).mockResolvedValue([
      {
        result: {
          balances: {
            items: [{ id: 'bal-123', available_balance: 0 }],
          },
        },
      },
      null,
    ]);

    const [payload, error] = await handler.execute(command);

    expect(payload).toBeNull();
    expect(error).toBeInstanceOf(Error);
    expect(error.message).toBe('Target currency balance is not sufficient');
  });
});

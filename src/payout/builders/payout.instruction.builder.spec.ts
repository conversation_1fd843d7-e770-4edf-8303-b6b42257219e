import { PayoutInstructionBuilder } from './payout.instruction.builder';
import { PaymentRequestDto } from '../dtos/payment-request.dto';
import { CurrencyMismatchException } from '../../common/exceptions/currency-mismatch.exception';
import { IdempotencyIdNotFoundException } from '../../common/exceptions/idempotency-id-not-found.exception';
import { ProviderMismatchException } from '../../common/exceptions/provider-mismatch.exception';
import { PayoneerProvider } from '../../common/constants/enums';

describe('PayoutInstructionBuilder', () => {
  let paymentRequest: PaymentRequestDto;

  beforeEach(() => {
    paymentRequest = {
      unique_request_id: 'unique-id',
      beneficiary: {
        beneficiary_id: 'beneficiary-id',
        currency: 'USD',
        beneficiary_legal_entity_id: 'entity-id',
      },
      currency: 'USD',
      amount: 100,
      reference_number: 'ref-123',
      purpose_of_payment: 'Client - Paid Salary on Behalf',
      provider: PayoneerProvider.Payoneer,
      beneficiary_legal_entity_id: 'entity-id',
    } as unknown as PaymentRequestDto;
  });

  it('should build payment payload successfully', () => {
    const builder = new PayoutInstructionBuilder(paymentRequest);
    const result = builder.checkAndBuild();

    expect(result.error).toBeNull();
    expect(result.payload).toEqual({
      payments: [
        {
          client_reference_id: 'unique-id',
          payee_id: 'beneficiary-id',
          description: 'Client - Paid Salary on Behalf',
          currency: 'USD',
          amount: 100,
          group_id: "entity-id"
        },
      ],
    });
  });

  it('should throw ProviderMismatchException if provider is not Payoneer', () => {
    paymentRequest.provider = 'InvalidProvider' as PayoneerProvider;
    const builder = new PayoutInstructionBuilder(paymentRequest);

    const result = builder.checkAndBuild();
    expect(result.error).toBeInstanceOf(ProviderMismatchException);
  });

  it('should throw CurrencyMismatchException if currencies do not match', () => {
    paymentRequest.beneficiary.currency = 'EUR';
    const builder = new PayoutInstructionBuilder(paymentRequest);

    const result = builder.checkAndBuild();
    expect(result.error).toBeInstanceOf(CurrencyMismatchException);
  });

  it('should throw IdempotencyIdNotFoundException if idempotency ID is missing', () => {
    paymentRequest.unique_request_id = '';
    const builder = new PayoutInstructionBuilder(paymentRequest);

    const result = builder.checkAndBuild();
    expect(result.error).toBeInstanceOf(IdempotencyIdNotFoundException);
  });

  it('should not set undefined or null values in the payload', () => {
    paymentRequest.amount = null;
    const builder = new PayoutInstructionBuilder(paymentRequest);
    const result = builder.checkAndBuild();

    expect(result.error).toBeNull();
    expect(result.payload.payments[0].amount).toBeUndefined();
  });

  it('should handle empty beneficiary ID gracefully', () => {
    paymentRequest.beneficiary.beneficiary_id = '';
    const builder = new PayoutInstructionBuilder(paymentRequest);
    const result = builder.checkAndBuild();

    expect(result.error).toBeNull();
    expect(result.payload.payments[0].payee_id).toBeUndefined();
  });

  it('should handle empty reference number gracefully', () => {
    paymentRequest.reference_number = '';
    const builder = new PayoutInstructionBuilder(paymentRequest);
    const result = builder.checkAndBuild();

    expect(result.error).toBeNull();
    expect(result.payload.payments[0].description).toBe('Client - Paid Salary on Behalf');
  });

  it('should handle null beneficiary legal entity ID gracefully', () => {
    paymentRequest.beneficiary.beneficiary_id = null;
    const builder = new PayoutInstructionBuilder(paymentRequest);
    const result = builder.checkAndBuild();

    expect(result.error).toBeNull();
    expect(result.payload.payments[0].payee_id).toBeUndefined();
  });

  it('should throw CurrencyMismatchException if currency is null', () => {
    paymentRequest.currency = null;
    const builder = new PayoutInstructionBuilder(paymentRequest);

    const result = builder.checkAndBuild();
    expect(result.error).toBeInstanceOf(CurrencyMismatchException);
  });


  it('should handle null amount gracefully', () => {
    paymentRequest.amount = null;
    const builder = new PayoutInstructionBuilder(paymentRequest);
    const result = builder.checkAndBuild();

    expect(result.error).toBeNull();
    expect(result.payload.payments[0].amount).toBeUndefined();
  });
});

import {
  AMOUNT_KEY,
  CLIENT_REFERENCE_ID_KEY,
  CURRENCY_KEY,
  DESCRIPTION_KEY,
  GROUP_ID_KEY,
  PAYEE_ID_KEY,
} from '../../common/constants/keys';
import { CurrencyMismatchException } from '../../common/exceptions/currency-mismatch.exception';
import { IdempotencyIdNotFoundException } from '../../common/exceptions/idempotency-id-not-found.exception';
import { ProviderMismatchException } from '../../common/exceptions/provider-mismatch.exception';
import { PaymentRequestDto } from '../dtos/payment-request.dto';
import * as dayjs from 'dayjs';
import * as UTC from 'dayjs/plugin/utc';
import * as Timezone from 'dayjs/plugin/timezone';
import {
  ISubmitMassPayoutRequest,
  ISubmitPayoutRequest,
} from '../../common/data-types/payoneer-v4.data.types';
import { PayoneerProvider } from '../../common/constants/enums';

type PaymentsInstructionRes = {
  payload: ISubmitMassPayoutRequest;
  error: Error;
};

dayjs.extend(UTC);
dayjs.extend(Timezone);

export class PayoutInstructionBuilder {
  private readonly paymentsPayload: Partial<Record<keyof ISubmitPayoutRequest, string | number>> = {};

  constructor(private readonly paymentRequest: PaymentRequestDto) {}

  checkAndBuild(): PaymentsInstructionRes {
    try {
      this.validateRequest();

      const payload = this.buildPaymentsPayload();

      return { payload, error: null };
    } catch (e) {
      return {
        payload: null,
        error: e,
      };
    }
  }

  private validateRequest(): void {
    this.checkProviderEquality();
    this.checkCurrencyEquality();
    this.checkIdempotencyIDExistence();
  }

  private checkProviderEquality() {
    if (
      ![PayoneerProvider.Payoneer, PayoneerProvider.PayoneerGBT].includes(
        this.paymentRequest.provider as PayoneerProvider,
      )
    ) {
      throw new ProviderMismatchException(this.paymentRequest.beneficiary_legal_entity_id);
    }
    return this;
  }

  private checkCurrencyEquality() {
    if (
      ![
        this.paymentRequest.beneficiary.currency,
        this.paymentRequest.beneficiary?.secondary_currency,
      ].includes(this.paymentRequest.currency)
    ) {
      throw new CurrencyMismatchException(this.paymentRequest.beneficiary_legal_entity_id);
    }
    return this;
  }

  private checkIdempotencyIDExistence() {
    const idempotencyID = this.paymentRequest.unique_request_id;
    if (!idempotencyID || idempotencyID === '') {
      throw new IdempotencyIdNotFoundException();
    }
    return this;
  }

  private buildPaymentsPayload(): ISubmitMassPayoutRequest {
    this.setValue(CLIENT_REFERENCE_ID_KEY, this.paymentRequest?.unique_request_id);
    this.setValue(PAYEE_ID_KEY, this.paymentRequest?.beneficiary?.beneficiary_id);
    this.setValue(DESCRIPTION_KEY, this.buildDescription());
    this.setValue(CURRENCY_KEY, this.paymentRequest.currency);
    this.setValue(AMOUNT_KEY, this.paymentRequest.amount);
    this.setValue(GROUP_ID_KEY, this.paymentRequest.beneficiary_legal_entity_id);

    return {
      payments: [this.paymentsPayload as ISubmitPayoutRequest],
    };
  }

  private buildDescription(): string {
    const { amount, currency, purpose_of_payment, reference_number } = this.paymentRequest;
    return purpose_of_payment ?? `Payment ${amount} ${currency} of invoice: ${reference_number}`;
  }

  private setValue<K extends keyof ISubmitPayoutRequest>(key: K, val: ISubmitPayoutRequest[K]): void {
    if (val !== undefined && val !== null && val !== '') {
      this.paymentsPayload[key] = val;
    }
  }
}

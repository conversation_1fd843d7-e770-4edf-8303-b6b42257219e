import { PaymentTypesValidator } from './payment-types.validator';
import { PaymentType } from '../../common/constants/enums';

describe('PaymentTypesValidator', () => {
  let validator: PaymentTypesValidator;

  beforeEach(() => {
    validator = new PaymentTypesValidator();
  });

  describe('validate', () => {
    it('should return true when payment_types includes Regular type', () => {
      const payment_types = [PaymentType.Regular];
      expect(validator.validate(payment_types)).toBe(true);
    });

    it('should return true when payment_types includes Priority type', () => {
      const payment_types = [PaymentType.Priority];
      expect(validator.validate(payment_types)).toBe(true);
    });

    it('should return true when payment_types includes both Regular and Priority types', () => {
      const payment_types = [PaymentType.Regular, PaymentType.Priority];
      expect(validator.validate(payment_types)).toBe(true);
    });

    it('should return false when payment_types does not include Regular or Priority type', () => {
      const payment_types = ['INVALID_TYPE'];
      expect(validator.validate(payment_types)).toBe(false);
    });

    it('should return false for empty payment_types array', () => {
      const payment_types: string[] = [];
      expect(validator.validate(payment_types)).toBe(false);
    });
  });

  describe('defaultMessage', () => {
    it('should return correct error message', () => {
      expect(validator.defaultMessage()).toBe('Payment types can only be either priority or regular');
    });
  });
});

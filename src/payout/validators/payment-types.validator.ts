import { ValidatorConstraint, ValidatorConstraintInterface } from 'class-validator';
import { PaymentType } from '../../common/constants/enums';

@ValidatorConstraint()
export class PaymentTypesValidator implements ValidatorConstraintInterface {
  validate(payment_types: string[]) {
    return payment_types.includes(PaymentType.Regular) || payment_types.includes(PaymentType.Priority);
  }

  defaultMessage(): string {
    return 'Payment types can only be either priority or regular';
  }
}

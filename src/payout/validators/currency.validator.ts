import { ValidationArguments, ValidatorConstraint, ValidatorConstraintInterface } from 'class-validator';
import { ValidationConstraint } from '../../common/constants/enums';
import { PaymentRequestDto } from '../dtos/payment-request.dto';

@ValidatorConstraint({
  name: ValidationConstraint.PaymentCurrencyMatcher,
  async: false,
})
export class CurrencyValidator implements ValidatorConstraintInterface {
  validate(currency: string, validationArguments: ValidationArguments) {
    const rootObject = validationArguments.object as PaymentRequestDto;
    if (rootObject.type === 'payout') {
      return [rootObject.beneficiary.currency, rootObject.beneficiary?.secondary_currency].includes(currency);
    }
    return true;
  }

  defaultMessage(): string {
    return 'You cannot assign this Beneficiary to the Payment as it is the incorrect currency';
  }
}

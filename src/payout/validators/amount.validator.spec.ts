import { AmountValidator } from './amount.validator';

describe('AmountValidator', () => {
  let validator: AmountValidator;

  beforeEach(() => {
    validator = new AmountValidator();
  });

  describe('validate', () => {
    it('should return true for positive amount', () => {
      expect(validator.validate(100)).toBe(true);
      expect(validator.validate(0.1)).toBe(true);
    });

    it('should return false for zero amount', () => {
      expect(validator.validate(0)).toBe(false);
    });

    it('should return false for negative amount', () => {
      expect(validator.validate(-100)).toBe(false);
      expect(validator.validate(-0.1)).toBe(false);
    });
  });

  describe('defaultMessage', () => {
    it('should return correct error message', () => {
      expect(validator.defaultMessage()).toBe('Amount can not be zero or negative value');
    });
  });
});

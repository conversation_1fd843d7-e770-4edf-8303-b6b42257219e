import { ValidationArguments } from 'class-validator';
import { CurrencyValidator } from './currency.validator';
import { PaymentRequestDto } from '../dtos/payment-request.dto';

describe('CurrencyValidator', () => {
  let validator: CurrencyValidator;
  let validationArguments: ValidationArguments;

  beforeEach(() => {
    validator = new CurrencyValidator();
    validationArguments = {
      object: {} as PaymentRequestDto,
      value: '',
      targetName: '',
      property: '',
      constraints: [],
    };
  });

  describe('validate', () => {
    it('should return true when payment type is not payout', () => {
      validationArguments.object = {
        type: 'other',
        beneficiary: {
          currency: 'USD',
        },
      } as unknown as PaymentRequestDto;

      const result = validator.validate('EUR', validationArguments);
      expect(result).toBe(true);
    });

    it('should return true when currencies match for payout type', () => {
      validationArguments.object = {
        type: 'payout',
        beneficiary: {
          currency: 'USD',
        },
      } as PaymentRequestDto;

      const result = validator.validate('USD', validationArguments);
      expect(result).toBe(true);
    });

    it('should return false when currencies do not match for payout type', () => {
      validationArguments.object = {
        type: 'payout',
        beneficiary: {
          currency: 'USD',
        },
      } as PaymentRequestDto;

      const result = validator.validate('EUR', validationArguments);
      expect(result).toBe(false);
    });
  });

  describe('defaultMessage', () => {
    it('should return error message', () => {
      const message = validator.defaultMessage();
      expect(message).toBe(
        'You cannot assign this Beneficiary to the Payment as it is the incorrect currency',
      );
    });
  });
});

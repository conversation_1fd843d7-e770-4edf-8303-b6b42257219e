import { Controller, Injectable, Logger, UseInterceptors } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { payoneer } from '@skuad/proto-utils/dist/interface/proto/payoneer/payoneer';
import { LogAfter } from 'skuad-utils-ts/dist/common';
import { ServiceType } from 'skuad-utils-ts/dist/common/constants/enums';
import { WithdrawProcessorService } from '../../../../services/withdraw-processor.service';
import { RpcLoggingInterceptor } from '@skuad/proto-utils/dist/common/interceptor/grpc.interceptor';

@Controller()
@UseInterceptors(RpcLoggingInterceptor)
@Injectable()
export class WithdrawController {
  private readonly logger = new Logger(WithdrawController.name);

  constructor(private readonly withdrawService: WithdrawProcessorService) {}

  @GrpcMethod('PayoneerRpcService', 'getEligibleBanks')
  @LogAfter('logger', ServiceType.Grpc)
  async getEligibleBanks(
    getEligibleBanksInput: payoneer.GetEligibleBanksRequest,
  ): Promise<payoneer.GetEligibleBanksResponse> {
    const response = await this.withdrawService.getEligibleBanks(
      getEligibleBanksInput.clientId,
      getEligibleBanksInput.accountId,
    );
    return response;
  }

  @GrpcMethod('PayoneerRpcService', 'processWithdrawFunds')
  @LogAfter('logger', ServiceType.Grpc)
  async processWithdrawFunds(
    processWithdrawFundsInput: payoneer.ProcessWithdrawFundsInput,
  ): Promise<payoneer.ProcessWithdrawFundsOutput> {
    const response = await this.withdrawService.processWithdrawFund(
      processWithdrawFundsInput.clientId,
      processWithdrawFundsInput,
    );
    return response;
  }

  @GrpcMethod('PayoneerRpcService', 'submitCommitId')
  @LogAfter('logger', ServiceType.Grpc)
  async submitCommitId(
    submitCommitIdInput: payoneer.SubmitCommitIdRequest,
  ): Promise<payoneer.SubmitCommitIdResponse> {
    const response = await this.withdrawService.submitCommitId(
      submitCommitIdInput.clientId,
      submitCommitIdInput.commitId,
      submitCommitIdInput.accountId,
    );
    return response;
  }
}

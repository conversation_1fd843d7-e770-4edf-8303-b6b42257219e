import { Test, TestingModule } from '@nestjs/testing';
import { RpcException } from '@nestjs/microservices';
import { WithdrawController } from './private.withdraw.v1.controller';
import { WithdrawProcessorService } from '../../../../services/withdraw-processor.service';
import { payoneer } from '@skuad/proto-utils/dist/interface/proto/payoneer/payoneer';

describe('WithdrawController', () => {
  let controller: WithdrawController;
  let withdrawService: WithdrawProcessorService;

  // Mock service
  const mockWithdrawService = {
    processWithdrawFund: jest.fn(),
    submitCommitId: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [WithdrawController],
      providers: [
        {
          provide: WithdrawProcessorService,
          useValue: mockWithdrawService,
        },
      ],
    }).compile();

    controller = module.get<WithdrawController>(WithdrawController);
    withdrawService = module.get<WithdrawProcessorService>(WithdrawProcessorService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should be defined', () => {
      expect(controller).toBeDefined();
    });

    it('should have WithdrawProcessorService injected', () => {
      expect(withdrawService).toBeDefined();
    });
  });

  describe('processWithdrawFunds', () => {
    const mockInput: payoneer.ProcessWithdrawFundsInput = {
      clientId: 'test-client',
      clientReferenceId: 'test-reference',
      amount: 100,
      description: 'Test withdrawal',
      to: {
        type: 'bank_id',
        id: 'test-bank-id'
      }
    };

    it('should process withdraw funds successfully', async () => {
      const mockResponse = { status: 'success' };
      mockWithdrawService.processWithdrawFund.mockResolvedValue(mockResponse);

      const result = await controller.processWithdrawFunds(mockInput);

      expect(result).toBe(mockResponse);
      expect(withdrawService.processWithdrawFund).toHaveBeenCalledWith(
        mockInput.clientId,
        mockInput,
      );
    });

    it('should throw RpcException when processWithdrawFund returns error', async () => {
      const mockError = new RpcException('Process failed');
      mockWithdrawService.processWithdrawFund.mockRejectedValue(mockError);

      await expect(controller.processWithdrawFunds(mockInput)).rejects.toThrow(RpcException);
      expect(withdrawService.processWithdrawFund).toHaveBeenCalledWith(
        mockInput.clientId,
        mockInput,
      );
    });
  });

  describe('submitCommitId', () => {
    const mockInput: payoneer.SubmitCommitIdRequest = {
      clientId: 'test-client',
      accountId: 'test-account',
      commitId: 'test-commit',
    };

    it('should submit commit ID successfully', async () => {
      const mockResponse = { status: 'success' };
      mockWithdrawService.submitCommitId.mockResolvedValue(mockResponse);

      const result = await controller.submitCommitId(mockInput);

      expect(result).toBe(mockResponse);
      expect(withdrawService.submitCommitId).toHaveBeenCalledWith(
        mockInput.clientId,
        mockInput.commitId,
        mockInput.accountId
      );
    });

    it('should throw RpcException when submitCommitId returns error', async () => {
      const mockError = new RpcException('Submission failed');
      mockWithdrawService.submitCommitId.mockRejectedValue(mockError);

      await expect(controller.submitCommitId(mockInput)).rejects.toThrow(RpcException);
      expect(withdrawService.submitCommitId).toHaveBeenCalledWith(
        mockInput.clientId,
        mockInput.commitId,
        mockInput.accountId
      );
    });
  });
});

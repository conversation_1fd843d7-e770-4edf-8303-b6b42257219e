import { PayoutProcessorService } from '../../../../services/payout-processor.service';
import { Controller, Get, Version, Logger } from '@nestjs/common';
import { Messages } from '../../../../../common/constants/messages';
import { PayoneerProvider } from '../../../../../common/constants/enums';

@Controller('payout')
export class PrivatePayoutTriggerV1Controller {
  private readonly logger = new Logger(PrivatePayoutTriggerV1Controller.name);

  constructor(private readonly payoutService: PayoutProcessorService) {}

  @Get('trigger')
  @Version('1')
  async triggerPayout() {
    let errors = await this.payoutService.execute(PayoneerProvider.Payoneer);
    if (errors && errors.length > 0) {
      this.logger.log({ message: errors });
    }

    errors = await this.payoutService.execute(PayoneerProvider.PayoneerGBT);
    if (errors && errors.length > 0) {
      this.logger.log({ message: errors });
    }

    return { message: Messages.PAYMENT_PROCESSOR_TRIGGERED_MANUALLY };
  }
}

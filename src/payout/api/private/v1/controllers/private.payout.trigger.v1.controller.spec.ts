import { Test, TestingModule } from '@nestjs/testing';
import { PrivatePayoutTriggerV1Controller } from './private.payout.trigger.v1.controller';
import { PayoutProcessorService } from '../../../../services/payout-processor.service';
import { Logger } from '@nestjs/common';
import { Messages } from '../../../../../common/constants/messages';
import { PayoneerProvider } from '../../../../../common/constants/enums';

describe('PrivatePayoutTriggerV1Controller', () => {
  let controller: PrivatePayoutTriggerV1Controller;
  let payoutService: PayoutProcessorService;
  let mockLogger: { log: jest.Mock };

  beforeEach(async () => {
    // Create a mock logger
    mockLogger = { log: jest.fn() };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [PrivatePayoutTriggerV1Controller],
      providers: [
        {
          provide: PayoutProcessorService,
          useValue: {
            execute: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<PrivatePayoutTriggerV1Controller>(PrivatePayoutTriggerV1Controller);
    payoutService = module.get<PayoutProcessorService>(PayoutProcessorService);

    // Replace the controller's logger with our mock
    (controller as any).logger = mockLogger;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('triggerPayout', () => {
    it('should trigger payouts for both providers and return success message', async () => {
      jest.spyOn(payoutService, 'execute').mockResolvedValue([]);

      const result = await controller.triggerPayout();

      expect(payoutService.execute).toHaveBeenCalledTimes(2);
      expect(payoutService.execute).toHaveBeenCalledWith(PayoneerProvider.Payoneer);
      expect(payoutService.execute).toHaveBeenCalledWith(PayoneerProvider.PayoneerGBT);
      expect(result).toEqual({ message: Messages.PAYMENT_PROCESSOR_TRIGGERED_MANUALLY });
    });

    it('should log errors when Payoneer provider execution fails', async () => {
      const mockErrors = ['Error 1', 'Error 2'];
      jest
        .spyOn(payoutService, 'execute')
        .mockResolvedValueOnce(mockErrors as any)
        .mockResolvedValueOnce([]);

      await controller.triggerPayout();

      expect(mockLogger.log).toHaveBeenCalledWith({ message: mockErrors });
      expect(mockLogger.log).toHaveBeenCalledTimes(1);
    });

    it('should log errors when PayoneerGBT provider execution fails', async () => {
      const mockErrors = ['Error 3', 'Error 4'];
      jest
        .spyOn(payoutService, 'execute')
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce(mockErrors as any);

      await controller.triggerPayout();

      expect(mockLogger.log).toHaveBeenCalledWith({ message: mockErrors });
      expect(mockLogger.log).toHaveBeenCalledTimes(1);
    });

    it('should log errors when both providers execution fails', async () => {
      const mockErrors1 = ['Error 1', 'Error 2'];
      const mockErrors2 = ['Error 3', 'Error 4'];
      jest
        .spyOn(payoutService, 'execute')
        .mockResolvedValueOnce(mockErrors1 as any)
        .mockResolvedValueOnce(mockErrors2 as any);

      await controller.triggerPayout();

      expect(mockLogger.log).toHaveBeenCalledWith({ message: mockErrors1 });
      expect(mockLogger.log).toHaveBeenCalledWith({ message: mockErrors2 });
      expect(mockLogger.log).toHaveBeenCalledTimes(2);
    });
  });
});

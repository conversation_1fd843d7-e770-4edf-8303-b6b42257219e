import { MiddlewareConsumer, Module, NestModule, RequestMethod } from '@nestjs/common';
import { CommandHandlers } from './commands/handlers';
import { CqrsModule } from '@nestjs/cqrs';
import { CommonModule } from '../common/common.module';
import { PayoutProcessorService } from './services/payout-processor.service';
import { PrivatePayoutTriggerV1Controller } from './api/private/v1/controllers/private.payout.trigger.v1.controller';
import { ReqAuthMiddleware } from '../common/middlewares/req-auth.middleware';
import { PaymentRequestService } from './services/payment-request.service';
import { PayoneerClientModule } from '../common/external-service/payoneer/payoneer.module';
import { PaymentGrpcClientModule } from '../common/external-service/payments/payments.module';
import { ProgramTokensRepository } from 'src/common/repositories/program-token.repository';
import { ProgramCurrencyMappingRepository } from 'src/common/repositories/program-currency-mapping.repository';
import { WithdrawProcessorService } from './services/withdraw-processor.service';
import { ProgramTokenService } from 'src/payin/service/program-token.service';
import { PayoneerAccountRepository } from 'src/common/repositories/account.repository';
import { ProgramTokenServiceHelper } from 'src/payin/service/helper/program-token.service.helper';
import { WithdrawController } from './api/private/v1/controllers/private.withdraw.v1.controller';
import { ForexServiceModule } from 'src/common/external-service/forex-service/forex.module';
import { ForexService } from 'src/common/external-service/forex-service/service/forex-service';

@Module({
  imports: [
    CommonModule,
    PayoneerClientModule,
    PaymentGrpcClientModule,
    CqrsModule,
    ForexServiceModule,
  ],
  providers: [
    ProgramTokensRepository,
    ProgramCurrencyMappingRepository,
    PayoneerAccountRepository,
    PayoutProcessorService,
    WithdrawProcessorService,
    ProgramTokenService,
    PaymentRequestService,
    ProgramTokenServiceHelper,
    ForexService,
    ...CommandHandlers,
  ],
  controllers: [PrivatePayoutTriggerV1Controller, WithdrawController],
  exports: [PayoutProcessorService, WithdrawProcessorService],
})
export class PayoutModule implements NestModule {
  configure(consumer: MiddlewareConsumer): MiddlewareConsumer | void {
    consumer.apply(ReqAuthMiddleware).forRoutes({ path: '/v1/payout/*', method: RequestMethod.ALL });
  }
}

import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { PayoneerHttpClient } from '../../common/external-service/payoneer/services/payoneer.http.client.service';
import { RpcException } from '@nestjs/microservices';
import { ProgramTokenService } from '../../payin/service/program-token.service';
import { payoneer } from '@skuad/proto-utils/dist/interface/proto/payoneer/payoneer';
import { convertToCamelCase } from '../../common/helpers/utils';
import { Status } from '@grpc/grpc-js/build/src/constants';

@Injectable()
export class WithdrawProcessorService {
  private readonly logger = new Logger(WithdrawProcessorService.name);

  constructor(
    private readonly payoneerClient: PayoneerHttpClient,
    private readonly programTokenService: ProgramTokenService,
  ) {}

  async getEligibleBanks(clientId: string, accountId?: string): Promise<payoneer.GetEligibleBanksResponse> {
    try {

      accountId = accountId || (await this.programTokenService.getAccount(clientId)).accountId;
      if (!accountId) {
        throw new BadRequestException('Account ID is required');
      }

      const [result, error] = await this.payoneerClient.getEligibleBanks(clientId, accountId);

      if (error) {
        throw error;
      }

      return convertToCamelCase(result);
    } catch (error) {
      this.logger.error('Withdraw funds failed', JSON.stringify(error));
     throw new RpcException({
       code: Status.INTERNAL,
       message: JSON.stringify({
         code: error?.errors?.error_details?.code || error?.status ,
         description: 'Failed to get eligible banks',
         message: error?.errors?.error_description || error?.message,
         error: error,
       }),
     });
    }
  }

  async processWithdrawFund(
    clientId: string,
    input: payoneer.ProcessWithdrawFundsInput,
  ): Promise<payoneer.ProcessWithdrawFundsOutput> {
    try {
      if (!input.balanceId) {
        throw new BadRequestException('Balance ID are required');
      }

      if (!input.amount || input.amount <= 0) {
        throw new BadRequestException('Valid amount is required');
      }

      const accountId = input?.accountId || (await this.programTokenService.getAccount(clientId))?.accountId;
      if (!accountId) {
        throw new BadRequestException('No mandate found for client');
      }

      const withdrawPayload = {
        client_reference_id: input.clientReferenceId,
        amount: input.amount,
        description: input.description || 'Funds withdrawal',
        to: input.to,
      };

      // Create withdraw transaction for track
      const [result, error] = await this.payoneerClient.ProcessWithdrawFund(
        clientId,
        accountId,
        input.balanceId,
        withdrawPayload,
      );

      if (error) {
        this.logger.error('Withdraw funds failed', {
          error,
          input: JSON.stringify(withdrawPayload),
        });
        throw error;
      }

      return convertToCamelCase(result);
    } catch (error) {
      this.logger.error('Withdraw funds failed', { error, input });

       throw new RpcException({
         code: Status.INTERNAL,
         message: JSON.stringify({
           code: error?.errors?.error_details?.code || error?.status,
           description: 'Failed to process withdraw funds',
           message: error?.errors?.error_description || error?.message,
           error: error,
         }),
       });
    }
  }

  async submitCommitId(
    clientId: string,
    commitId: string,
    accountId?: string,
  ): Promise<payoneer.SubmitCommitIdResponse> {
    try {
      if (!commitId) {
        throw new BadRequestException('Commit ID are required');
      }
      accountId = accountId || (await this.programTokenService.getAccount(clientId)).accountId;

      const [result, error] = await this.payoneerClient.submitCommitId(clientId, accountId, commitId);

      if (error) {
        this.logger.error('Withdraw funds failed', {
          error,
          input: JSON.stringify({ accountId: accountId, commitId: commitId }),
        });
        throw error;
      }

      return convertToCamelCase(result);
    } catch (error) {
      this.logger.error('Withdraw funds failed', { error });

      throw new RpcException({
        code: Status.INTERNAL,
        message: JSON.stringify({
          code: error?.errors?.error_details?.code || error?.status,
          description: 'Failed to submit commit ID for withdraw funds',
          message: error?.errors?.error_description || error?.message,
          error: error,
        }),
      });
    }
  }
}

import { Test, TestingModule } from '@nestjs/testing';
import { AutoDebitProcessorService } from './auto-debit-processor.service';
import { PaymentRequestService } from './payment-request.service';
import { CommandBus } from '@nestjs/cqrs';
import { PaymentRequestStatus, PayoneerProvider } from '../../common/constants/enums';
import { PaymentProcessorDto } from '../dtos/payment-processor.dto';
import { PaymentRequestDto } from '../dtos/payment-request.dto';

describe('AutoDebitProcessorService', () => {
  let service: AutoDebitProcessorService;
  let paymentRequestService: jest.Mocked<PaymentRequestService>;
  let commandBus: jest.Mocked<CommandBus>;

  const mockPaymentRequest: PaymentRequestDto = {
    unique_request_id: 'test-req-id',
    provider_system_reference_number: 'ref123',
    // Add other necessary fields if needed for your DTO
  } as PaymentRequestDto;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AutoDebitProcessorService,
        {
          provide: PaymentRequestService,
          useValue: {
            fetchPaymentRequests: jest.fn(),
            findManyAndUpdatePaymentRequest: jest.fn(),
            findOneAndUpdatePaymentRequest: jest.fn(),
          },
        },
        {
          provide: CommandBus,
          useValue: {
            execute: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get(AutoDebitProcessorService);
    paymentRequestService = module.get(PaymentRequestService);
    commandBus = module.get(CommandBus);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('execute', () => {
    it('returns empty output if no payment requests found', async () => {
      paymentRequestService.fetchPaymentRequests.mockResolvedValue([[], null]);

      const result = await service.execute();

      expect(result).toEqual([]);
      expect(paymentRequestService.fetchPaymentRequests).toHaveBeenCalledWith(
        PayoneerProvider.PayoneerAutoDebit,
      );
    });

    it('handles dataFetchError and returns PaymentProcessorDto with error', async () => {
      paymentRequestService.fetchPaymentRequests.mockResolvedValue([[], new Error('fetch error')]);

      const result = await service.execute();

      expect(result).toHaveLength(1);
      const error = result[0].getError();
      expect(error.data_fetch_error.message).toBe('fetch error');
    });

    it('handles unexpected error', async () => {
      paymentRequestService.fetchPaymentRequests.mockRejectedValue(new Error('Unexpected'));

      const result = await service.execute();

      expect(result).toHaveLength(1);
      const error = result[0].getError();
      expect(error.unexpected_error.message).toBe('Unexpected');
    });

    it('processes payment requests and updates status', async () => {
      paymentRequestService.fetchPaymentRequests.mockResolvedValue([[mockPaymentRequest], null]);
      paymentRequestService.findManyAndUpdatePaymentRequest.mockResolvedValue([[mockPaymentRequest], null]);
      commandBus.execute.mockResolvedValue([{ success: true }, null]);
      paymentRequestService.findOneAndUpdatePaymentRequest.mockRejectedValue(new Error('Update DB error'));

      const result = await service.execute();

      expect(paymentRequestService.fetchPaymentRequests).toHaveBeenCalled();
      expect(paymentRequestService.findManyAndUpdatePaymentRequest).toHaveBeenCalled();
      expect(commandBus.execute).toHaveBeenCalled();
      expect(paymentRequestService.findOneAndUpdatePaymentRequest).toHaveBeenCalled();
      expect(result.length).toBeGreaterThanOrEqual(0);
    });

    it('handles unexpected error', async () => {
      paymentRequestService.fetchPaymentRequests.mockRejectedValue(new Error('Unexpected'));

      const result = await service.execute();

      expect(result).toHaveLength(1);
      const error = result[0].getError();
      expect(error.unexpected_error.message).toBe('Unexpected');
    });

    it('skips update if uniqueRequestIds is undefined', async () => {
      const invalidPaymentRequest = {
        // no unique_request_id
      } as unknown as PaymentRequestDto;

      paymentRequestService.fetchPaymentRequests.mockResolvedValue([[invalidPaymentRequest], null]);

      const mapSpy = jest.spyOn<any, any>(Array.prototype, 'map').mockImplementationOnce(() => undefined);

      const result = await service.execute();

      mapSpy.mockRestore(); // clean up

      // Expect one result with unexpected_error due to payoutResponses being undefined
      expect(result).toHaveLength(1);
      expect(result[0].getError()?.unexpected_error).toBeDefined();
      expect((result[0].getError().unexpected_error as Error).message).toContain(
        'payoutResponses is not iterable',
      );
    });

    it('covers fallback to empty array when updatedPaymentRequests is undefined', async () => {
      paymentRequestService.fetchPaymentRequests.mockResolvedValue([[mockPaymentRequest], null]);

      // Simulate undefined updatedPaymentRequests
      paymentRequestService.findManyAndUpdatePaymentRequest.mockResolvedValue([undefined, null]);

      const executeSpy = jest.spyOn(commandBus, 'execute');

      const result = await service.execute();

      expect(paymentRequestService.findManyAndUpdatePaymentRequest).toHaveBeenCalled();
      expect(executeSpy).not.toHaveBeenCalled();

      // Expect unexpected_error due to undefined payoutResponses
      expect(result).toHaveLength(1);
      expect(result[0].getError()?.unexpected_error).toBeDefined();
      expect((result[0].getError().unexpected_error as Error).message).toContain(
        'payoutResponses is not iterable',
      );
    });


  });

  describe('processAutoDebitPaymentRequests', () => {
    it('processes payment request and updates status to Completed', async () => {
      commandBus.execute.mockResolvedValue([{ success: true }, null]);
      paymentRequestService.findOneAndUpdatePaymentRequest.mockResolvedValue(new Error('Update DB error'));

      const result = await service.processAutoDebitPaymentRequests([mockPaymentRequest]);

      expect(commandBus.execute).toHaveBeenCalled();
      expect(paymentRequestService.findOneAndUpdatePaymentRequest).toHaveBeenCalledWith(
        { unique_request_id: mockPaymentRequest.unique_request_id },
        expect.objectContaining({ status: PaymentRequestStatus.Completed }),
        PayoneerProvider.PayoneerAutoDebit,
      );
      expect(result).toEqual([]);
    });

    it('processes payment request and updates status to Failed on error', async () => {
      commandBus.execute.mockResolvedValue([null, 'create error']);
      paymentRequestService.findOneAndUpdatePaymentRequest.mockResolvedValue(new Error('Update DB error'));

      const result = await service.processAutoDebitPaymentRequests([mockPaymentRequest]);

      expect(commandBus.execute).toHaveBeenCalled();
      expect(paymentRequestService.findOneAndUpdatePaymentRequest).toHaveBeenCalledWith(
        { unique_request_id: mockPaymentRequest.unique_request_id },
        expect.objectContaining({ status: PaymentRequestStatus.Failed }),
        PayoneerProvider.PayoneerAutoDebit,
      );
      expect(result).toEqual([]);
    });

    it('handles error during findOneAndUpdatePaymentRequest gracefully', async () => {
      commandBus.execute.mockResolvedValue([{ success: true }, null]);
      paymentRequestService.findOneAndUpdatePaymentRequest.mockRejectedValue(new Error('Update DB error'));

      const result = await service.processAutoDebitPaymentRequests([mockPaymentRequest]);

      expect(result).toEqual([]);
    });
    it('logs gracefully when processAutoDebitPaymentRequests throws undefined error', async () => {
      const faultyPaymentRequest = {
        unique_request_id: 'req-undefined-error',
      } as PaymentRequestDto;

      paymentRequestService.fetchPaymentRequests.mockResolvedValue([[faultyPaymentRequest], null]);
      paymentRequestService.findManyAndUpdatePaymentRequest.mockResolvedValue([[faultyPaymentRequest], null]);

      const spy = jest
        .spyOn<any, any>(service, 'processAutoDebitPaymentRequests')
        .mockImplementationOnce(() => {
          throw undefined;
        });

      const result = await service.execute();

      spy.mockRestore(); // cleanup

      expect(result).toHaveLength(1);
      const error = result[0].getError();
      expect(error.unexpected_error).toBeUndefined();
    });
  });
});

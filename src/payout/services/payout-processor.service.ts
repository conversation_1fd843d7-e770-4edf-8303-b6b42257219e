import { Injectable, Logger } from '@nestjs/common';
import { PaymentRequestStatus, PaymentRequestType, PayoneerProvider } from '../../common/constants/enums';
import { Messages } from '../../common/constants/messages';
import { PaymentProcessorDto } from '../dtos/payment-processor.dto';
import { PaymentRequestService } from './payment-request.service';
import { isArrayEmpty } from '../../common/helpers/utils';
import { PaymentRequestDto } from '../dtos/payment-request.dto';
import { CommandBus } from '@nestjs/cqrs';
import { ProcessPayoutInstructionCommand } from '../commands/impl/process-payout-instruction.command';
import { FindBeneficiaryRequestProtoDto } from '@skuad/proto-utils/dist/payments/beneficiary/types/types';
import { BeneficiaryGrpcClient } from '../../common/external-service/payments/services/beneficiary-grpc-client.service';

@Injectable()
export class PayoutProcessorService {
  private readonly logger = new Logger(PayoutProcessorService.name);

  constructor(
    private readonly commandBus: CommandBus,
    private readonly service: PaymentRequestService,
    private readonly beneficiaryGrpcService: BeneficiaryGrpcClient,
  ) {}

  async execute(provider: PayoneerProvider): Promise<PaymentProcessorDto[]> {
    let outputs: PaymentProcessorDto[] = [];
    const paymentProcessorDto = new PaymentProcessorDto();

    try {
      const [paymentRequests, dataFetchError] = await this.service.fetchPaymentRequests(provider);
      if (dataFetchError) {
        paymentProcessorDto.setDataFetchError(dataFetchError);
        outputs.push(paymentProcessorDto);
        return outputs;
      }

      if (isArrayEmpty(paymentRequests)) {
        this.logger.log({ message: Messages.PAYMENT_REQUESTS_NOT_FOUND });
        return outputs;
      }

      this.logger.log({ message: 'payment requests are fetched successfully', data: paymentRequests });
      const [uniqueReqIDsForPayoneer, uniqueReqIDsForPayoneerGBT] =
        this.service.getUniqueRequestIDs(paymentRequests);

      const updatedPaymentRequestsFinal = [];
      if (uniqueReqIDsForPayoneer?.length) {
        const [updatedPaymentRequests, updateError] = await this.service.findManyAndUpdatePaymentRequest(
          { unique_request_ids: uniqueReqIDsForPayoneer, status: PaymentRequestStatus.Initiated },
          {
            status: PaymentRequestStatus.Processing,
          },
          PayoneerProvider.Payoneer,
        );

        if (updateError) {
          paymentProcessorDto.setPaymentRequestUpdateError(updateError);
          outputs.push(paymentProcessorDto);
        }

        updatedPaymentRequestsFinal.push(...(updatedPaymentRequests || ([] as PaymentRequestDto[])));
      }

      if (uniqueReqIDsForPayoneerGBT?.length) {
        const [updatedPaymentRequests, updateError] = await this.service.findManyAndUpdatePaymentRequest(
          { unique_request_ids: uniqueReqIDsForPayoneerGBT, status: PaymentRequestStatus.Initiated },
          {
            status: PaymentRequestStatus.Processing,
          },
          PayoneerProvider.PayoneerGBT,
        );

        if (updateError) {
          paymentProcessorDto.setPaymentRequestUpdateError(updateError);
          outputs.push(paymentProcessorDto);
        }
        updatedPaymentRequestsFinal.push(...(updatedPaymentRequests || ([] as PaymentRequestDto[])));
      }

      const paymentRequestsForPayout = await this.separateRequests(updatedPaymentRequestsFinal);
      const payoutResponses = await this.processPayoutPaymentRequests(paymentRequestsForPayout, provider);
      outputs = [...outputs, ...payoutResponses];
    } catch (error) {
      this.logger.error({ message: error?.message, stack: error?.stack });
      paymentProcessorDto.setUnexpectedError(error);
      outputs.push(paymentProcessorDto);
    }

    return outputs;
  }

  async separateRequests(paymentRequests: PaymentRequestDto[]): Promise<Record<string, PaymentRequestDto[]>> {
    const paymentRequestsPayout: Record<string, PaymentRequestDto[]> = {};

    for (const paymentRequest of paymentRequests) {
      try {
        // Fetch and check if beneficiary is active or not.
        const fetchBeneficiaryInput: FindBeneficiaryRequestProtoDto = {
          queryParams: {
            providerBeneficiaryId: paymentRequest?.beneficiary?.beneficiary_id,
          },
        };

        // Will fetch all the beneficiary in one api call.
        const beneficiary = await this.beneficiaryGrpcService.findOne(fetchBeneficiaryInput);

        if (!beneficiary?.data?.meta?.programId) {
          this.logger.error(
            JSON.stringify({
              errorMessage: '[SLACK]: Beneficiary programId is not found',
              data: {
                beneficiaryLegalEntityId: beneficiary?.data?.beneficiaryLegalEntityId,
                provider: beneficiary?.data?.provider,
              },
            }),
          );
          continue;
        }

        if (paymentRequest?.type === PaymentRequestType.PayOut) {
          if (!paymentRequestsPayout[beneficiary?.data?.meta?.programId]) {
            paymentRequestsPayout[beneficiary?.data?.meta?.programId] = [];
          }

          if (beneficiary?.data?.meta?.secondaryCurrency) {
            paymentRequest.beneficiary.secondary_currency = beneficiary.data.meta.secondaryCurrency;
          }

          paymentRequestsPayout[beneficiary?.data?.meta?.programId].push(paymentRequest);
        }
      } catch (error) {
        this.logger.error(
          JSON.stringify({
            errorMessage: '[SLACK]: Error while separating the payment requests',
            error: error?.message,
            stack: error?.stack,
            data: {
              idempotencyKey: paymentRequest?.unique_request_id,
              beneficiaryLegalEntityId: paymentRequest?.beneficiary?.beneficiary_id,
              provider: paymentRequest?.provider,
            },
          }),
        );
      }
    }

    return paymentRequestsPayout;
  }

  async processPayoutPaymentRequests(
    paymentRequestsPayout: Record<string, PaymentRequestDto[]>,
    provider: PayoneerProvider,
  ): Promise<PaymentProcessorDto[]> {
    const allFailedPaymentRequests = [];

    for (const [programId, paymentRequests] of Object.entries(paymentRequestsPayout)) {
      const { failedPaymentRequests } = await this.commandBus.execute(
        new ProcessPayoutInstructionCommand(paymentRequests, programId, provider),
      );

      allFailedPaymentRequests.push(...(failedPaymentRequests || []));
    }

    (allFailedPaymentRequests || [])?.forEach((paymentRequest: PaymentRequestDto) => {
      this.logger.log({
        message: `[SLACK] payout creation failed for client_reference_id: ${paymentRequest.unique_request_id} from payoneer or payoneer_gbt`,
      });
    });
    return allFailedPaymentRequests;
  }
}

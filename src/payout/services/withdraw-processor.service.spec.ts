import { Test, TestingModule } from '@nestjs/testing';
import { WithdrawProcessorService } from './withdraw-processor.service';
import { PayoneerHttpClient } from '../../common/external-service/payoneer/services/payoneer.http.client.service';
import { ProgramTokenService } from '../../payin/service/program-token.service';
import { PayoneerAccountService } from '../../payin/service/payoneer-account.service';
import { BadRequestException, Logger } from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';
import { TokenType } from '../../common/constants/enums';
import { ProgramTokensEntity } from '../../common/entities/program-token.entity';
import { error } from 'console';

describe('WithdrawProcessorService', () => {
  let service: WithdrawProcessorService;
  let payoneerClient: jest.Mocked<PayoneerHttpClient>;
  let programTokenService: jest.Mocked<ProgramTokenService>;
  let payoneerAccountService: jest.Mocked<PayoneerAccountService>;

  // Create a mock ProgramTokensEntity factory function
  const createMockProgramToken = (accountId: string): ProgramTokensEntity => {
    return {
      id: 'test-id',
      accountId,
      accessToken: 'test-token',
      consentedAt: new Date(),
      expiresAt: new Date(),
      refreshToken: 'test-refresh-token',
      refreshTokenExpiresAt: new Date(),
      tokenType: TokenType.AccessToken,
      scope: 'test-scope',
      idToken: 'test-id-token',
      clientId: 'test-client',
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'test',
      updatedBy: 'test',
      deletedAt: null,
      deletedBy: null,
      hasId: () => true,
      save: () => Promise.resolve(null),
      remove: () => Promise.resolve(null),
      softRemove: () => Promise.resolve(null),
      recover: () => Promise.resolve(null),
      reload: () => Promise.resolve(null),
    } as ProgramTokensEntity;
  };

  beforeEach(async () => {
    const mockPayoneerClient = {
      getEligibleBanks: jest.fn(),
      ProcessWithdrawFund: jest.fn(),
      submitCommitId: jest.fn(),
    };

    const mockProgramTokenService = {
      getAccount: jest.fn(),
    };

    const mockPayoneerAccountService = {
      getAccountBalance: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WithdrawProcessorService,
        {
          provide: PayoneerHttpClient,
          useValue: mockPayoneerClient,
        },
        {
          provide: ProgramTokenService,
          useValue: mockProgramTokenService,
        },
        {
          provide: PayoneerAccountService,
          useValue: mockPayoneerAccountService,
        },
      ],
    }).compile();

    service = module.get<WithdrawProcessorService>(WithdrawProcessorService);
    payoneerClient = module.get(PayoneerHttpClient);
    programTokenService = module.get(ProgramTokenService);
    payoneerAccountService = module.get(PayoneerAccountService);
  });

  describe('getEligibleBanks', () => {
    const clientId = 'test-client';
    const accountId = 'test-account';

    it('should throw BadRequestException when accountId is missing', async () => {
      programTokenService.getAccount.mockResolvedValue(null);
      await expect(service.getEligibleBanks(clientId)).rejects.toThrow(RpcException);
    });

    it('should return eligible banks successfully', async () => {
      const mockResponse = { result: { items: [{ id: 'bank-1' }] } };
      programTokenService.getAccount.mockResolvedValue(createMockProgramToken(accountId));
      payoneerClient.getEligibleBanks.mockResolvedValue([mockResponse, null]);

      const result = await service.getEligibleBanks(clientId, accountId);
      expect(result).toEqual(mockResponse);
      expect(payoneerClient.getEligibleBanks).toHaveBeenCalledWith(clientId, accountId);
    });

    it('should throw RpcException when API call fails', async () => {
      programTokenService.getAccount.mockResolvedValue(createMockProgramToken(accountId));
      payoneerClient.getEligibleBanks.mockResolvedValue([null, { error: 'API Error', errors: {} }]);

      await expect(service.getEligibleBanks(clientId, accountId)).rejects.toThrow(RpcException);
    });

    it('should throw RpcException when client ID is empty', async () => {
      await expect(service.getEligibleBanks('')).rejects.toThrow(RpcException);
    });

    it('should throw RpcException when client ID is null', async () => {
      await expect(service.getEligibleBanks(null)).rejects.toThrow(RpcException);
    });

    it('should handle empty bank list response correctly', async () => {
      // Create a mock response without specifying internal structure details
      const mockResponse = { result: {} };
      programTokenService.getAccount.mockResolvedValue(createMockProgramToken(accountId));
      payoneerClient.getEligibleBanks.mockResolvedValue([mockResponse, null]);

      const result = await service.getEligibleBanks(clientId, accountId);
      expect(result).toEqual(mockResponse);
      // Just verify the result object itself without checking specific properties
      expect(result).toBeDefined();
      expect(result.result).toBeDefined();
    });
    it('should handle network errors gracefully', async () => {
      programTokenService.getAccount.mockResolvedValue(createMockProgramToken(accountId));
      payoneerClient.getEligibleBanks.mockRejectedValue(new Error('Network error'));
      await expect(service.getEligibleBanks(clientId, accountId)).rejects.toThrow(RpcException);
    });
    it('should handle completely undefined error object', async () => {
      payoneerClient.getEligibleBanks.mockRejectedValue(undefined); // error itself is undefined

      await expect(service.getEligibleBanks(clientId, accountId)).rejects.toThrow(RpcException);
    });
    it('should handle error without "errors" field', async () => {
      payoneerClient.getEligibleBanks.mockResolvedValue([
        null,
        {
          error: '',
          errors: undefined,
        },
      ]);

      await expect(service.getEligibleBanks(clientId, accountId)).rejects.toThrow(RpcException);
    });
    it('should handle error without "error_details"', async () => {
      payoneerClient.getEligibleBanks.mockResolvedValue([
        null,
        {
          errors: {},
          error: '',
        },
      ]);

      await expect(service.getEligibleBanks(clientId, accountId)).rejects.toThrow(RpcException);
    });
    it('should handle error with empty error_details', async () => {
      payoneerClient.getEligibleBanks.mockResolvedValue([
        null,
        {
          errors: {
            error_details: {}, // code missing
          },
          error: '',
        },
      ]);

      await expect(service.getEligibleBanks(clientId, accountId)).rejects.toThrow(RpcException);
    });
  });

  describe('processWithdrawFund', () => {
    const clientId = 'test-client';
    const validInput = {
      accountId: 'test-account',
      balanceId: 'test-balance',
      amount: 100,
      targetPartnerId: 'test-partner',
      clientReferenceId: 'test-reference',
      description: 'test withdrawal',
      to: {
        type: 'bank_id',
        id: 'test-bank-id',
      },
    };

    it('should throw BadRequestException when required fields are missing', async () => {
      await expect(service.processWithdrawFund(clientId, {})).rejects.toThrow(RpcException);
      await expect(service.processWithdrawFund(clientId, { accountId: 'test' })).rejects.toThrow(
        RpcException,
      );
    });

    it('should throw BadRequestException when amount is invalid', async () => {
      const invalidInput = { ...validInput, amount: 0 };
      await expect(service.processWithdrawFund(clientId, invalidInput)).rejects.toThrow(RpcException);
    });

    it('should throw BadRequestException when no mandate found', async () => {
      programTokenService.getAccount.mockResolvedValue(null);
      await expect(service.processWithdrawFund(clientId, validInput)).rejects.toThrow(RpcException);
    });

    it('should process withdrawal successfully', async () => {
      const mockWithdrawResponse = { status: 'success' };
      programTokenService.getAccount.mockResolvedValue(createMockProgramToken('test-account'));
      payoneerClient.ProcessWithdrawFund.mockResolvedValue([mockWithdrawResponse, null]);

      const result = await service.processWithdrawFund(clientId, validInput);
      expect(result).toEqual(mockWithdrawResponse);
    });

    it('should throw RpcException when withdrawal fails', async () => {
      programTokenService.getAccount.mockResolvedValue(createMockProgramToken('test-account'));
      payoneerClient.ProcessWithdrawFund.mockResolvedValue([
        null,
        { error: 'Withdrawal failed', errors: {} },
      ]);

      await expect(service.processWithdrawFund(clientId, validInput)).rejects.toThrow(RpcException);
    });

    it('should throw RpcException when amount is negative', async () => {
      const invalidInput = { ...validInput, amount: -100 };
      await expect(service.processWithdrawFund(clientId, invalidInput)).rejects.toThrow(RpcException);
    });

    it('should throw RpcException when client ID is empty', async () => {
      await expect(service.processWithdrawFund('', validInput)).rejects.toThrow(RpcException);
    });

    it('should throw RpcException when client ID is null', async () => {
      await expect(service.processWithdrawFund(null, validInput)).rejects.toThrow(RpcException);
    });

    it('should throw RpcException when bank ID is invalid', async () => {
      const invalidInput = {
        ...validInput,
        to: {
          type: 'bank_id',
          id: '',
        },
      };
      await expect(service.processWithdrawFund(clientId, invalidInput)).rejects.toThrow(RpcException);
    });

    it('should throw RpcException when target type is invalid', async () => {
      const invalidInput = {
        ...validInput,
        to: {
          type: 'invalid_type',
          id: 'test-bank-id',
        },
      };
      await expect(service.processWithdrawFund(clientId, invalidInput)).rejects.toThrow(RpcException);
    });

    it('should handle network errors gracefully', async () => {
      programTokenService.getAccount.mockResolvedValue(createMockProgramToken('test-account'));
      payoneerClient.ProcessWithdrawFund.mockRejectedValue(new Error('Network error'));

      await expect(service.processWithdrawFund(clientId, validInput)).rejects.toThrow(RpcException);
    });

    it('should process withdrawal with maximum allowed amount', async () => {
      const maxAmountInput = { ...validInput, amount: 10000 };
      const mockWithdrawResponse = { status: 'success' };
      programTokenService.getAccount.mockResolvedValue(createMockProgramToken('test-account'));
      payoneerClient.ProcessWithdrawFund.mockResolvedValue([mockWithdrawResponse, null]);

      const result = await service.processWithdrawFund(clientId, maxAmountInput);
      expect(result).toEqual(mockWithdrawResponse);
    });

    it('should throw RpcException when programTokenService.getAccount returns null (accountId optional chain)', async () => {
      const inputWithoutAccountId = {
        ...validInput,
        accountId: undefined,
      };
      programTokenService.getAccount.mockResolvedValue(null); // this triggers `?.accountId` returning undefined

      await expect(service.processWithdrawFund(clientId, inputWithoutAccountId)).rejects.toThrow(
        RpcException,
      );
    });

    it('should handle undefined error object gracefully in processWithdrawFund', async () => {
      programTokenService.getAccount.mockResolvedValue(createMockProgramToken('test-account'));
      payoneerClient.ProcessWithdrawFund.mockRejectedValue(undefined);

      await expect(service.processWithdrawFund(clientId, validInput)).rejects.toThrow(RpcException);
    });

    it('should fallback to error.status when error.errors is undefined', async () => {
      programTokenService.getAccount.mockResolvedValue(createMockProgramToken('test-account'));

      payoneerClient.ProcessWithdrawFund.mockResolvedValue([
        null,
        {
          error: '',
          errors: {},
        },
      ]);

      await expect(service.processWithdrawFund(clientId, validInput)).rejects.toThrow(RpcException);
    });

    it('should fallback when error_details is missing in processWithdrawFund', async () => {
      programTokenService.getAccount.mockResolvedValue(createMockProgramToken('test-account'));

      payoneerClient.ProcessWithdrawFund.mockResolvedValue([
        null,
        {
          errors: {
            error_description: 'Something failed',
          },
          error: null,
        },
      ]);

      await expect(service.processWithdrawFund(clientId, validInput)).rejects.toThrow(RpcException);
    });

    it('should fallback when error_details.code is undefined in processWithdrawFund', async () => {
      programTokenService.getAccount.mockResolvedValue(createMockProgramToken('test-account'));

      payoneerClient.ProcessWithdrawFund.mockResolvedValue([
        null,
        {
          errors: {
            error_details: {},
            error_description: 'Withdraw failed',
          },
          error: '',
        },
      ]);

      await expect(service.processWithdrawFund(clientId, validInput)).rejects.toThrow(RpcException);
    });

    it('should fallback to error.message when error_description is missing in processWithdrawFund', async () => {
      programTokenService.getAccount.mockResolvedValue(createMockProgramToken('test-account'));

      payoneerClient.ProcessWithdrawFund.mockResolvedValue([
        null,
        {
          errors: {},
          error: '',
        },
      ]);

      await expect(service.processWithdrawFund(clientId, validInput)).rejects.toThrow(RpcException);
    });
  });

  describe('submitCommitId', () => {
    const clientId = 'test-client';
    const accountId = 'test-account';
    const commitId = 'test-commit';

    it('should throw BadRequestException when required fields are missing', async () => {
      await expect(service.submitCommitId(clientId, '')).rejects.toThrow(RpcException);
      await expect(service.submitCommitId(clientId, null)).rejects.toThrow(RpcException);
    });

    it('should submit commit ID successfully', async () => {
      const mockResponse = { status: 'success' };
      programTokenService.getAccount.mockResolvedValue(createMockProgramToken(accountId));
      payoneerClient.submitCommitId.mockResolvedValue([mockResponse, null]);

      const result = await service.submitCommitId(clientId, commitId, accountId);
      expect(result).toEqual(mockResponse);
      expect(payoneerClient.submitCommitId).toHaveBeenCalledWith(clientId, accountId, commitId);
    });

    it('should throw RpcException when submission fails', async () => {
      programTokenService.getAccount.mockResolvedValue(createMockProgramToken(accountId));
      payoneerClient.submitCommitId.mockResolvedValue([null, { error: 'Submission failed', errors: {} }]);

      await expect(service.submitCommitId(clientId, commitId, accountId)).rejects.toThrow(RpcException);
    });

    it('should throw RpcException when client ID is empty', async () => {
      await expect(service.submitCommitId('', commitId)).rejects.toThrow(RpcException);
    });

    it('should throw RpcException when client ID is null', async () => {
      await expect(service.submitCommitId(null, commitId)).rejects.toThrow(RpcException);
    });

    it('should throw RpcException when account not found', async () => {
      programTokenService.getAccount.mockResolvedValue(null);
      await expect(service.submitCommitId(clientId, commitId, accountId)).rejects.toThrow(RpcException);
    });

    it('should handle network errors gracefully', async () => {
      programTokenService.getAccount.mockResolvedValue(createMockProgramToken(accountId));
      payoneerClient.submitCommitId.mockRejectedValue(new Error('Network error'));

      await expect(service.submitCommitId(clientId, commitId, accountId)).rejects.toThrow(RpcException);
    });

    it('should handle API timeout errors', async () => {
      programTokenService.getAccount.mockResolvedValue(createMockProgramToken(accountId));
      payoneerClient.submitCommitId.mockResolvedValue([
        null,
        { error: 'Request timeout', errors: { code: 408 } },
      ]);

      await expect(service.submitCommitId(clientId, commitId, accountId)).rejects.toThrow(RpcException);
    });

    it('should handle unauthorized errors', async () => {
      programTokenService.getAccount.mockResolvedValue(createMockProgramToken(accountId));
      payoneerClient.submitCommitId.mockResolvedValue([
        null,
        { error: 'Unauthorized', errors: { code: 401 } },
      ]);

      await expect(service.submitCommitId(clientId, commitId, accountId)).rejects.toThrow(RpcException);
    });

    it('should handle completely undefined error object', async () => {
      payoneerClient.submitCommitId.mockRejectedValue(undefined);

      await expect(service.submitCommitId(clientId, commitId, accountId)).rejects.toThrow(RpcException);
    });

    it('should fallback to status when error_details.code is missing', async () => {
      payoneerClient.submitCommitId.mockResolvedValue([
        null,
        {
          error: '',
          errors: {},
        },
      ]);

      await expect(service.submitCommitId(clientId, commitId, accountId)).rejects.toThrow(RpcException);
    });

    it('should fallback when error_details is missing', async () => {
      payoneerClient.submitCommitId.mockResolvedValue([
        null,
        {
          errors: {
            error_description: 'desc',
          },
          error: '',
        },
      ]);

      await expect(service.submitCommitId(clientId, commitId, accountId)).rejects.toThrow(RpcException);
    });

    it('should fallback when code inside error_details is missing', async () => {
      payoneerClient.submitCommitId.mockResolvedValue([
        null,
        {
          errors: {
            error_details: {},
            error_description: 'error',
          },
          error: '',
        },
      ]);

      await expect(service.submitCommitId(clientId, commitId, accountId)).rejects.toThrow(RpcException);
    });

    it('should use error.message when error_description is missing', async () => {
      payoneerClient.submitCommitId.mockResolvedValue([
        null,
        {
          errors: {},
          error: '',
        },
      ]);

      await expect(service.submitCommitId(clientId, commitId, accountId)).rejects.toThrow(RpcException);
    });
  });
});

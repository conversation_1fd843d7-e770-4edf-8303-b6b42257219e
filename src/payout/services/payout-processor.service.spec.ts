import { Test, TestingModule } from '@nestjs/testing';
import { PayoutProcessorService } from './payout-processor.service';
import { CommandBus } from '@nestjs/cqrs';
import { PaymentRequestService } from './payment-request.service';
import { BeneficiaryGrpcClient } from '../../common/external-service/payments/services/beneficiary-grpc-client.service';
import { PaymentProcessorDto } from '../dtos/payment-processor.dto';
import { PaymentRequestDto } from '../dtos/payment-request.dto';
import {
  BeneficiaryRole,
  PaymentRequestStatus,
  PaymentRequestType,
  PayoneerProvider,
} from '../../common/constants/enums';
import { Messages } from '../../common/constants/messages';
import { ProcessPayoutInstructionCommand } from '../commands/impl/process-payout-instruction.command';

describe('PayoutProcessorService', () => {
  let service: PayoutProcessorService;
  let commandBus: CommandBus;
  let paymentRequestService: PaymentRequestService;
  let beneficiaryGrpcService: BeneficiaryGrpcClient;

  const mockCommandBus = {
    execute: jest.fn(),
  };

  const mockPaymentRequestService = {
    fetchPaymentRequests: jest.fn(),
    getUniqueRequestIDs: jest.fn(),
    findManyAndUpdatePaymentRequest: jest.fn(),
  };

  const mockBeneficiaryGrpcService = {
    findOne: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PayoutProcessorService,
        { provide: CommandBus, useValue: mockCommandBus },
        { provide: PaymentRequestService, useValue: mockPaymentRequestService },
        { provide: BeneficiaryGrpcClient, useValue: mockBeneficiaryGrpcService },
      ],
    }).compile();

    service = module.get<PayoutProcessorService>(PayoutProcessorService);
    commandBus = module.get<CommandBus>(CommandBus);
    paymentRequestService = module.get<PaymentRequestService>(PaymentRequestService);
    beneficiaryGrpcService = module.get<BeneficiaryGrpcClient>(BeneficiaryGrpcClient);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('execute', () => {
    it('should handle data fetch error', async () => {
      const error = new Error('Fetch error');
      mockPaymentRequestService.fetchPaymentRequests.mockResolvedValue([null, error]);

      const setDataFetchErrorSpy = jest.spyOn(PaymentProcessorDto.prototype, 'setDataFetchError');

      const result = await service.execute(PayoneerProvider.Payoneer);

      expect(setDataFetchErrorSpy).toHaveBeenCalledWith(error);
      expect(result.length).toBe(1); // One PaymentProcessorDto should be in outputs
    });

    it('should handle empty payment requests', async () => {
      mockPaymentRequestService.fetchPaymentRequests.mockResolvedValue([[], null]);

      const result = await service.execute(PayoneerProvider.Payoneer);

      expect(result).toEqual([]);
    });

    it('should process payment requests successfully', async () => {
      const mockPaymentRequests = [{ id: 1 }, { id: 2 }];
      mockPaymentRequestService.fetchPaymentRequests.mockResolvedValue([mockPaymentRequests, null]);
      mockPaymentRequestService.getUniqueRequestIDs.mockReturnValue([['id1'], ['id2']]);
      mockPaymentRequestService.findManyAndUpdatePaymentRequest.mockResolvedValue([[{ id: 1 }], null]);
      mockBeneficiaryGrpcService.findOne.mockResolvedValue({
        data: { active: true, meta: { programId: 'prog1' } },
      });
      mockCommandBus.execute.mockResolvedValue({ failedPaymentRequests: [] });

      const result = await service.execute(PayoneerProvider.Payoneer);

      expect(result).toEqual([]);
    });

    it('should skip Payoneer update if uniqueReqIDsForPayoneer is undefined', async () => {
      const mockRequests = [{ unique_request_id: 'abc' }];

      mockPaymentRequestService.fetchPaymentRequests.mockResolvedValue([mockRequests, null]);

      // Simulate undefined for Payoneer IDs
      mockPaymentRequestService.getUniqueRequestIDs.mockReturnValue([undefined, ['id2']]);

      mockPaymentRequestService.findManyAndUpdatePaymentRequest.mockResolvedValue([[{ id: 1 }], null]);
      mockBeneficiaryGrpcService.findOne.mockResolvedValue({
        data: { meta: { programId: 'prog1' } },
      });
      mockCommandBus.execute.mockResolvedValue({ failedPaymentRequests: [] });

      const result = await service.execute(PayoneerProvider.Payoneer);

      expect(result).toEqual([]); // no output
    });

    it('should skip PayoneerGBT update if uniqueReqIDsForPayoneerGBT is undefined', async () => {
      const mockRequests = [{ unique_request_id: 'abc' }];

      mockPaymentRequestService.fetchPaymentRequests.mockResolvedValue([mockRequests, null]);

      // Simulate undefined for PayoneerGBT IDs
      mockPaymentRequestService.getUniqueRequestIDs.mockReturnValue([['id1'], undefined]);

      mockPaymentRequestService.findManyAndUpdatePaymentRequest.mockResolvedValue([[{ id: 1 }], null]);
      mockBeneficiaryGrpcService.findOne.mockResolvedValue({
        data: { meta: { programId: 'prog1' } },
      });
      mockCommandBus.execute.mockResolvedValue({ failedPaymentRequests: [] });

      const result = await service.execute(PayoneerProvider.PayoneerGBT);

      expect(result).toEqual([]);
    });
    it('should handle unexpected error with undefined error object', async () => {
      mockPaymentRequestService.fetchPaymentRequests.mockImplementationOnce(() => {
        throw undefined; // triggers error?.message and stack
      });

      const loggerSpy = jest.spyOn(service['logger'], 'error');

      const result = await service.execute(PayoneerProvider.Payoneer);

      expect(loggerSpy).toHaveBeenCalledWith({ message: undefined, stack: undefined });

      expect(result).toHaveLength(1);
      const error = result[0].getError();
      expect(error?.unexpected_error).toBeUndefined();
    });

    it('should cover ([] as PaymentRequestDto[]) fallback in execute when update returns undefined', async () => {
      const mockRequests = [{ unique_request_id: 'req-1' }];

      // Simulate fetch success
      mockPaymentRequestService.fetchPaymentRequests.mockResolvedValue([mockRequests, null]);

      // Simulate IDs found for Payoneer but not GBT
      mockPaymentRequestService.getUniqueRequestIDs.mockReturnValue([['req-1'], []]);

      // Simulate updatedPaymentRequests being undefined (triggers fallback)
      mockPaymentRequestService.findManyAndUpdatePaymentRequest.mockResolvedValue([undefined, null]);

      // Spy on separateRequests to throw so we don’t need to mock beneficiary
      jest.spyOn(service, 'separateRequests').mockImplementation(() => {
        throw undefined;
      });

      const loggerSpy = jest.spyOn(service['logger'], 'error');

      const result = await service.execute(PayoneerProvider.Payoneer);

      // Confirm fallback happened and error handled
      expect(loggerSpy).toHaveBeenCalledWith({
        message: undefined,
        stack: undefined,
      });

      expect(result).toHaveLength(1);
      expect(result[0].getError()?.unexpected_error).toBeUndefined();
    });

    it('should cover ([] as PaymentRequestDto[]) fallback in PayoneerGBT update path', async () => {
      const mockRequests = [{ unique_request_id: 'req-2' }];

      // Simulate fetch success
      mockPaymentRequestService.fetchPaymentRequests.mockResolvedValue([mockRequests, null]);

      // Simulate only GBT IDs returned
      mockPaymentRequestService.getUniqueRequestIDs.mockReturnValue([[], ['req-2']]);

      // Simulate update returning undefined → triggers fallback
      mockPaymentRequestService.findManyAndUpdatePaymentRequest.mockResolvedValue([undefined, null]);

      // Spy to throw and skip further logic
      jest.spyOn(service, 'separateRequests').mockImplementation(() => {
        throw undefined;
      });

      const loggerSpy = jest.spyOn(service['logger'], 'error');

      const result = await service.execute(PayoneerProvider.PayoneerGBT);

      // Ensure logger saw undefined
      expect(loggerSpy).toHaveBeenCalledWith({
        message: undefined,
        stack: undefined,
      });

      expect(result).toHaveLength(1);
    });
  });

  describe('separateRequests', () => {
    it('should skip requests without programId', async () => {
      const mockPaymentRequests = [
        {
          type: PaymentRequestType.PayOut,
          beneficiary: { beneficiary_id: 'ben1' },
        },
      ] as PaymentRequestDto[];

      mockBeneficiaryGrpcService.findOne.mockResolvedValueOnce({
        data: {
          active: true,
          meta: {}, // missing programId
        },
      });

      const result = await service.separateRequests(mockPaymentRequests);

      expect(Object.keys(result).length).toBe(0);
    });

    it('should skip inactive beneficiaries', async () => {
      const mockPaymentRequests = [
        {
          type: PaymentRequestType.PayOut,
          beneficiary: { beneficiary_id: 'ben1' },
        },
      ] as PaymentRequestDto[];

      mockBeneficiaryGrpcService.findOne.mockResolvedValueOnce({
        data: {
          active: false,
          meta: { programId: 'prog1' },
        },
      });

      const result = await service.separateRequests(mockPaymentRequests);

      expect(Object.keys(result).length).toBe(1);
    });

    it('logs and skips when programId is missing in beneficiary data', async () => {
      const mockRequest = {
        type: PaymentRequestType.PayOut,
        beneficiary: { beneficiary_id: 'ben-1' },
      } as PaymentRequestDto;

      mockBeneficiaryGrpcService.findOne.mockResolvedValue({
        data: {
          beneficiaryLegalEntityId: 'leg-123',
          provider: 'payoneer',
          meta: {}, // no programId
        },
      });

      const loggerSpy = jest.spyOn(service['logger'], 'error');

      const result = await service.separateRequests([mockRequest]);

      expect(result).toEqual({});
      expect(loggerSpy).toHaveBeenCalledWith(
        JSON.stringify({
          errorMessage: '[SLACK]: Beneficiary programId is not found',
          data: {
            beneficiaryLegalEntityId: 'leg-123',
            provider: 'payoneer',
          },
        }),
      );
    });

    it('adds secondary_currency if present and groups by programId', async () => {
      const mockRequest = {
        type: PaymentRequestType.PayOut,
        beneficiary: {
          beneficiary_id: 'ben-2',
        },
      } as PaymentRequestDto;

      mockBeneficiaryGrpcService.findOne.mockResolvedValue({
        data: {
          meta: {
            programId: 'prog-22',
            secondaryCurrency: 'EUR',
          },
        },
      });

      const result = await service.separateRequests([mockRequest]);

      expect(result).toEqual({
        'prog-22': [
          expect.objectContaining({
            beneficiary: expect.objectContaining({
              secondary_currency: 'EUR',
            }),
          }),
        ],
      });
    });

    it('handles case where secondaryCurrency is not present', async () => {
      const mockRequest = {
        type: PaymentRequestType.PayOut,
        beneficiary: {
          beneficiary_id: 'ben-3',
        },
      } as PaymentRequestDto;

      mockBeneficiaryGrpcService.findOne.mockResolvedValue({
        data: {
          meta: {
            programId: 'prog-33',
            // no secondaryCurrency
          },
        },
      });

      const result = await service.separateRequests([mockRequest]);

      expect(result).toEqual({
        'prog-33': [mockRequest],
      });
    });

    it('handles beneficiary response missing .data', async () => {
      const mockRequest = {
        type: PaymentRequestType.PayOut,
        beneficiary: {
          beneficiary_id: 'ben-4',
        },
      } as PaymentRequestDto;

      mockBeneficiaryGrpcService.findOne.mockResolvedValue({}); // no `data`

      const loggerSpy = jest.spyOn(service['logger'], 'error');

      const result = await service.separateRequests([mockRequest]);

      expect(result).toEqual({});
      expect(loggerSpy).toHaveBeenCalled();
    });

    it('should skip when paymentRequest.type is not undefined', async () => {
      const request = {
        beneficiary: { beneficiary_id: 'ben' },
      } as PaymentRequestDto;

      mockBeneficiaryGrpcService.findOne.mockResolvedValue({
        data: { meta: { programId: 'prog' } },
      });

      const result = await service.separateRequests([request]);
      expect(result).toEqual({});
    });

    it('should skip when paymentRequest is null', async () => {
      const request = null as unknown as PaymentRequestDto;

      const result = await service.separateRequests([request]);
      expect(result).toEqual({});
    });

    it('should append to existing programId array', async () => {
      const request = {
        type: PaymentRequestType.PayOut,
        beneficiary: { beneficiary_id: 'ben' },
      } as PaymentRequestDto;

      const findOneMock = {
        data: {
          meta: { programId: 'prog-1' },
        },
      };

      mockBeneficiaryGrpcService.findOne.mockResolvedValue(findOneMock);

      // Call twice with same programId to trigger existing array branch
      const result = await service.separateRequests([request, request]);

      expect(result['prog-1'].length).toBe(2);
    });

    it('should log and skip when findOne throws with undefined error', async () => {
      const mockRequest = {
        type: PaymentRequestType.PayOut,
        unique_request_id: 'req-1',
        beneficiary: { beneficiary_id: 'ben-5' },
        provider: PayoneerProvider.Payoneer,
      } as PaymentRequestDto;

      // Make findOne throw `undefined` → triggers error?.message, error?.stack
      mockBeneficiaryGrpcService.findOne.mockImplementationOnce(() => {
        throw undefined;
      });

      const loggerSpy = jest.spyOn(service['logger'], 'error');

      const result = await service.separateRequests([mockRequest]);

      expect(result).toEqual({});
      expect(loggerSpy).toHaveBeenCalledWith(
        JSON.stringify({
          errorMessage: '[SLACK]: Error while separating the payment requests',
          error: undefined,
          stack: undefined,
          data: {
            idempotencyKey: 'req-1',
            beneficiaryLegalEntityId: 'ben-5',
            provider: PayoneerProvider.Payoneer,
          },
        }),
      );
    });

    it('should log error with undefined error object', async () => {
      const mockRequest = {
        unique_request_id: 'req-1',
        type: PaymentRequestType.PayOut,
        beneficiary: { beneficiary_id: 'ben-1' },
        provider: PayoneerProvider.Payoneer,
      } as PaymentRequestDto;

      mockBeneficiaryGrpcService.findOne.mockImplementationOnce(() => {
        throw undefined;
      });

      const loggerSpy = jest.spyOn(service['logger'], 'error');

      const result = await service.separateRequests([mockRequest]);

      expect(result).toEqual({});
      expect(loggerSpy).toHaveBeenCalledWith(
        JSON.stringify({
          errorMessage: '[SLACK]: Error while separating the payment requests',
          error: undefined,
          stack: undefined,
          data: {
            idempotencyKey: 'req-1',
            beneficiaryLegalEntityId: 'ben-1',
            provider: PayoneerProvider.Payoneer,
          },
        }),
      );
    });

    it('should log error with real Error object', async () => {
      const error = new Error('grpc failed');
      const mockRequest = {
        unique_request_id: 'req-2',
        type: PaymentRequestType.PayOut,
        beneficiary: { beneficiary_id: 'ben-2' },
        provider: PayoneerProvider.Payoneer,
      } as PaymentRequestDto;

      mockBeneficiaryGrpcService.findOne.mockImplementationOnce(() => {
        throw error;
      });

      const loggerSpy = jest.spyOn(service['logger'], 'error');

      const result = await service.separateRequests([mockRequest]);

      expect(result).toEqual({});
      expect(loggerSpy).toHaveBeenCalledWith(
        JSON.stringify({
          errorMessage: '[SLACK]: Error while separating the payment requests',
          error: error.message,
          stack: error.stack,
          data: {
            idempotencyKey: 'req-2',
            beneficiaryLegalEntityId: 'ben-2',
            provider: PayoneerProvider.Payoneer,
          },
        }),
      );
    });

    it('should log error when beneficiary object is undefined', async () => {
      const error = new Error('grpc failed');
      const mockRequest = {
        unique_request_id: 'req-3',
        type: PaymentRequestType.PayOut,
        beneficiary: undefined,
        provider: PayoneerProvider.Payoneer,
      } as unknown as PaymentRequestDto;

      mockBeneficiaryGrpcService.findOne.mockImplementationOnce(() => {
        throw error;
      });

      const loggerSpy = jest.spyOn(service['logger'], 'error');

      const result = await service.separateRequests([mockRequest]);

      expect(result).toEqual({});
      expect(loggerSpy).toHaveBeenCalledWith(
        JSON.stringify({
          errorMessage: '[SLACK]: Error while separating the payment requests',
          error: error.message,
          stack: error.stack,
          data: {
            idempotencyKey: 'req-3',
            beneficiaryLegalEntityId: undefined,
            provider: PayoneerProvider.Payoneer,
          },
        }),
      );
    });

    it('should log error when paymentRequest is undefined', async () => {
      const error = new Error('unexpected error');
      const mockRequest = undefined as unknown as PaymentRequestDto;

      mockBeneficiaryGrpcService.findOne.mockImplementationOnce(() => {
        throw error;
      });

      const loggerSpy = jest.spyOn(service['logger'], 'error');

      const result = await service.separateRequests([mockRequest]);

      expect(result).toEqual({});
      expect(loggerSpy).toHaveBeenCalledWith(
        JSON.stringify({
          errorMessage: '[SLACK]: Error while separating the payment requests',
          error: error.message,
          stack: error.stack,
          data: {
            idempotencyKey: undefined,
            beneficiaryLegalEntityId: undefined,
            provider: undefined,
          },
        }),
      );
    });
  });

  describe('processPayoutPaymentRequests', () => {
    it('should process payout requests and return failed requests', async () => {
      const mockPaymentRequests = {
        prog1: [{ id: 1 } as unknown as PaymentRequestDto],
      } as Record<string, PaymentRequestDto[]>;

      const mockFailedRequests = [{ id: 1, error: 'Failed' }];
      mockCommandBus.execute.mockResolvedValue({ failedPaymentRequests: mockFailedRequests });

      const result = await service.processPayoutPaymentRequests(
        mockPaymentRequests,
        PayoneerProvider.Payoneer,
      );

      expect(result).toEqual(mockFailedRequests);
      expect(commandBus.execute).toHaveBeenCalledWith(expect.any(ProcessPayoutInstructionCommand));
    });

    it('should handle undefined failedPaymentRequests from commandBus.execute', async () => {
      const paymentRequestsByProgram = {
        prog1: [{ unique_request_id: 'req-1' } as PaymentRequestDto],
      };

      mockCommandBus.execute.mockResolvedValue({ failedPaymentRequests: undefined });

      const loggerSpy = jest.spyOn(service['logger'], 'log');

      const result = await service.processPayoutPaymentRequests(
        paymentRequestsByProgram,
        PayoneerProvider.Payoneer,
      );

      expect(result).toEqual([]);
      expect(loggerSpy).not.toHaveBeenCalled(); // since no failed requests
    });

    it('should log nothing when failedPaymentRequests is empty array', async () => {
      const paymentRequestsByProgram = {
        prog1: [{ unique_request_id: 'req-3' } as PaymentRequestDto],
      };

      mockCommandBus.execute.mockResolvedValue({ failedPaymentRequests: [] });

      const loggerSpy = jest.spyOn(service['logger'], 'log');

      const result = await service.processPayoutPaymentRequests(
        paymentRequestsByProgram,
        PayoneerProvider.Payoneer,
      );

      expect(result).toEqual([]);
      expect(loggerSpy).not.toHaveBeenCalled();
    });

    it('should log each failed payment request', async () => {
      const failedRequests = [
        { unique_request_id: 'fail-1' } as PaymentRequestDto,
        { unique_request_id: 'fail-2' } as PaymentRequestDto,
      ];

      const paymentRequestsByProgram = {
        prog1: [{ unique_request_id: 'fail-1' } as PaymentRequestDto],
      };

      mockCommandBus.execute.mockResolvedValue({ failedPaymentRequests: failedRequests });

      const loggerSpy = jest.spyOn(service['logger'], 'log');

      const result = await service.processPayoutPaymentRequests(
        paymentRequestsByProgram,
        PayoneerProvider.Payoneer,
      );

      expect(result).toEqual(failedRequests);
      expect(loggerSpy).toHaveBeenCalledTimes(2);
      expect(loggerSpy).toHaveBeenCalledWith({
        message: `[SLACK] payout creation failed for client_reference_id: fail-1 from payoneer or payoneer_gbt`,
      });
      expect(loggerSpy).toHaveBeenCalledWith({
        message: `[SLACK] payout creation failed for client_reference_id: fail-2 from payoneer or payoneer_gbt`,
      });
    });
  });
  describe('execute - handles update errors', () => {
    it('should handle payment request update error for Payoneer', async () => {
      const mockPaymentRequests = [{ unique_request_id: 'req1' }];

      mockPaymentRequestService.fetchPaymentRequests.mockResolvedValue([mockPaymentRequests, null]);
      mockPaymentRequestService.getUniqueRequestIDs.mockReturnValue([['req1'], []]);
      const updateError = new Error('Update error');

      mockPaymentRequestService.findManyAndUpdatePaymentRequest.mockResolvedValue([[], updateError]);

      // Spy on the method
      const setUpdateErrorSpy = jest.spyOn(PaymentProcessorDto.prototype, 'setPaymentRequestUpdateError');

      const result = await service.execute(PayoneerProvider.Payoneer);

      expect(setUpdateErrorSpy).toHaveBeenCalledWith(updateError);
      expect(result.length).toBe(1);
    });

    it('should handle payment request update error for PayoneerGBT', async () => {
      const mockPaymentRequests = [{ unique_request_id: 'req2' }];

      mockPaymentRequestService.fetchPaymentRequests.mockResolvedValue([mockPaymentRequests, null]);
      mockPaymentRequestService.getUniqueRequestIDs.mockReturnValue([[], ['req2']]);
      const updateError = new Error('Update error for GBT');

      mockPaymentRequestService.findManyAndUpdatePaymentRequest.mockResolvedValue([[], updateError]);

      const setUpdateErrorSpy = jest.spyOn(PaymentProcessorDto.prototype, 'setPaymentRequestUpdateError');

      const result = await service.execute(PayoneerProvider.PayoneerGBT);

      expect(setUpdateErrorSpy).toHaveBeenCalledWith(updateError);
      expect(result.length).toBe(1);
    });
  });

  describe('execute - handles unexpected errors', () => {
    it('should handle unexpected error and push PaymentProcessorDto with setUnexpectedError', async () => {
      const unexpectedError = new Error('Unexpected failure');

      // Make fetchPaymentRequests throw an error
      mockPaymentRequestService.fetchPaymentRequests.mockImplementation(() => {
        throw unexpectedError;
      });

      const setUnexpectedErrorSpy = jest.spyOn(PaymentProcessorDto.prototype, 'setUnexpectedError');

      const result = await service.execute(PayoneerProvider.Payoneer);

      expect(setUnexpectedErrorSpy).toHaveBeenCalledWith(unexpectedError);
      expect(result.length).toBe(1);
    });
  });

  describe('separateRequests - full optional chaining and branching', () => {
    const baseRequest = {
      type: PaymentRequestType.PayOut,
      beneficiary: { beneficiary_id: 'ben-1' },
    } as PaymentRequestDto;

    it('should handle null beneficiary response', async () => {
      mockBeneficiaryGrpcService.findOne.mockResolvedValue(null);

      const loggerSpy = jest.spyOn(service['logger'], 'error');
      const result = await service.separateRequests([baseRequest]);

      expect(result).toEqual({});
      expect(loggerSpy).toHaveBeenCalled();
    });

    it('should handle beneficiary without data', async () => {
      mockBeneficiaryGrpcService.findOne.mockResolvedValue({});

      const loggerSpy = jest.spyOn(service['logger'], 'error');
      const result = await service.separateRequests([baseRequest]);

      expect(result).toEqual({});
      expect(loggerSpy).toHaveBeenCalled();
    });

    it('should handle beneficiary without meta', async () => {
      mockBeneficiaryGrpcService.findOne.mockResolvedValue({
        data: {
          beneficiaryLegalEntityId: 'abc',
          provider: 'payoneer',
          meta: undefined,
        },
      });

      const loggerSpy = jest.spyOn(service['logger'], 'error');
      const result = await service.separateRequests([baseRequest]);

      expect(result).toEqual({});
      expect(loggerSpy).toHaveBeenCalledWith(
        JSON.stringify({
          errorMessage: '[SLACK]: Beneficiary programId is not found',
          data: {
            beneficiaryLegalEntityId: 'abc',
            provider: 'payoneer',
          },
        }),
      );
    });

    it('should handle meta without programId', async () => {
      mockBeneficiaryGrpcService.findOne.mockResolvedValue({
        data: {
          beneficiaryLegalEntityId: 'abc',
          provider: 'payoneer',
          meta: {},
        },
      });

      const loggerSpy = jest.spyOn(service['logger'], 'error');
      const result = await service.separateRequests([baseRequest]);

      expect(result).toEqual({});
      expect(loggerSpy).toHaveBeenCalledWith(
        JSON.stringify({
          errorMessage: '[SLACK]: Beneficiary programId is not found',
          data: {
            beneficiaryLegalEntityId: 'abc',
            provider: 'payoneer',
          },
        }),
      );
    });

    it('should handle programId present but no secondaryCurrency', async () => {
      const mockRequest = structuredClone(baseRequest);
      mockBeneficiaryGrpcService.findOne.mockResolvedValue({
        data: {
          meta: { programId: 'prog-123' },
        },
      });

      const result = await service.separateRequests([mockRequest]);

      expect(result).toEqual({
        'prog-123': [mockRequest],
      });
      expect(mockRequest.beneficiary.secondary_currency).toBeUndefined();
    });

    it('should handle programId with secondaryCurrency present', async () => {
      const mockRequest = structuredClone(baseRequest);
      mockBeneficiaryGrpcService.findOne.mockResolvedValue({
        data: {
          meta: {
            programId: 'prog-456',
            secondaryCurrency: 'GBP',
          },
        },
      });

      const result = await service.separateRequests([mockRequest]);

      expect(result).toEqual({
        'prog-456': [
          expect.objectContaining({
            beneficiary: expect.objectContaining({
              secondary_currency: 'GBP',
            }),
          }),
        ],
      });
    });

    it('should skip non-payout type requests even with programId', async () => {
      const nonPayoutRequest = {
        ...baseRequest,
        type: PaymentRequestType.PayIn, // not PayOut
      } as PaymentRequestDto;

      mockBeneficiaryGrpcService.findOne.mockResolvedValue({
        data: {
          meta: {
            programId: 'prog-789',
          },
        },
      });

      const result = await service.separateRequests([nonPayoutRequest]);
      expect(result).toEqual({});
    });
    it('should skip when beneficiary is undefined', async () => {
      const mockRequest = {
        type: PaymentRequestType.PayOut,
        beneficiary: { beneficiary_id: 'ben-6' },
      } as PaymentRequestDto;

      mockBeneficiaryGrpcService.findOne.mockResolvedValue(undefined);

      const loggerSpy = jest.spyOn(service['logger'], 'error');

      const result = await service.separateRequests([mockRequest]);

      expect(result).toEqual({});
      expect(loggerSpy).toHaveBeenCalled();
    });
  });
  describe('separateRequests - optional chaining coverage inside PayOut block', () => {
    const baseRequest = {
      type: PaymentRequestType.PayOut,
      beneficiary: { beneficiary_id: 'ben-id' },
    } as PaymentRequestDto;

    it('should skip if paymentRequest is null', async () => {
      const result = await service.separateRequests([null as unknown as PaymentRequestDto]);
      expect(result).toEqual({});
    });

    it('should skip if paymentRequest.type is undefined', async () => {
      const result = await service.separateRequests([
        { beneficiary: { beneficiary_id: 'x' } } as PaymentRequestDto,
      ]);
      expect(result).toEqual({});
    });

    it('should skip if beneficiary.data is undefined', async () => {
      const req = structuredClone(baseRequest);
      mockBeneficiaryGrpcService.findOne.mockResolvedValue({});

      const result = await service.separateRequests([req]);
      expect(result).toEqual({});
    });

    it('should skip if meta is undefined', async () => {
      const req = structuredClone(baseRequest);
      mockBeneficiaryGrpcService.findOne.mockResolvedValue({
        data: { meta: undefined },
      });

      const result = await service.separateRequests([req]);
      expect(result).toEqual({});
    });

    it('should skip if programId is undefined', async () => {
      const req = structuredClone(baseRequest);
      mockBeneficiaryGrpcService.findOne.mockResolvedValue({
        data: { meta: {} },
      });

      const result = await service.separateRequests([req]);
      expect(result).toEqual({});
    });

    it('should add to paymentRequestsPayout when programId exists and secondaryCurrency is present', async () => {
      const req = structuredClone(baseRequest);
      mockBeneficiaryGrpcService.findOne.mockResolvedValue({
        data: {
          meta: {
            programId: 'prog-123',
            secondaryCurrency: 'JPY',
          },
        },
      });

      const result = await service.separateRequests([req]);

      expect(result['prog-123']).toHaveLength(1);
      expect(result['prog-123'][0].beneficiary.secondary_currency).toBe('JPY');
    });

    it('should add to paymentRequestsPayout when programId exists and secondaryCurrency is missing', async () => {
      const req = structuredClone(baseRequest);
      mockBeneficiaryGrpcService.findOne.mockResolvedValue({
        data: {
          meta: {
            programId: 'prog-456',
          },
        },
      });

      const result = await service.separateRequests([req]);

      expect(result['prog-456']).toHaveLength(1);
      expect(result['prog-456'][0].beneficiary.secondary_currency).toBeUndefined();
    });
  });
});

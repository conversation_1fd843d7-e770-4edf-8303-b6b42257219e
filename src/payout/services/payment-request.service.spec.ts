import { Test, TestingModule } from '@nestjs/testing';
import { PaymentRequestService } from './payment-request.service';
import { PaymentRequestGrpcClient } from '../../common/external-service/payments/services/payment-request-grpc-client.service';
import { HttpStatus, Logger } from '@nestjs/common';
import { Currency, PaymentRequestStatus, PayoneerProvider } from '../../common/constants/enums';
import { PaymentRequestDto } from '../dtos/payment-request.dto';
import { ForexService } from '../../common/external-service/forex-service/service/forex-service';
import { PayoneerHttpClient } from '../../common/external-service/payoneer/services/payoneer.http.client.service';
import { ProgramCurrencyMappingRepository } from '../../common/repositories/program-currency-mapping.repository';
import { plainToClass } from 'class-transformer';

describe('PaymentRequestService', () => {
  let service: PaymentRequestService;
  let paymentRequestGrpcClient: PaymentRequestGrpcClient;

  const mockForexService = {
    getForexCharges: jest.fn(),
  };

  const mockPayoneerHttpClient = {
    queryProgramBalanceV4: jest.fn(),
  };

  const mockProgramCurrencyMappingRepository = {
    getProgramIdUsingCurrency: jest.fn(),
  };

  const mockPaymentRequestGrpcClient = {
    findMany: jest.fn(),
    updateOne: jest.fn(),
    updateMany: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaymentRequestService,
        {
          provide: PaymentRequestGrpcClient,
          useValue: mockPaymentRequestGrpcClient,
        },
        {
          provide: ForexService,
          useValue: mockForexService,
        },
        {
          provide: PayoneerHttpClient,
          useValue: mockPayoneerHttpClient,
        },
        {
          provide: ProgramCurrencyMappingRepository,
          useValue: mockProgramCurrencyMappingRepository,
        },
      ],
    })
      .setLogger(new Logger())
      .compile();

    service = module.get<PaymentRequestService>(PaymentRequestService);
    paymentRequestGrpcClient = module.get<PaymentRequestGrpcClient>(PaymentRequestGrpcClient);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('fetchPaymentRequests', () => {
    it('should fetch payment requests successfully', async () => {
      const mockResponse = {
        statusCode: HttpStatus.OK,
        data: {
          data: [{ id: '1', provider: PayoneerProvider.Payoneer }],
        },
      };
      mockPaymentRequestGrpcClient.findMany.mockResolvedValue(mockResponse);

      const [result, error] = await service.fetchPaymentRequests(PayoneerProvider.Payoneer);

      expect(error).toBeNull();
      expect(result).toHaveLength(1);
    });

    it('should handle errors when fetching payment requests', async () => {
      const mockError = new Error('Failed to fetch');
      mockPaymentRequestGrpcClient.findMany.mockRejectedValue(mockError);

      const [result, error] = await service.fetchPaymentRequests(PayoneerProvider.Payoneer);

      expect(result).toEqual([]);
      expect(error).toBeDefined();
    });
  });

  describe('findOneAndUpdatePaymentRequest', () => {
    it('should update payment request successfully', async () => {
      const mockResponse = {
        statusCode: HttpStatus.OK,
      };
      mockPaymentRequestGrpcClient.updateOne.mockResolvedValue(mockResponse);

      const query = { unique_request_id: '123' };
      const updateQuery = { status: PaymentRequestStatus.Pending };

      const error = await service.findOneAndUpdatePaymentRequest(
        query,
        updateQuery,
        PayoneerProvider.Payoneer,
      );

      expect(error).toBeNull();
    });

    it('should handle errors when updating payment request', async () => {
      const mockError = new Error('Update failed');
      mockPaymentRequestGrpcClient.updateOne.mockRejectedValue(mockError);

      const query = { unique_request_id: '123' };
      const updateQuery = { status: PaymentRequestStatus.Failed };

      const error = await service.findOneAndUpdatePaymentRequest(
        query,
        updateQuery,
        PayoneerProvider.Payoneer,
      );

      expect(error).toBeDefined();
    });
  });

  describe('findManyAndUpdatePaymentRequest', () => {
    it('should update multiple payment requests successfully', async () => {
      const mockResponse = {
        statusCode: HttpStatus.OK,
        data: {
          data: [{ id: '1' }, { id: '2' }],
        },
      };
      mockPaymentRequestGrpcClient.updateMany.mockResolvedValue(mockResponse);

      const query = { unique_request_ids: ['123', '456'] };
      const updateQuery = { status: PaymentRequestStatus.Pending };

      const [result, error] = await service.findManyAndUpdatePaymentRequest(
        query,
        updateQuery,
        PayoneerProvider.Payoneer,
      );

      expect(error).toBeNull();
      expect(result).toBeDefined();
    });
  });

  describe('getUniqueRequestIDs', () => {
    it('should separate IDs by provider correctly', () => {
      const paymentRequests: PaymentRequestDto[] = [
        { unique_request_id: '1', provider: PayoneerProvider.Payoneer } as PaymentRequestDto,
        { unique_request_id: '2', provider: PayoneerProvider.PayoneerGBT } as PaymentRequestDto,
      ];

      const [payoneerIds, payoneerGBTIds] = service.getUniqueRequestIDs(paymentRequests);

      expect(payoneerIds).toEqual(['1']);
      expect(payoneerGBTIds).toEqual(['2']);
    });
  });

  describe('getTimestampsByStatus', () => {
    it('should return correct timestamps for Pending status', () => {
      const result = service['getTimestampsByStatus'](PaymentRequestStatus.Pending);
      expect(result).toHaveProperty('sent_to_provider_at');
    });

    it('should return correct timestamps for Failed status', () => {
      const result = service['getTimestampsByStatus'](PaymentRequestStatus.Failed);
      expect(result).toHaveProperty('failed_by_provider_at');
    });

    it('should return correct timestamps for Rejected status', () => {
      const result = service['getTimestampsByStatus'](PaymentRequestStatus.Rejected);
      expect(result).toHaveProperty('rejected_by_system_at');
    });

    it('should return empty object for unknown status', () => {
      const result = service['getTimestampsByStatus']('UNKNOWN' as PaymentRequestStatus);
      expect(result).toEqual({});
    });
  });

  describe('fetchPaymentRequestsBasedOnProvider', () => {
    it('should return empty array when no data is found', async () => {
      const mockResponse = {
        statusCode: HttpStatus.OK,
        data: [] as any,
      };
      mockPaymentRequestGrpcClient.findMany.mockResolvedValue(mockResponse);

      const [result, error] = await service.fetchPaymentRequestsBasedOnProvider(PayoneerProvider.Payoneer);

      expect(error).toBeNull();
      expect(result).toEqual([]);
    });

    it('should handle bad request errors', async () => {
      const mockResponse = {
        statusCode: HttpStatus.BAD_REQUEST,
        errors: ['Invalid request'],
      };
      mockPaymentRequestGrpcClient.findMany.mockResolvedValue(mockResponse);

      const [result, error] = await service.fetchPaymentRequestsBasedOnProvider(PayoneerProvider.Payoneer);

      expect(result).toEqual([]);
      expect(error).toBeDefined();
      expect(error.message).toBe('Bad Request Exception');
    });

    it('should transform response data to DTOs', async () => {
      const mockData = [
        {
          id: '1',
          provider: PayoneerProvider.Payoneer,
          status: PaymentRequestStatus.Initiated,
        },
      ];

      const mockResponse = {
        statusCode: HttpStatus.OK,
        data: {
          data: mockData,
        },
      };
      mockPaymentRequestGrpcClient.findMany.mockResolvedValue(mockResponse);

      const [result, error] = await service.fetchPaymentRequestsBasedOnProvider(PayoneerProvider.Payoneer);

      expect(error).toBeNull();
      expect(result).toHaveLength(1);
      expect(result[0]).toBeInstanceOf(PaymentRequestDto);
    });
  });

  describe('findManyAndUpdatePaymentRequest', () => {
    it('should handle HTTP errors', async () => {
      const mockResponse = {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        errors: ['Server error'],
      };
      mockPaymentRequestGrpcClient.updateMany.mockResolvedValue(mockResponse);

      const query = { unique_request_ids: ['123'] };
      const updateQuery = { status: PaymentRequestStatus.Failed };

      const [result, error] = await service.findManyAndUpdatePaymentRequest(
        query,
        updateQuery,
        PayoneerProvider.Payoneer,
      );

      expect(result).toBeNull();
      expect(error).toBeDefined();
      expect(error.message).toContain('Server error');
    });

    it('should handle empty update response', async () => {
      const mockResponse = {
        statusCode: HttpStatus.OK,
        data: {
          data: [] as any,
        },
      };
      mockPaymentRequestGrpcClient.updateMany.mockResolvedValue(mockResponse);

      const query = { unique_request_ids: ['123'] };
      const updateQuery = { status: PaymentRequestStatus.Failed };

      const [result, error] = await service.findManyAndUpdatePaymentRequest(
        query,
        updateQuery,
        PayoneerProvider.Payoneer,
      );

      expect(error).toBeNull();
      expect(result).toEqual([]);
    });
  });

  describe('fetchPaymentRequests for PayoneerGBT provider', () => {
    it('should return an empty array if program balance is zero', async () => {
      // Arrange
      const rawMockPaymentRequests = [{ unique_request_id: 'req_1', amount: 100, currency: 'INR' }];
      const mockForexResponse = {
        currencyExchangeRates: [{ fromCurrency: 'INR', toCurrency: 'USD', originalExchangeRate: 0.012 }],
      };
      jest
        .spyOn(service, 'fetchPaymentRequestsBasedOnProvider')
        .mockResolvedValue([plainToClass(PaymentRequestDto, rawMockPaymentRequests), null]);
      mockForexService.getForexCharges.mockResolvedValue(mockForexResponse);
      mockProgramCurrencyMappingRepository.getProgramIdUsingCurrency.mockResolvedValue('gbt_program_id');
      mockPayoneerHttpClient.queryProgramBalanceV4.mockResolvedValue([{ result: { balance: 0 } }, null]);

      // Act
      const [result, error] = await service.fetchPaymentRequests(PayoneerProvider.PayoneerGBT);

      // Assert
      expect(error).toBeNull();
      expect(result).toHaveLength(0);
    });
  });

  describe('handlePayoneerGBTRequests', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should handle USD to USD conversion without forex service call', async () => {
      // Arrange
      const mockRequests = [
        { unique_request_id: 'req_1', amount: 100, currency: Currency.USD } as PaymentRequestDto,
      ];
      mockProgramCurrencyMappingRepository.getProgramIdUsingCurrency.mockResolvedValue('test_program_id');
      mockPayoneerHttpClient.queryProgramBalanceV4.mockResolvedValue([{ result: { balance: 200 } }, null]);

      // Act
      const [result, error] = await service['handlePayoneerGBTRequests'](mockRequests);

      // Assert
      expect(error).toBeNull();
      expect(result).toHaveLength(1);
      expect(result[0].unique_request_id).toBe('req_1');
      expect(mockForexService.getForexCharges).not.toHaveBeenCalled();
    });

    it('should fetch forex rates for non-USD currencies', async () => {
      // Arrange
      const mockRequests = [
        { unique_request_id: 'req_1', amount: 100, currency: Currency.EUR } as PaymentRequestDto,
        { unique_request_id: 'req_2', amount: 200, currency: Currency.GBP } as PaymentRequestDto,
      ];
      const mockForexResponse = {
        currencyExchangeRates: [
          { fromCurrency: Currency.EUR, toCurrency: Currency.USD, originalExchangeRate: 1.1 },
          { fromCurrency: Currency.GBP, toCurrency: Currency.USD, originalExchangeRate: 1.3 },
        ],
      };
      mockForexService.getForexCharges.mockResolvedValue(mockForexResponse);
      mockProgramCurrencyMappingRepository.getProgramIdUsingCurrency.mockResolvedValue('test_program_id');
      mockPayoneerHttpClient.queryProgramBalanceV4.mockResolvedValue([{ result: { balance: 500 } }, null]);

      // Act
      const [result, error] = await service['handlePayoneerGBTRequests'](mockRequests);

      // Assert
      expect(error).toBeNull();
      expect(result).toHaveLength(2);
      expect(mockForexService.getForexCharges).toHaveBeenCalledWith([
        { fromCurrency: Currency.EUR, toCurrency: Currency.USD },
        { fromCurrency: Currency.GBP, toCurrency: Currency.USD },
      ]);
    });

    it('should sort requests by converted USD amount in ascending order', async () => {
      // Arrange
      const mockRequests = [
        { unique_request_id: 'req_large', amount: 200, currency: Currency.EUR } as PaymentRequestDto, // 200 * 1.1 = 220 USD
        { unique_request_id: 'req_small', amount: 100, currency: Currency.EUR } as PaymentRequestDto, // 100 * 1.1 = 110 USD
        { unique_request_id: 'req_medium', amount: 150, currency: Currency.EUR } as PaymentRequestDto, // 150 * 1.1 = 165 USD
      ];
      const mockForexResponse = {
        currencyExchangeRates: [
          { fromCurrency: Currency.EUR, toCurrency: Currency.USD, originalExchangeRate: 1.1 },
        ],
      };
      mockForexService.getForexCharges.mockResolvedValue(mockForexResponse);
      mockProgramCurrencyMappingRepository.getProgramIdUsingCurrency.mockResolvedValue('test_program_id');
      mockPayoneerHttpClient.queryProgramBalanceV4.mockResolvedValue([{ result: { balance: 1000 } }, null]);

      // Act
      const [result, error] = await service['handlePayoneerGBTRequests'](mockRequests);

      // Assert
      expect(error).toBeNull();
      expect(result).toHaveLength(3);
      // Should be sorted by converted amount: small (110), medium (165), large (220)
      expect(result[0].unique_request_id).toBe('req_small');
      expect(result[1].unique_request_id).toBe('req_medium');
      expect(result[2].unique_request_id).toBe('req_large');
    });

    it('should filter requests based on program balance', async () => {
      // Arrange
      const mockRequests = [
        { unique_request_id: 'req_1', amount: 100, currency: Currency.EUR } as PaymentRequestDto, // 100 * 1.1 = 110 USD
        { unique_request_id: 'req_2', amount: 200, currency: Currency.EUR } as PaymentRequestDto, // 200 * 1.1 = 220 USD
        { unique_request_id: 'req_3', amount: 50, currency: Currency.EUR } as PaymentRequestDto, // 50 * 1.1 = 55 USD
      ];
      const mockForexResponse = {
        currencyExchangeRates: [
          { fromCurrency: Currency.EUR, toCurrency: Currency.USD, originalExchangeRate: 1.1 },
        ],
      };
      mockForexService.getForexCharges.mockResolvedValue(mockForexResponse);
      mockProgramCurrencyMappingRepository.getProgramIdUsingCurrency.mockResolvedValue('test_program_id');
      mockPayoneerHttpClient.queryProgramBalanceV4.mockResolvedValue([{ result: { balance: 200 } }, null]); // Only allows first two requests

      // Act
      const [result, error] = await service['handlePayoneerGBTRequests'](mockRequests);

      // Assert
      expect(error).toBeNull();
      expect(result).toHaveLength(2); // Only req_3 (55) and req_1 (110) fit within 200 balance
      const selectedIds = result.map((r) => r.unique_request_id);
      expect(selectedIds).toContain('req_3'); // 55 USD
      expect(selectedIds).toContain('req_1'); // 110 USD
      expect(selectedIds).not.toContain('req_2'); // 220 USD would exceed balance
    });

    it('should handle requests with zero or negative exchange rates', async () => {
      // Arrange
      const mockRequests = [
        { unique_request_id: 'req_1', amount: 100, currency: Currency.EUR } as PaymentRequestDto,
        { unique_request_id: 'req_2', amount: 200, currency: Currency.GBP } as PaymentRequestDto,
      ];
      const mockForexResponse = {
        currencyExchangeRates: [
          { fromCurrency: Currency.EUR, toCurrency: Currency.USD, originalExchangeRate: 0 }, // Zero rate
          { fromCurrency: Currency.GBP, toCurrency: Currency.USD, originalExchangeRate: 1.3 },
        ],
      };
      mockForexService.getForexCharges.mockResolvedValue(mockForexResponse);
      mockProgramCurrencyMappingRepository.getProgramIdUsingCurrency.mockResolvedValue('test_program_id');
      mockPayoneerHttpClient.queryProgramBalanceV4.mockResolvedValue([{ result: { balance: 500 } }, null]);

      // Act
      const [result, error] = await service['handlePayoneerGBTRequests'](mockRequests);

      // Assert
      expect(error).toBeNull();
      expect(result).toHaveLength(1); // Only req_2 should be included
      expect(result[0].unique_request_id).toBe('req_2');
    });

    it('should handle requests with missing currency', async () => {
      // Arrange
      const mockRequests = [
        { unique_request_id: 'req_1', amount: 100, currency: null } as PaymentRequestDto,
        { unique_request_id: 'req_2', amount: 200, currency: Currency.EUR } as PaymentRequestDto,
      ];
      const mockForexResponse = {
        currencyExchangeRates: [
          { fromCurrency: Currency.EUR, toCurrency: Currency.USD, originalExchangeRate: 1.1 },
        ],
      };
      mockForexService.getForexCharges.mockResolvedValue(mockForexResponse);
      mockProgramCurrencyMappingRepository.getProgramIdUsingCurrency.mockResolvedValue('test_program_id');
      mockPayoneerHttpClient.queryProgramBalanceV4.mockResolvedValue([{ result: { balance: 500 } }, null]);

      // Act
      const [result, error] = await service['handlePayoneerGBTRequests'](mockRequests);

      // Assert
      expect(error).toBeNull();
      expect(result).toHaveLength(1); // Only req_2 should be included
      expect(result[0].unique_request_id).toBe('req_2');
    });

    it('should handle forex service error', async () => {
      // Arrange
      const mockRequests = [
        { unique_request_id: 'req_1', amount: 100, currency: Currency.EUR } as PaymentRequestDto,
      ];
      mockForexService.getForexCharges.mockRejectedValue(new Error('Forex service error'));
      mockProgramCurrencyMappingRepository.getProgramIdUsingCurrency.mockResolvedValue('test_program_id');

      // Act & Assert
      await expect(service['handlePayoneerGBTRequests'](mockRequests)).rejects.toThrow('Forex service error');
    });

    it('should handle program balance fetch error', async () => {
      // Arrange
      const mockRequests = [
        { unique_request_id: 'req_1', amount: 100, currency: Currency.USD } as PaymentRequestDto,
      ];
      mockProgramCurrencyMappingRepository.getProgramIdUsingCurrency.mockResolvedValue('test_program_id');
      mockPayoneerHttpClient.queryProgramBalanceV4.mockResolvedValue([
        null,
        { error: 'Balance fetch failed' },
      ]);

      // Act & Assert
      await expect(service['handlePayoneerGBTRequests'](mockRequests)).rejects.toThrow(
        'Balance fetch failed',
      );
    });

    it('should handle program ID fetch error', async () => {
      // Arrange
      const mockRequests = [
        { unique_request_id: 'req_1', amount: 100, currency: Currency.USD } as PaymentRequestDto,
      ];
      mockProgramCurrencyMappingRepository.getProgramIdUsingCurrency.mockRejectedValue(
        new Error('Program ID fetch failed'),
      );

      // Act & Assert
      await expect(service['handlePayoneerGBTRequests'](mockRequests)).rejects.toThrow(
        'Program ID fetch failed',
      );
    });

    it('should handle insufficient balance for all requests', async () => {
      // Arrange
      const mockRequests = [
        { unique_request_id: 'req_1', amount: 100, currency: Currency.EUR } as PaymentRequestDto, // 100 * 1.1 = 110 USD
        { unique_request_id: 'req_2', amount: 200, currency: Currency.EUR } as PaymentRequestDto, // 200 * 1.1 = 220 USD
      ];
      const mockForexResponse = {
        currencyExchangeRates: [
          { fromCurrency: Currency.EUR, toCurrency: Currency.USD, originalExchangeRate: 1.1 },
        ],
      };
      mockForexService.getForexCharges.mockResolvedValue(mockForexResponse);
      mockProgramCurrencyMappingRepository.getProgramIdUsingCurrency.mockResolvedValue('test_program_id');
      mockPayoneerHttpClient.queryProgramBalanceV4.mockResolvedValue([{ result: { balance: 50 } }, null]); // Insufficient for any request

      // Act
      const [result, error] = await service['handlePayoneerGBTRequests'](mockRequests);

      // Assert
      expect(error).toBeNull();
      expect(result).toHaveLength(0); // No requests fit within balance
    });
  });

  describe('getUniqueCurrencyPairs', () => {
    it('should extract unique currency pairs from requests', () => {
      // Arrange
      const mockRequests = [
        { currency: Currency.EUR } as PaymentRequestDto,
        { currency: Currency.GBP } as PaymentRequestDto,
        { currency: Currency.EUR } as PaymentRequestDto, // Duplicate
        { currency: Currency.USD } as PaymentRequestDto,
      ];

      // Act
      const result = service['getUniqueCurrencyPairs'](mockRequests, Currency.USD);

      // Assert
      expect(result).toHaveLength(3); // EUR, GBP, USD (duplicates removed)
      expect(result).toEqual(
        expect.arrayContaining([
          { fromCurrency: Currency.EUR, toCurrency: Currency.USD },
          { fromCurrency: Currency.GBP, toCurrency: Currency.USD },
          { fromCurrency: Currency.USD, toCurrency: Currency.USD },
        ]),
      );
    });

    it('should skip requests with missing currency', () => {
      // Arrange
      const mockRequests = [
        { currency: Currency.EUR } as PaymentRequestDto,
        { currency: null } as PaymentRequestDto,
        { currency: undefined } as PaymentRequestDto,
      ];

      // Act
      const result = service['getUniqueCurrencyPairs'](mockRequests, Currency.USD);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({ fromCurrency: Currency.EUR, toCurrency: Currency.USD });
    });

    it('should return empty array for empty requests', () => {
      // Arrange
      const mockRequests: PaymentRequestDto[] = [];

      // Act
      const result = service['getUniqueCurrencyPairs'](mockRequests, Currency.USD);

      // Assert
      expect(result).toHaveLength(0);
    });
  });

  describe('getForexRatesMap', () => {
    it('should create map from forex service response', async () => {
      // Arrange
      const mockPairs = [
        { fromCurrency: Currency.EUR, toCurrency: Currency.USD },
        { fromCurrency: Currency.GBP, toCurrency: Currency.USD },
      ];
      const mockForexResponse = {
        currencyExchangeRates: [
          { fromCurrency: Currency.EUR, toCurrency: Currency.USD, originalExchangeRate: 1.1 },
          { fromCurrency: Currency.GBP, toCurrency: Currency.USD, originalExchangeRate: 1.3 },
        ],
      };
      mockForexService.getForexCharges.mockResolvedValue(mockForexResponse);

      // Act
      const result = await service['getForexRatesMap'](mockPairs);

      // Assert
      expect(result.size).toBe(2);
      expect(result.get('EURUSD')).toBe(1.1);
      expect(result.get('GBPUSD')).toBe(1.3);
    });

    it('should handle missing exchange rates in response', async () => {
      // Arrange
      const mockPairs = [{ fromCurrency: Currency.EUR, toCurrency: Currency.USD }];
      const mockForexResponse = {
        currencyExchangeRates: [
          { fromCurrency: Currency.EUR, toCurrency: Currency.USD, originalExchangeRate: undefined },
        ],
      };
      mockForexService.getForexCharges.mockResolvedValue(mockForexResponse);

      // Act
      const result = await service['getForexRatesMap'](mockPairs);

      // Assert
      expect(result.size).toBe(0);
    });

    it('should handle empty forex service response', async () => {
      // Arrange
      const mockPairs = [{ fromCurrency: Currency.EUR, toCurrency: Currency.USD }];
      const mockForexResponse = { currencyExchangeRates: null };
      mockForexService.getForexCharges.mockResolvedValue(mockForexResponse);

      // Act
      const result = await service['getForexRatesMap'](mockPairs);

      // Assert
      expect(result.size).toBe(0);
    });
  });

  describe('getEnrichedRequests', () => {
    it('should enrich requests with converted USD amounts', () => {
      // Arrange
      const mockRequests = [
        { unique_request_id: 'req_1', amount: 100, currency: Currency.EUR } as PaymentRequestDto,
        { unique_request_id: 'req_2', amount: 200, currency: Currency.GBP } as PaymentRequestDto,
      ];
      const mockForexRatesMap = new Map([
        ['EURUSD', 1.1],
        ['GBPUSD', 1.3],
      ]);

      // Act
      const result = service['getEnrichedRequests'](mockRequests, mockForexRatesMap, Currency.USD);

      // Assert
      expect(result).toHaveLength(2);
      expect(result[0]).toEqual(
        expect.objectContaining({
          unique_request_id: 'req_1',
          amount: 100,
          currency: Currency.EUR,
          convertedAmountUSD: 110,
        }),
      );
      expect(result[1]).toEqual(
        expect.objectContaining({
          unique_request_id: 'req_2',
          amount: 200,
          currency: Currency.GBP,
          convertedAmountUSD: 260,
        }),
      );
    });

    it('should skip requests with missing or zero exchange rates', () => {
      // Arrange
      const mockRequests = [
        { unique_request_id: 'req_1', amount: 100, currency: Currency.EUR } as PaymentRequestDto,
        { unique_request_id: 'req_2', amount: 200, currency: Currency.GBP } as PaymentRequestDto,
      ];
      const mockForexRatesMap = new Map([
        ['EURUSD', 1.1],
        ['GBPUSD', 0], // Zero rate
      ]);

      // Act
      const result = service['getEnrichedRequests'](mockRequests, mockForexRatesMap, Currency.USD);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].unique_request_id).toBe('req_1');
    });
  });

  describe('getProgramBalance', () => {
    it('should return program balance from API response', async () => {
      // Arrange
      const mockProgramId = 'test_program_id';
      const mockResponse = { result: { balance: 1000 } };
      mockPayoneerHttpClient.queryProgramBalanceV4.mockResolvedValue([mockResponse, null]);

      // Act
      const result = await service['getProgramBalance'](mockProgramId);

      // Assert
      expect(result).toBe(1000);
      expect(mockPayoneerHttpClient.queryProgramBalanceV4).toHaveBeenCalledWith(mockProgramId);
    });

    it('should throw error when balance fetch fails', async () => {
      // Arrange
      const mockProgramId = 'test_program_id';
      const mockError = { error: 'Balance fetch failed' };
      mockPayoneerHttpClient.queryProgramBalanceV4.mockResolvedValue([null, mockError]);

      // Act & Assert
      await expect(service['getProgramBalance'](mockProgramId)).rejects.toThrow('Balance fetch failed');
    });
  });

  describe('filterRequestsByBalance', () => {
    it('should filter requests within balance limit', () => {
      // Arrange
      const mockEnrichedRequests = [
        { unique_request_id: 'req_1', convertedAmountUSD: 50 } as PaymentRequestDto & {
          convertedAmountUSD: number;
        },
        { unique_request_id: 'req_2', convertedAmountUSD: 100 } as PaymentRequestDto & {
          convertedAmountUSD: number;
        },
        { unique_request_id: 'req_3', convertedAmountUSD: 200 } as PaymentRequestDto & {
          convertedAmountUSD: number;
        },
      ];
      const programBalance = 180;

      // Act
      const result = service['filterRequestsByBalance'](mockEnrichedRequests, programBalance);

      // Assert
      expect(result).toHaveLength(2); // Only first two requests fit within balance
      expect(result[0].unique_request_id).toBe('req_1');
      expect(result[1].unique_request_id).toBe('req_2');
      // convertedAmountUSD should be removed from result
      expect(result[0]).not.toHaveProperty('convertedAmountUSD');
      expect(result[1]).not.toHaveProperty('convertedAmountUSD');
    });

    it('should return empty array when no requests fit within balance', () => {
      // Arrange
      const mockEnrichedRequests = [
        { unique_request_id: 'req_1', convertedAmountUSD: 100 } as PaymentRequestDto & {
          convertedAmountUSD: number;
        },
      ];
      const programBalance = 50;

      // Act
      const result = service['filterRequestsByBalance'](mockEnrichedRequests, programBalance);

      // Assert
      expect(result).toHaveLength(0);
    });

    it('should handle empty enriched requests array', () => {
      // Arrange
      const mockEnrichedRequests: Array<PaymentRequestDto & { convertedAmountUSD: number }> = [];
      const programBalance = 1000;

      // Act
      const result = service['filterRequestsByBalance'](mockEnrichedRequests, programBalance);

      // Assert
      expect(result).toHaveLength(0);
    });
  });
});

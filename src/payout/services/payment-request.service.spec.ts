import { Test, TestingModule } from '@nestjs/testing';
import { PaymentRequestService } from './payment-request.service';
import { PaymentRequestGrpcClient } from '../../common/external-service/payments/services/payment-request-grpc-client.service';
import { HttpStatus, Logger } from '@nestjs/common';
import { Currency, PaymentRequestStatus, PayoneerProvider } from '../../common/constants/enums';
import { PaymentRequestDto } from '../dtos/payment-request.dto';
import { ForexService } from '../../common/external-service/forex-service/service/forex-service';
import { PayoneerHttpClient } from '../../common/external-service/payoneer/services/payoneer.http.client.service';
import { ProgramCurrencyMappingRepository } from '../../common/repositories/program-currency-mapping.repository';
import { plainToClass } from 'class-transformer';

describe('PaymentRequestService', () => {
  let service: PaymentRequestService;
  let paymentRequestGrpcClient: PaymentRequestGrpcClient;

  const mockForexService = {
    getForexCharges: jest.fn(),
  };

  const mockPayoneerHttpClient = {
    queryProgramBalanceV4: jest.fn(),
  };

  const mockProgramCurrencyMappingRepository = {
    getProgramIdUsingCurrency: jest.fn(),
  };

  const mockPaymentRequestGrpcClient = {
    findMany: jest.fn(),
    updateOne: jest.fn(),
    updateMany: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaymentRequestService,
        {
          provide: PaymentRequestGrpcClient,
          useValue: mockPaymentRequestGrpcClient,
        },
        {
          provide: ForexService,
          useValue: mockForexService,
        },
        {
          provide: PayoneerHttpClient,
          useValue: mockPayoneerHttpClient,
        },
        {
          provide: ProgramCurrencyMappingRepository,
          useValue: mockProgramCurrencyMappingRepository,
        },
      ],
    })
      .setLogger(new Logger())
      .compile();

    service = module.get<PaymentRequestService>(PaymentRequestService);
    paymentRequestGrpcClient = module.get<PaymentRequestGrpcClient>(PaymentRequestGrpcClient);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('fetchPaymentRequests', () => {
    it('should fetch payment requests successfully', async () => {
      const mockResponse = {
        statusCode: HttpStatus.OK,
        data: {
          data: [{ id: '1', provider: PayoneerProvider.Payoneer }],
        },
      };
      mockPaymentRequestGrpcClient.findMany.mockResolvedValue(mockResponse);

      const [result, error] = await service.fetchPaymentRequests(PayoneerProvider.Payoneer);

      expect(error).toBeNull();
      expect(result).toHaveLength(1);
    });

    it('should handle errors when fetching payment requests', async () => {
      const mockError = new Error('Failed to fetch');
      mockPaymentRequestGrpcClient.findMany.mockRejectedValue(mockError);

      const [result, error] = await service.fetchPaymentRequests(PayoneerProvider.Payoneer);

      expect(result).toEqual([]);
      expect(error).toBeDefined();
    });
  });

  describe('findOneAndUpdatePaymentRequest', () => {
    it('should update payment request successfully', async () => {
      const mockResponse = {
        statusCode: HttpStatus.OK,
      };
      mockPaymentRequestGrpcClient.updateOne.mockResolvedValue(mockResponse);

      const query = { unique_request_id: '123' };
      const updateQuery = { status: PaymentRequestStatus.Pending };

      const error = await service.findOneAndUpdatePaymentRequest(
        query,
        updateQuery,
        PayoneerProvider.Payoneer,
      );

      expect(error).toBeNull();
    });

    it('should handle errors when updating payment request', async () => {
      const mockError = new Error('Update failed');
      mockPaymentRequestGrpcClient.updateOne.mockRejectedValue(mockError);

      const query = { unique_request_id: '123' };
      const updateQuery = { status: PaymentRequestStatus.Failed };

      const error = await service.findOneAndUpdatePaymentRequest(
        query,
        updateQuery,
        PayoneerProvider.Payoneer,
      );

      expect(error).toBeDefined();
    });
  });

  describe('findManyAndUpdatePaymentRequest', () => {
    it('should update multiple payment requests successfully', async () => {
      const mockResponse = {
        statusCode: HttpStatus.OK,
        data: {
          data: [{ id: '1' }, { id: '2' }],
        },
      };
      mockPaymentRequestGrpcClient.updateMany.mockResolvedValue(mockResponse);

      const query = { unique_request_ids: ['123', '456'] };
      const updateQuery = { status: PaymentRequestStatus.Pending };

      const [result, error] = await service.findManyAndUpdatePaymentRequest(
        query,
        updateQuery,
        PayoneerProvider.Payoneer,
      );

      expect(error).toBeNull();
      expect(result).toBeDefined();
    });
  });

  describe('getUniqueRequestIDs', () => {
    it('should separate IDs by provider correctly', () => {
      const paymentRequests: PaymentRequestDto[] = [
        { unique_request_id: '1', provider: PayoneerProvider.Payoneer } as PaymentRequestDto,
        { unique_request_id: '2', provider: PayoneerProvider.PayoneerGBT } as PaymentRequestDto,
      ];

      const [payoneerIds, payoneerGBTIds] = service.getUniqueRequestIDs(paymentRequests);

      expect(payoneerIds).toEqual(['1']);
      expect(payoneerGBTIds).toEqual(['2']);
    });
  });

  describe('getTimestampsByStatus', () => {
    it('should return correct timestamps for Pending status', () => {
      const result = service['getTimestampsByStatus'](PaymentRequestStatus.Pending);
      expect(result).toHaveProperty('sent_to_provider_at');
    });

    it('should return correct timestamps for Failed status', () => {
      const result = service['getTimestampsByStatus'](PaymentRequestStatus.Failed);
      expect(result).toHaveProperty('failed_by_provider_at');
    });

    it('should return correct timestamps for Rejected status', () => {
      const result = service['getTimestampsByStatus'](PaymentRequestStatus.Rejected);
      expect(result).toHaveProperty('rejected_by_system_at');
    });

    it('should return empty object for unknown status', () => {
      const result = service['getTimestampsByStatus']('UNKNOWN' as PaymentRequestStatus);
      expect(result).toEqual({});
    });
  });

  describe('fetchPaymentRequestsBasedOnProvider', () => {
    it('should return empty array when no data is found', async () => {
      const mockResponse = {
        statusCode: HttpStatus.OK,
        data: [] as any,
      };
      mockPaymentRequestGrpcClient.findMany.mockResolvedValue(mockResponse);

      const [result, error] = await service.fetchPaymentRequestsBasedOnProvider(PayoneerProvider.Payoneer);

      expect(error).toBeNull();
      expect(result).toEqual([]);
    });

    it('should handle bad request errors', async () => {
      const mockResponse = {
        statusCode: HttpStatus.BAD_REQUEST,
        errors: ['Invalid request'],
      };
      mockPaymentRequestGrpcClient.findMany.mockResolvedValue(mockResponse);

      const [result, error] = await service.fetchPaymentRequestsBasedOnProvider(PayoneerProvider.Payoneer);

      expect(result).toEqual([]);
      expect(error).toBeDefined();
      expect(error.message).toBe('Bad Request Exception');
    });

    it('should transform response data to DTOs', async () => {
      const mockData = [
        {
          id: '1',
          provider: PayoneerProvider.Payoneer,
          status: PaymentRequestStatus.Initiated,
        },
      ];

      const mockResponse = {
        statusCode: HttpStatus.OK,
        data: {
          data: mockData,
        },
      };
      mockPaymentRequestGrpcClient.findMany.mockResolvedValue(mockResponse);

      const [result, error] = await service.fetchPaymentRequestsBasedOnProvider(PayoneerProvider.Payoneer);

      expect(error).toBeNull();
      expect(result).toHaveLength(1);
      expect(result[0]).toBeInstanceOf(PaymentRequestDto);
    });
  });

  describe('findManyAndUpdatePaymentRequest', () => {
    it('should handle HTTP errors', async () => {
      const mockResponse = {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        errors: ['Server error'],
      };
      mockPaymentRequestGrpcClient.updateMany.mockResolvedValue(mockResponse);

      const query = { unique_request_ids: ['123'] };
      const updateQuery = { status: PaymentRequestStatus.Failed };

      const [result, error] = await service.findManyAndUpdatePaymentRequest(
        query,
        updateQuery,
        PayoneerProvider.Payoneer,
      );

      expect(result).toBeNull();
      expect(error).toBeDefined();
      expect(error.message).toContain('Server error');
    });

    it('should handle empty update response', async () => {
      const mockResponse = {
        statusCode: HttpStatus.OK,
        data: {
          data: [] as any,
        },
      };
      mockPaymentRequestGrpcClient.updateMany.mockResolvedValue(mockResponse);

      const query = { unique_request_ids: ['123'] };
      const updateQuery = { status: PaymentRequestStatus.Failed };

      const [result, error] = await service.findManyAndUpdatePaymentRequest(
        query,
        updateQuery,
        PayoneerProvider.Payoneer,
      );

      expect(error).toBeNull();
      expect(result).toEqual([]);
    });
  });

  describe('fetchPaymentRequests for PayoneerGBT provider', () => {
    it('should return an empty array if program balance is zero', async () => {
      // Arrange
      const rawMockPaymentRequests = [{ unique_request_id: 'req_1', amount: 100, currency: 'INR' }];
      const mockForexResponse = { currencyExchangeRates: [{ fromCurrency: 'INR', toCurrency: 'USD', originalExchangeRate: 0.012 }] };
      jest.spyOn(service, 'fetchPaymentRequestsBasedOnProvider').mockResolvedValue([
        plainToClass(PaymentRequestDto, rawMockPaymentRequests),
        null,
      ]);
      mockForexService.getForexCharges.mockResolvedValue(mockForexResponse);
      mockProgramCurrencyMappingRepository.getProgramIdUsingCurrency.mockResolvedValue('gbt_program_id');
      mockPayoneerHttpClient.queryProgramBalanceV4.mockResolvedValue([{ result: { balance: 0 } }, null]);

      // Act
      const [result, error] = await service.fetchPaymentRequests(PayoneerProvider.PayoneerGBT);

      // Assert
      expect(error).toBeNull();
      expect(result).toHaveLength(0);
    });
  });
});

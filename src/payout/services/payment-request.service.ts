import { BadRequestException, HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { PaymentRequestDto } from '../dtos/payment-request.dto';
import { Currency, PaymentRequestStatus, PayoneerProvider } from '../../common/constants/enums';
import { plainToClass } from 'class-transformer';
import {
  IPaymentRequestExtendedV2Contract,
  IPaymentRequestQuery,
  IPaymentRequestUpdate,
} from '../../common/data-types/common.data-type';
import * as dayjs from 'dayjs';
import { isEmpty } from '../../common/helpers/utils';
import { PaymentRequestGrpcClient } from '../../common/external-service/payments/services/payment-request-grpc-client.service';
import { ForexService } from '../../common/external-service/forex-service/service/forex-service';
import { CurrencyPairs } from '../../common/external-service/forex-service/service/forex.dto';
import { PayoneerHttpClient } from '../../common/external-service/payoneer/services/payoneer.http.client.service';
import { ProgramCurrencyMappingRepository } from '../../common/repositories/program-currency-mapping.repository';
import * as Big from 'big.js';
@Injectable()
export class PaymentRequestService {
  private readonly logger = new Logger(PaymentRequestService.name);

  constructor(
    private readonly paymentRequestGrpcClient: PaymentRequestGrpcClient,
    private readonly forexService: ForexService,
    private readonly client: PayoneerHttpClient,
    private readonly programsRepository: ProgramCurrencyMappingRepository,
  ) {}

  async fetchPaymentRequests(provider: PayoneerProvider): Promise<[PaymentRequestDto[], Error]> {
    try {
      const [payoneerPaymentRequests, payoneerDataFetchError] =
        await this.fetchPaymentRequestsBasedOnProvider(provider);

      if (payoneerDataFetchError) {
        throw payoneerDataFetchError;
      }

      if (payoneerPaymentRequests.length !== 0 && provider === PayoneerProvider.PayoneerGBT) {
        const targetProgramBalanceCurrency = Currency.USD;

        const uniqueCurrencyPairs: { [key: string]: CurrencyPairs } = {};
        for (const request of payoneerPaymentRequests) {
          if (request.currency) {
            const pairKey = `${request.currency}${targetProgramBalanceCurrency}`;
            uniqueCurrencyPairs[pairKey] = {
              fromCurrency: request.currency,
              toCurrency: targetProgramBalanceCurrency,
            };
          }
        }
        const currencyPairs: CurrencyPairs[] = Object.values(uniqueCurrencyPairs);

        const forexServiceResponse = await this.forexService.getForexCharges(currencyPairs);

        // Map forex rates for easy lookup: "FROM_TO" -> originalExchangeRate
        const forexRatesMap = new Map<string, number>();

        if (
          forexServiceResponse.currencyExchangeRates &&
          forexServiceResponse.currencyExchangeRates.length > 0
        ) {
          for (const rate of forexServiceResponse.currencyExchangeRates) {
            if (rate.fromCurrency && rate.toCurrency && rate.originalExchangeRate !== undefined) {
              forexRatesMap.set(`${rate.fromCurrency}${rate.toCurrency}`, rate.originalExchangeRate);
            }
          }
        }

        // Step 1: Get program ID
        const targetProgramId = await this.programsRepository.getProgramIdUsingCurrency(
          targetProgramBalanceCurrency,
          PayoneerProvider.PayoneerGBT,
        );

        // Step 2: Map payoneerPaymentRequests to include convertedAmountUSD
        const enrichedRequests: Array<PaymentRequestDto & { convertedAmountUSD: number }> = [];

        for (const request of payoneerPaymentRequests) {
          const rateKey = `${request.currency}${targetProgramBalanceCurrency}`;
          const rate = forexRatesMap.get(rateKey);

          if (rate !== undefined && rate !== 0) {
            const convertedAmountUSD = Number(new Big(request.amount).mul(rate).toFixed(2));
            enrichedRequests.push({ ...request, convertedAmountUSD } as any);
          } else {
            this.logger.warn(
              `Missing or zero exchange rate for ${rateKey}. Skipping request ${request.unique_request_id}`,
            );
          }
        }

        // Step 3: Sort by converted amount
        enrichedRequests.sort((a, b) => a.convertedAmountUSD - b.convertedAmountUSD);

        // Step 4: Get program balance
        const [balanceResp, balanceError] = await this.client.queryProgramBalanceV4(targetProgramId);
        if (balanceError) throw new Error(balanceError.error);

        const programBalance = balanceResp.result.balance;

        // Step 5: Filter and remove convertedAmountUSD before push
        const selectedPaymentRequests: PaymentRequestDto[] = [];
        let currentSumUSD = 0;

        for (const [index, requestWithAmount] of enrichedRequests.entries()) {
          const { convertedAmountUSD, ...request } = requestWithAmount;

          if (currentSumUSD + convertedAmountUSD <= programBalance) {
            selectedPaymentRequests.push(request as PaymentRequestDto); // Only pure PaymentRequestDto
            currentSumUSD += convertedAmountUSD;
          } else {
            const skippedRequests = enrichedRequests.slice(index);
            this.logger.warn({
              message: `Skipping ${skippedRequests.length} payment requests due to insufficient program balance.`,
              programBalance,
              currentSumOfSelectedRequests: currentSumUSD,
              skippedRequests: skippedRequests.map((r) => ({
                unique_request_id: r.unique_request_id,
                convertedAmountUSD: r.convertedAmountUSD,
              })),
            });
            break;
          }
        }

        return [[...selectedPaymentRequests], null];
      }

      return [[...(payoneerPaymentRequests ?? ([] as PaymentRequestDto[]))], null];
    } catch (error) {
      this.logger.error({ message: error?.message, stack: error?.stack });
      return [[], error];
    }
  }

  async fetchPaymentRequestsBasedOnProvider(
    provider: PayoneerProvider,
  ): Promise<[PaymentRequestDto[], Error]> {
    try {
      const { statusCode, data, errors } = await this.paymentRequestGrpcClient.findMany({
        provider: provider,
        isValid: true,
        status: PaymentRequestStatus.Initiated,
      });

      if (statusCode !== HttpStatus.OK || errors) {
        throw new BadRequestException(errors);
      }

      if (isEmpty(data as Record<string, any>[])) return [[], null];

      const transformedDtos = plainToClass(PaymentRequestDto, data.data) as any;

      return [transformedDtos, null];
    } catch (error) {
      this.logger.error({ message: error?.message, stack: error?.stack });
      return [[], error];
    }
  }

  async findOneAndUpdatePaymentRequest(
    query: IPaymentRequestQuery,
    updateQuery: IPaymentRequestUpdate,
    provider: string,
  ): Promise<Error> {
    try {
      const { status } = updateQuery;
      if (status) {
        Object.assign(updateQuery, this.getTimestampsByStatus(status));
      }

      const { statusCode, errors } = await this.paymentRequestGrpcClient.updateOne({
        queryParams: {
          provider: provider,
          idempotencyKeys: [query.unique_request_id],
        },
        updateParams: {
          status: updateQuery.status,
          error: JSON.stringify(updateQuery.payment_error),
          providerPayload: JSON.stringify(updateQuery.provider_payload),
          providerSystemReferenceNumber: updateQuery.provider_system_reference_number,
          sentToProviderAt: updateQuery.sent_to_provider_at,
          failedByProviderAt: updateQuery.failed_by_provider_at,
          rejectedBySystemAt: updateQuery.rejected_by_system_at,
        },
      });

      if (statusCode !== HttpStatus.OK || errors) {
        throw new HttpException(JSON.stringify(errors), statusCode);
      }
      return null;
    } catch (error) {
      this.logger.error({ message: error?.message, stack: error?.stack });
      return error;
    }
  }

  async findManyAndUpdatePaymentRequest(
    query: IPaymentRequestQuery,
    updateQuery: IPaymentRequestUpdate,
    provider: string,
  ): Promise<[PaymentRequestDto[], Error]> {
    try {
      if (!query?.unique_request_ids?.length) {
        throw new BadRequestException('unique_request_ids is required');
      }

      const { statusCode, data, errors } = await this.paymentRequestGrpcClient.updateMany({
        queryParams: {
          provider: provider,
          idempotencyKeys: [...query.unique_request_ids],
          ...(query?.status && { status: query.status }),
        },
        updateParams: {
          status: updateQuery.status,
        },
      });

      if (statusCode !== HttpStatus.OK) {
        throw new HttpException(JSON.stringify(errors), statusCode);
      }

      const transformedDtos = plainToClass(PaymentRequestDto, data?.data) as any;
      return [transformedDtos, null];
    } catch (error) {
      this.logger.error({ message: error?.message, stack: error?.stack });
      return [null, error];
    }
  }

  private getTimestampsByStatus(status: PaymentRequestStatus) {
    switch (status) {
      case PaymentRequestStatus.Pending:
        return {
          sent_to_provider_at: dayjs().toDate(),
        } as Pick<IPaymentRequestExtendedV2Contract, 'sent_to_provider_at'>;
      case PaymentRequestStatus.Failed:
        return {
          failed_by_provider_at: dayjs().toDate(),
        } as Pick<IPaymentRequestExtendedV2Contract, 'failed_by_provider_at'>;
      case PaymentRequestStatus.Rejected:
        return {
          rejected_by_system_at: dayjs().toDate(),
        } as Pick<IPaymentRequestExtendedV2Contract, 'rejected_by_system_at'>;
      default:
        return {};
    }
  }

  getUniqueRequestIDs(paymentRequests: PaymentRequestDto[]): [string[], string[]] {
    const uniqueReqIDsForPayoneer: string[] = [];
    const uniqueReqIDsForPayoneerGBT: string[] = [];
    for (const paymentRequest of paymentRequests) {
      if (paymentRequest?.provider === PayoneerProvider.Payoneer) {
        uniqueReqIDsForPayoneer.push(paymentRequest.unique_request_id);
      } else if (paymentRequest?.provider === PayoneerProvider.PayoneerGBT) {
        uniqueReqIDsForPayoneerGBT.push(paymentRequest.unique_request_id);
      }
    }
    return [uniqueReqIDsForPayoneer, uniqueReqIDsForPayoneerGBT];
  }
}

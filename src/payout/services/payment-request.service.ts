import { BadRequestException, HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { PaymentRequestDto } from '../dtos/payment-request.dto';
import { Currency, PaymentRequestStatus, PayoneerProvider } from '../../common/constants/enums';
import { plainToClass } from 'class-transformer';
import {
  IPaymentRequestExtendedV2Contract,
  IPaymentRequestQuery,
  IPaymentRequestUpdate,
} from '../../common/data-types/common.data-type';
import * as dayjs from 'dayjs';
import { isEmpty } from '../../common/helpers/utils';
import { PaymentRequestGrpcClient } from '../../common/external-service/payments/services/payment-request-grpc-client.service';
import { ForexService } from '../../common/external-service/forex-service/service/forex-service';
import { CurrencyPairs } from '../../common/external-service/forex-service/service/forex.dto';
import { PayoneerHttpClient } from '../../common/external-service/payoneer/services/payoneer.http.client.service';
import { ProgramCurrencyMappingRepository } from '../../common/repositories/program-currency-mapping.repository';
import * as Big from 'big.js';
@Injectable()
export class PaymentRequestService {
  private readonly logger = new Logger(PaymentRequestService.name);

  constructor(
    private readonly paymentRequestGrpcClient: PaymentRequestGrpcClient,
    private readonly forexService: ForexService,
    private readonly client: PayoneerHttpClient,
    private readonly programsRepository: ProgramCurrencyMappingRepository,
  ) {}

  async fetchPaymentRequests(provider: PayoneerProvider): Promise<[PaymentRequestDto[], Error]> {
    try {
      const [payoneerPaymentRequests, payoneerDataFetchError] =
        await this.fetchPaymentRequestsBasedOnProvider(provider);

      if (payoneerDataFetchError) throw payoneerDataFetchError;

      if (payoneerPaymentRequests.length && provider === PayoneerProvider.PayoneerGBT) {
        return await this.handlePayoneerGBTRequests(payoneerPaymentRequests);
      }

      return [[...(payoneerPaymentRequests ?? [])], null];
    } catch (error) {
      this.logger.error({ message: error?.message, stack: error?.stack });
      return [[], error];
    }
  }

  private async handlePayoneerGBTRequests(
    requests: PaymentRequestDto[],
  ): Promise<[PaymentRequestDto[], Error]> {
    try {
      const targetCurrency = Currency.USD;
      const currencyPairs = this.getUniqueCurrencyPairs(requests, targetCurrency);

      let forexRatesMap: Map<string, number>;
      if (
        currencyPairs.length === 1 &&
        currencyPairs[0].fromCurrency === Currency.USD &&
        currencyPairs[0].toCurrency === Currency.USD
      ) {
        forexRatesMap = new Map([[`${Currency.USD}${Currency.USD}`, 1]]);
      } else {
        forexRatesMap = await this.getForexRatesMap(currencyPairs);
      }
      const programId = await this.programsRepository.getProgramIdUsingCurrency(
        targetCurrency,
        PayoneerProvider.PayoneerGBT,
      );

      const enrichedRequests = this.getEnrichedRequests(requests, forexRatesMap, targetCurrency);
      const sortedRequests = [...enrichedRequests].sort(
        (a, b) => a.convertedAmountUSD - b.convertedAmountUSD,
      );

      const programBalance = await this.getProgramBalance(programId);
      const selectedRequests = this.filterRequestsByBalance(sortedRequests, programBalance);

      return [[...selectedRequests], null];
    } catch (error) {
      this.logger.error({
        message: `Error in handlePayoneerGBTRequests: ${error?.message}`,
        stack: error?.stack,
      });
      throw error;
    }
  }

  private getUniqueCurrencyPairs(requests: PaymentRequestDto[], targetCurrency: Currency): CurrencyPairs[] {
    const uniquePairs: Record<string, CurrencyPairs> = {};

    for (const req of requests) {
      if (!req.currency) continue;

      const key = `${req.currency}${targetCurrency}`;
      uniquePairs[key] = { fromCurrency: req.currency, toCurrency: targetCurrency };
    }

    return Object.values(uniquePairs);
  }

  private async getForexRatesMap(pairs: CurrencyPairs[]): Promise<Map<string, number>> {
    const response = await this.forexService.getForexCharges(pairs);
    const map = new Map<string, number>();

    for (const rate of response.currencyExchangeRates ?? []) {
      if (rate.fromCurrency && rate.toCurrency && rate.originalExchangeRate !== undefined) {
        map.set(`${rate.fromCurrency}${rate.toCurrency}`, rate.originalExchangeRate);
      }
    }

    return map;
  }

  private getEnrichedRequests(
    requests: PaymentRequestDto[],
    forexRatesMap: Map<string, number>,
    targetCurrency: Currency,
  ): Array<PaymentRequestDto & { convertedAmountUSD: number }> {
    const enriched: Array<PaymentRequestDto & { convertedAmountUSD: number }> = [];

    for (const request of requests) {
      const rateKey = `${request.currency}${targetCurrency}`;
      const rate = forexRatesMap.get(rateKey);

      if (rate && rate !== 0) {
        const convertedAmountUSD = Number(new Big(request.amount).mul(rate).toFixed(2));
        enriched.push({ ...request, convertedAmountUSD } as any);
      } else {
        this.logger.warn(
          `Missing or zero exchange rate for ${rateKey}. Skipping request ${request.unique_request_id}`,
        );
      }
    }

    return enriched;
  }

  private async getProgramBalance(programId: string): Promise<number> {
    const [balanceResp, balanceError] = await this.client.queryProgramBalanceV4(programId);
    if (balanceError) throw new Error(balanceError.error);
    return balanceResp?.result?.balance;
  }

  private filterRequestsByBalance(
    enrichedRequests: Array<PaymentRequestDto & { convertedAmountUSD: number }>,
    programBalance: number,
  ): PaymentRequestDto[] {
    const selected: PaymentRequestDto[] = [];
    let totalUSD = 0;

    for (let i = 0; i < enrichedRequests.length; i++) {
      const { convertedAmountUSD, ...original } = enrichedRequests[i];

      if (totalUSD + convertedAmountUSD <= programBalance) {
        selected.push(original as PaymentRequestDto);
        totalUSD += convertedAmountUSD;
      } else {
        const skipped = enrichedRequests.slice(i);
        this.logger.warn({
          message: `Skipping ${skipped.length} payment requests due to insufficient program balance.`,
          programBalance,
          currentSumOfSelectedRequests: totalUSD,
          skippedRequests: skipped.map((r) => ({
            unique_request_id: r.unique_request_id,
            convertedAmountUSD: r.convertedAmountUSD,
          })),
        });
        break;
      }
    }

    return selected;
  }

  async fetchPaymentRequestsBasedOnProvider(
    provider: PayoneerProvider,
  ): Promise<[PaymentRequestDto[], Error]> {
    try {
      const { statusCode, data, errors } = await this.paymentRequestGrpcClient.findMany({
        provider: provider,
        isValid: true,
        status: PaymentRequestStatus.Initiated,
      });

      if (statusCode !== HttpStatus.OK || errors) {
        throw new BadRequestException(errors);
      }

      if (isEmpty(data as Record<string, any>[])) return [[], null];

      const transformedDtos = plainToClass(PaymentRequestDto, data.data) as any;

      return [transformedDtos, null];
    } catch (error) {
      this.logger.error({ message: error?.message, stack: error?.stack });
      return [[], error];
    }
  }

  async findOneAndUpdatePaymentRequest(
    query: IPaymentRequestQuery,
    updateQuery: IPaymentRequestUpdate,
    provider: string,
  ): Promise<Error> {
    try {
      const { status } = updateQuery;
      if (status) {
        Object.assign(updateQuery, this.getTimestampsByStatus(status));
      }

      const { statusCode, errors } = await this.paymentRequestGrpcClient.updateOne({
        queryParams: {
          provider: provider,
          idempotencyKeys: [query.unique_request_id],
        },
        updateParams: {
          status: updateQuery.status,
          error: JSON.stringify(updateQuery.payment_error),
          providerPayload: JSON.stringify(updateQuery.provider_payload),
          providerSystemReferenceNumber: updateQuery.provider_system_reference_number,
          sentToProviderAt: updateQuery.sent_to_provider_at,
          failedByProviderAt: updateQuery.failed_by_provider_at,
          rejectedBySystemAt: updateQuery.rejected_by_system_at,
        },
      });

      if (statusCode !== HttpStatus.OK || errors) {
        throw new HttpException(JSON.stringify(errors), statusCode);
      }
      return null;
    } catch (error) {
      this.logger.error({ message: error?.message, stack: error?.stack });
      return error;
    }
  }

  async findManyAndUpdatePaymentRequest(
    query: IPaymentRequestQuery,
    updateQuery: IPaymentRequestUpdate,
    provider: string,
  ): Promise<[PaymentRequestDto[], Error]> {
    try {
      if (!query?.unique_request_ids?.length) {
        throw new BadRequestException('unique_request_ids is required');
      }

      const { statusCode, data, errors } = await this.paymentRequestGrpcClient.updateMany({
        queryParams: {
          provider: provider,
          idempotencyKeys: [...query.unique_request_ids],
          ...(query?.status && { status: query.status }),
        },
        updateParams: {
          status: updateQuery.status,
        },
      });

      if (statusCode !== HttpStatus.OK) {
        throw new HttpException(JSON.stringify(errors), statusCode);
      }

      const transformedDtos = plainToClass(PaymentRequestDto, data?.data) as any;
      return [transformedDtos, null];
    } catch (error) {
      this.logger.error({ message: error?.message, stack: error?.stack });
      return [null, error];
    }
  }

  private getTimestampsByStatus(status: PaymentRequestStatus) {
    switch (status) {
      case PaymentRequestStatus.Pending:
        return {
          sent_to_provider_at: dayjs().toDate(),
        } as Pick<IPaymentRequestExtendedV2Contract, 'sent_to_provider_at'>;
      case PaymentRequestStatus.Failed:
        return {
          failed_by_provider_at: dayjs().toDate(),
        } as Pick<IPaymentRequestExtendedV2Contract, 'failed_by_provider_at'>;
      case PaymentRequestStatus.Rejected:
        return {
          rejected_by_system_at: dayjs().toDate(),
        } as Pick<IPaymentRequestExtendedV2Contract, 'rejected_by_system_at'>;
      default:
        return {};
    }
  }

  getUniqueRequestIDs(paymentRequests: PaymentRequestDto[]): [string[], string[]] {
    const uniqueReqIDsForPayoneer: string[] = [];
    const uniqueReqIDsForPayoneerGBT: string[] = [];
    for (const paymentRequest of paymentRequests) {
      if (paymentRequest?.provider === PayoneerProvider.Payoneer) {
        uniqueReqIDsForPayoneer.push(paymentRequest.unique_request_id);
      } else if (paymentRequest?.provider === PayoneerProvider.PayoneerGBT) {
        uniqueReqIDsForPayoneerGBT.push(paymentRequest.unique_request_id);
      }
    }
    return [uniqueReqIDsForPayoneer, uniqueReqIDsForPayoneerGBT];
  }
}

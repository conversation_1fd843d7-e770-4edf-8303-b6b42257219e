/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 5th April 2025
 */

import { Injectable, Logger } from '@nestjs/common';
import { PaymentRequestStatus, PayoneerProvider } from '../../common/constants/enums';
import { Messages } from '../../common/constants/messages';
import { PaymentProcessorDto } from '../dtos/payment-processor.dto';
import { PaymentRequestService } from './payment-request.service';
import { isArrayEmpty } from '../../common/helpers/utils';
import { PaymentRequestDto } from '../dtos/payment-request.dto';
import { CommandBus } from '@nestjs/cqrs';
import { ProcessAutoDebitPaymentRequestCommand } from '../commands/impl/process-auto-debit-payment-request.command';

@Injectable()
export class AutoDebitProcessorService {
  private readonly logger = new Logger(AutoDebitProcessorService.name);

  constructor(
    private readonly commandBus: CommandBus,
    private readonly paymentRequestService: PaymentRequestService,
  ) {}

  async execute(): Promise<PaymentProcessorDto[]> {
    let outputs: PaymentProcessorDto[] = [];
    const paymentProcessorDto = new PaymentProcessorDto();

    try {
      const [paymentRequests, dataFetchError] = await this.paymentRequestService.fetchPaymentRequests(
        PayoneerProvider.PayoneerAutoDebit,
      );

      if (dataFetchError) {
        paymentProcessorDto.setDataFetchError(dataFetchError);
        outputs.push(paymentProcessorDto);
        return outputs;
      }

      if (isArrayEmpty(paymentRequests)) {
        this.logger.log({ message: Messages.PAYMENT_REQUESTS_NOT_FOUND });
        return outputs;
      }

      this.logger.log({ message: 'payment requests are fetched successfully', data: paymentRequests });
      const uniqueRequestIds = paymentRequests.map((e) => e.unique_request_id);

      const updatedPaymentRequestsFinal = [];
      if (uniqueRequestIds?.length) {
        const [updatedPaymentRequests, updateError] =
          await this.paymentRequestService.findManyAndUpdatePaymentRequest(
            { unique_request_ids: uniqueRequestIds, status: PaymentRequestStatus.Initiated },
            {
              status: PaymentRequestStatus.Processing,
            },
            PayoneerProvider.PayoneerAutoDebit,
          );

        if (updateError) {
          paymentProcessorDto.setPaymentRequestUpdateError(updateError);
          outputs.push(paymentProcessorDto);
        }

        updatedPaymentRequestsFinal.push(...(updatedPaymentRequests || ([] as PaymentRequestDto[])));
      }

      const payoutResponses = await this.processAutoDebitPaymentRequests(updatedPaymentRequestsFinal);
      outputs = [...outputs, ...payoutResponses];
    } catch (error) {
      this.logger.error({ message: error?.message, stack: error?.stack });
      paymentProcessorDto.setUnexpectedError(error);
      outputs.push(paymentProcessorDto);
    }

    return outputs;
  }

  async processAutoDebitPaymentRequests(
    paymentRequests: PaymentRequestDto[],
  ): Promise<PaymentProcessorDto[]> {
    let outputs: PaymentProcessorDto[] = [];
    for (const paymentRequest of paymentRequests) {
      try {
        const paymentProcessorDto = new PaymentProcessorDto();
        const [result, error] = await this.commandBus.execute(
          new ProcessAutoDebitPaymentRequestCommand(paymentRequest),
        );

        if (error) {
          paymentProcessorDto.setPaymentCreateError(error);
        }

        await this.paymentRequestService.findOneAndUpdatePaymentRequest(
          { unique_request_id: paymentRequest.unique_request_id },
          {
            status: error ? PaymentRequestStatus.Failed : PaymentRequestStatus.Completed,
            provider_system_reference_number: paymentRequest.provider_system_reference_number,
            provider_payload: result,
            payment_error: error,
          },
          PayoneerProvider.PayoneerAutoDebit,
        );
      } catch (error) {
        this.logger.error({
          message: 'Error updating payment request',
          error: error?.message,
          stack: error?.stack,
          data: { id: paymentRequest.unique_request_id },
        });
      }
      return outputs;
    }
  }
}

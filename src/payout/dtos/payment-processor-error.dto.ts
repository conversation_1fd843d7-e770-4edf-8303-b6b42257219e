export class PaymentProcessorErrorDto {
  data_fetch_error: Error | Record<string, any>;
  validation_errors: Record<string, any>[];
  make_payload_error: Error | Record<string, any>;
  payment_create_error: Error | Record<string, any>;
  request_update_Error: Error | Record<string, any>;
  payout_status_fetch_error: Error | Record<string, any>;
  unexpected_error: Error | Record<string, any>;
  payment_request_provider_config: Error | Record<string, any>;
}

import { PaymentProcessorErrorDto } from './payment-processor-error.dto';
import { isObjectEmpty } from '../../common/helpers/utils';
import { Expose } from 'class-transformer';
import { IPaymentRequestQuery } from '../../common/data-types/common.data-type';

export class PaymentProcessorDto {
  @Expose() legal_entity_id: string;
  @Expose() unique_request_id: string;
  @Expose() unique_request_ids: string[];

  private readonly payment_error: PaymentProcessorErrorDto;

  constructor() {
    this.payment_error = new PaymentProcessorErrorDto();
  }

  getError(): PaymentProcessorErrorDto {
    return isObjectEmpty(this.payment_error) ? undefined : this.payment_error;
  }

  getFilterQueryV1(): IPaymentRequestQuery {
    return {
      unique_request_id: this.unique_request_id,
    };
  }

  getFilterQueryV2(): IPaymentRequestQuery {
    return {
      unique_request_ids: this.unique_request_ids,
    };
  }

  hasError(): boolean {
    return Object.entries(this.payment_error).length > 0;
  }

  setValidationErrors(error: Record<string, any>[]) {
    this.payment_error.validation_errors = error;
  }

  setPaymentRequestUpdateError(error: Error) {
    this.payment_error.request_update_Error = error;
  }

  setMakePayloadError(error: Error | Record<string, any>) {
    this.payment_error.make_payload_error = error;
  }

  setPaymentCreateError(error: Error | Record<string, any>) {
    this.payment_error.payment_create_error = error;
  }

  setProviderConfigErrors(error: Error) {
    this.payment_error.payment_request_provider_config = error;
  }

  setUnexpectedError(error: Error | Record<string, any>) {
    this.payment_error.unexpected_error = error;
  }

  setDataFetchError(error: Error | Record<string, any>) {
    this.payment_error.data_fetch_error = error;
  }

  setPayoutStatusFetchError(error: Error | Record<string, any>) {
    this.payment_error.payout_status_fetch_error = error;
  }
}

import {
  IsEnum,
  IsISO86<PERSON>,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  MaxLength,
  MinLength,
  Validate,
  ValidateNested,
} from 'class-validator';
import { Expose, Transform, Type } from 'class-transformer';
import {
  BeneficiaryRole,
  Currency,
  PaymentRequestStatus,
  PaymentRequestType,
  PaymentType,
  SwiftChargeType,
} from '../../common/constants/enums';
import { BaseDto } from '../../common/dtos/base.dto';
import { AmountValidator } from '../validators/amount.validator';
import { CurrencyValidator } from '../validators/currency.validator';
import { PaymentProviderValidator } from '../../common/validators/payment-provider.validator';
import { IPaymentRequestExtendedV2Contract } from '../../common/data-types/common.data-type';
import * as dayjs from 'dayjs';

export class PaymentRequestMeta {
  @IsNotEmpty()
  @IsString()
  @Expose()
  geography: string;

  @IsOptional()
  @IsString()
  @Expose()
  purposeCode?: string;

  @IsOptional()
  @IsString()
  @Expose()
  balanceId?: string;

  @IsOptional()
  @IsString()
  @Expose()
  balanceCurrency?: string;
}

export class PaymentBeneficiary {
  @IsNotEmpty()
  @IsString()
  @Expose({ name: 'providerBeneficiaryId' })
  beneficiary_id: string;

  @IsNotEmpty()
  @IsString()
  @MaxLength(3)
  @MinLength(3)
  @Expose({ name: 'payoutCurrency' })
  currency: string;

  @IsNotEmpty()
  @IsString()
  @MinLength(2)
  @MaxLength(2)
  @Expose({ name: 'bankCountry' })
  bank_country: string;

  @IsNotEmpty()
  @IsEnum(BeneficiaryRole)
  @Expose({ name: 'beneficiaryRole' })
  beneficiary_role: BeneficiaryRole;

  @IsOptional()
  @IsEnum(SwiftChargeType)
  @Expose({ name: 'swiftChargeType' })
  swift_charge_type: SwiftChargeType;

  // internal field to hold the secondary currency if applicable
  secondary_currency?: string;
}

export class ProviderClientConfig {
  @IsOptional()
  @Expose({ name: 'accountId' })
  account_id: string;
}

export class PaymentRequestDto extends BaseDto implements Partial<IPaymentRequestExtendedV2Contract> {
  @IsNotEmpty()
  @IsString()
  @Expose({ name: 'beneficiaryLegalEntityId' })
  beneficiary_legal_entity_id: string;

  @IsNotEmpty()
  @IsString()
  @Expose({ name: 'beneficiaryPayoutProfileId' })
  beneficiary_payout_profile_id: string;

  @IsNotEmpty()
  @IsString()
  @Expose({ name: 'clientLegalEntityId' })
  client_legal_entity_id: string;

  @IsNotEmpty()
  @IsString()
  @Expose({ name: 'clientProviderProfileId' })
  client_provider_profile_id: string;

  @IsOptional()
  @IsString()
  @Expose({ name: 'providerSystemReferenceNumber' })
  provider_system_reference_number: string;

  @IsNotEmpty()
  @Validate(PaymentProviderValidator)
  @Expose()
  provider: string;

  @IsNotEmpty()
  @IsNumber()
  @Validate(AmountValidator)
  @Expose({ name: 'destinationAmount' })
  amount: number;

  @IsNotEmpty()
  @IsEnum(Currency)
  @Validate(CurrencyValidator)
  @Expose({ name: 'destinationCurrency' })
  currency: string;

  @IsNotEmpty()
  @IsEnum(Currency)
  @Expose()
  sourceCurrency: Currency;

  @IsNotEmpty()
  @IsEnum(PaymentRequestStatus)
  @Expose()
  status: PaymentRequestStatus;

  @IsNotEmpty()
  @IsString()
  @Expose()
  checksum: string;

  @IsNotEmpty()
  @IsString()
  @Expose({ name: 'purposeOfPayment' })
  purpose_of_payment: string;

  @IsNotEmpty()
  @IsString()
  @Expose({ name: 'clientReference' })
  reference_number: string;

  @IsNotEmpty()
  @IsUUID()
  @Expose({ name: 'idempotencyKey' })
  unique_request_id: string;

  @IsNotEmpty()
  @IsISO8601()
  @Transform(({ value }) => (value ? dayjs(value).toISOString().substring(0, 10) : value))
  @Expose({ name: 'paymentDate' })
  payment_date: string;

  @IsNotEmpty()
  @IsObject()
  @ValidateNested({ each: true })
  @Type(() => PaymentBeneficiary)
  @Expose({ name: 'beneficiaryConfig' })
  beneficiary: PaymentBeneficiary;

  @IsNotEmpty()
  @IsObject()
  @ValidateNested({ each: true })
  @Type(() => ProviderClientConfig)
  @Expose({ name: 'clientConfig' })
  client_config: ProviderClientConfig;

  @Expose({ name: 'paymentType' })
  payment_type: PaymentType;

  @Expose({ name: 'isValid' })
  is_valid: boolean;

  @Expose()
  @IsNotEmpty()
  @IsString()
  @IsEnum(PaymentRequestType)
  type: PaymentRequestType;

  @IsNotEmpty()
  @IsObject()
  @Type(() => PaymentRequestMeta)
  @Expose({ name: 'meta' })
  meta: PaymentRequestMeta;
}

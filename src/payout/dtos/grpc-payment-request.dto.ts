import {
  BeneficiaryRole,
  Country,
  Currency,
  PaymentRequestStatus,
  SwiftChargeType,
} from '../../common/constants/enums';

export class GrpcPaymentRequestDto {
  beneficiaryLegalEntityId: string;
  clientLegalEntityId: string;
  beneficiaryPayoutProfileId: string;
  clientProviderProfileId: string;
  destinationAmount: number;
  status: PaymentRequestStatus;
  sourceCurrency: Currency;
  destinationCurrency: Currency;
  isValid: boolean;
  checksum: string;
  provider: string;
  reason: string;
  paymentType: string;
  purposeOfPayment: string;
  clientReference: string;
  idempotencyKey: string;
  paymentDate: string;
  beneficiaryConfig: {
    legalEntityId?: string;
    providerBeneficiaryId?: string;
    providerPayoutId?: string;
    payoutCurrency?: Currency;
    bankCountry?: Country;
    country?: Country;
    beneficiaryRole?: BeneficiaryRole;
    paymentTypes?: string[];
    swiftChargeType?: SwiftChargeType;
    walletId?: string;
    customerId?: string;
  };
  clientConfig: {
    walletId: string;
    accountId: string;
  };
  meta: {
    purposeCode?: string;
    geography: string;
  };
  type: string;
}

import { BadGatewayException, Injectable, Logger } from '@nestjs/common';
import { ProgramCurrencyMappingRepository } from '../../common/repositories/program-currency-mapping.repository';
import { PayoneerProvider } from '../../common/constants/enums';
import { findNestedKey } from '../../common/helpers/utils';

@Injectable()
export class ProgramService {
  private readonly logger = new Logger(ProgramService.name);
  private readonly defaultProgramCurrency = 'DEFAULT';
  private readonly defaultSearchKey = 'currency';

  constructor(private readonly programsRepository: ProgramCurrencyMappingRepository) {}

  async getProgramIdUsingCurrencyAndProvider(currency: string, provider: PayoneerProvider): Promise<string> {
    return this.programsRepository.getProgramIdUsingCurrency(currency, provider);
  }

  async getCurrencySupportedForPayoneerAccount(): Promise<string[]> {
    try {
      const programs = await this.programsRepository.getProgramsOnTheBasisOfProvider(
        PayoneerProvider.Payoneer,
      );
      if (programs?.length) {
        return programs.map((program) => program.currency);
      }
      return [];
    } catch (error) {
      this.logger.error(
        JSON.stringify({
          message: 'Error fetching supported currencies',
          error: error?.message,
          stack: error?.stack,
        }),
      );
      throw error;
    }
  }

  async getProgramIdFromGrpcInput(
    input: Record<any, any>,
    provider: PayoneerProvider,
    key: string = null,
  ): Promise<string> {
    try {
      key = key ?? this.defaultSearchKey;

      let currency = findNestedKey(input ?? {}, key);

      if (!currency && provider === PayoneerProvider.PayoneerGBT) {
        currency = this.defaultProgramCurrency;
      } else if (!currency) {
        throw new BadGatewayException('Currency not provided');
      }

      return this.getProgramIdUsingCurrencyAndProvider(currency, provider);
    } catch (error) {
      this.logger.error(
        JSON.stringify({
          message: 'Error fetching program id',
          error: error?.message,
          stack: error?.stack,
        }),
      );
      throw error;
    }
  }
}

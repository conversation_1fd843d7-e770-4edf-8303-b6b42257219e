import { Test, TestingModule } from '@nestjs/testing';
import { BadGatewayException } from '@nestjs/common';
import { ProgramService } from './program.service';
import { ProgramCurrencyMappingRepository } from '../../common/repositories/program-currency-mapping.repository';
import { PayoneerProvider } from '../../common/constants/enums';

describe('ProgramService', () => {
  let service: ProgramService;
  let repository: ProgramCurrencyMappingRepository;

  const mockProgramsRepository = {
    getProgramIdUsingCurrency: jest.fn(),
    getProgramsOnTheBasisOfProvider: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProgramService,
        {
          provide: ProgramCurrencyMappingRepository,
          useValue: mockProgramsRepository,
        },
      ],
    }).compile();

    service = module.get<ProgramService>(ProgramService);
    repository = module.get<ProgramCurrencyMappingRepository>(ProgramCurrencyMappingRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getProgramIdUsingCurrencyAndProvider', () => {
    it('should return program id', async () => {
      const expectedProgramId = 'program123';
      mockProgramsRepository.getProgramIdUsingCurrency.mockResolvedValue(expectedProgramId);

      const result = await service.getProgramIdUsingCurrencyAndProvider('USD', PayoneerProvider.Payoneer);
      expect(result).toBe(expectedProgramId);
      expect(repository.getProgramIdUsingCurrency).toHaveBeenCalledWith('USD', PayoneerProvider.Payoneer);
    });
  });

  describe('getCurrencySupportedForPayoneerAccount', () => {
    it('should return array of supported currencies', async () => {
      const mockPrograms = [{ currency: 'USD' }, { currency: 'EUR' }];
      mockProgramsRepository.getProgramsOnTheBasisOfProvider.mockResolvedValue(mockPrograms);

      const result = await service.getCurrencySupportedForPayoneerAccount();
      expect(result).toEqual(['USD', 'EUR']);
      expect(repository.getProgramsOnTheBasisOfProvider).toHaveBeenCalledWith(PayoneerProvider.Payoneer);
    });

    it('should return empty array when no programs found', async () => {
      mockProgramsRepository.getProgramsOnTheBasisOfProvider.mockResolvedValue([]);

      const result = await service.getCurrencySupportedForPayoneerAccount();
      expect(result).toEqual([]);
    });

    it('should throw error when repository call fails', async () => {
      const error = new Error('Repository error');
      mockProgramsRepository.getProgramsOnTheBasisOfProvider.mockRejectedValue(error);

      await expect(service.getCurrencySupportedForPayoneerAccount()).rejects.toThrow(error);
    });

    it('should return empty array when programs is undefined', async () => {
      mockProgramsRepository.getProgramsOnTheBasisOfProvider.mockResolvedValue(undefined);

      const result = await service.getCurrencySupportedForPayoneerAccount();
      expect(result).toEqual([]);
    });

    it('should return empty array when programs is null', async () => {
      mockProgramsRepository.getProgramsOnTheBasisOfProvider.mockResolvedValue(null);

      const result = await service.getCurrencySupportedForPayoneerAccount();
      expect(result).toEqual([]);
    });

    it('should handle null error object gracefully in currency fetch', async () => {
      mockProgramsRepository.getProgramsOnTheBasisOfProvider.mockRejectedValue(null);

      await expect(service.getCurrencySupportedForPayoneerAccount()).rejects.toBeNull();
    });
  });

  describe('getProgramIdFromGrpcInput', () => {
    it('should return program id for valid input', async () => {
      const expectedProgramId = 'program123';
      mockProgramsRepository.getProgramIdUsingCurrency.mockResolvedValue(expectedProgramId);

      const result = await service.getProgramIdFromGrpcInput({ currency: 'USD' }, PayoneerProvider.Payoneer);
      expect(result).toBe(expectedProgramId);
    });

    it('should use default currency for PayoneerGBT provider when currency not provided', async () => {
      const expectedProgramId = 'program123';
      mockProgramsRepository.getProgramIdUsingCurrency.mockResolvedValue(expectedProgramId);

      const result = await service.getProgramIdFromGrpcInput({}, PayoneerProvider.PayoneerGBT);
      expect(result).toBe(expectedProgramId);
      expect(repository.getProgramIdUsingCurrency).toHaveBeenCalledWith(
        'DEFAULT',
        PayoneerProvider.PayoneerGBT,
      );
    });

    it('should throw BadGatewayException when input is not provided', async () => {
      await expect(service.getProgramIdFromGrpcInput(null, PayoneerProvider.Payoneer)).rejects.toThrow(
        BadGatewayException,
      );
    });

    it('should throw BadGatewayException when currency is not provided for non-GBT provider', async () => {
      await expect(service.getProgramIdFromGrpcInput({}, PayoneerProvider.Payoneer)).rejects.toThrow(
        BadGatewayException,
      );
    });

    it('should throw error when repository call fails', async () => {
      const error = new Error('Repository error');
      mockProgramsRepository.getProgramIdUsingCurrency.mockRejectedValue(error);

      await expect(
        service.getProgramIdFromGrpcInput({ currency: 'USD' }, PayoneerProvider.Payoneer),
      ).rejects.toThrow(error);
    });

    it('should use provided key instead of default', async () => {
      const expectedProgramId = 'program456';
      mockProgramsRepository.getProgramIdUsingCurrency.mockResolvedValue(expectedProgramId);

      const input = { customKey: 'EUR' };

      const result = await service.getProgramIdFromGrpcInput(input, PayoneerProvider.Payoneer, 'customKey');

      expect(result).toBe(expectedProgramId);
      expect(repository.getProgramIdUsingCurrency).toHaveBeenCalledWith('EUR', PayoneerProvider.Payoneer);
    });
  });
});

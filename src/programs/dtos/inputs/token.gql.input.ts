import { Field, InputType } from '@nestjs/graphql';
import { BeneficiaryRegistrationPurpose } from '../../../beneficiary/dtos/inputs/beneficiary.graphql.input';

@InputType('PayoneerCreateAuthorizationLinkInput')
export class CreateAuthorizationLinkInput {
  @Field(() => String)
  redirectUrl: string;

  @Field(() => BeneficiaryRegistrationPurpose)
  purpose: BeneficiaryRegistrationPurpose;

  @Field(() => String, { nullable: true })
  state?: string;
}

@InputType('PayoneerGenerateAccessTokenInput')
export class GenerateAccessTokenInput {
  @Field(() => String)
  code: string;

  @Field(() => String)
  redirectUrl: string;

  @Field(() => BeneficiaryRegistrationPurpose, { nullable: true })
  purpose?: BeneficiaryRegistrationPurpose;
}

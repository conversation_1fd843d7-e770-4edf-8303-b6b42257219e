import { Field, ObjectType } from '@nestjs/graphql';

@ObjectType('PayoneerCreateAuthorizationLinkOutput')
export class CreateAuthorizationLinkOutput {
  @Field(() => String)
  link: string;
}

@ObjectType('PayoneerGenerateAccessTokenOutput')
export class GenerateAccessTokenOutput {
  @Field(() => String)
  tokenType: string;

  @Field(() => String)
  accessToken: string;

  @Field()
  expiresIn: number;

  @Field()
  consentedOn: number;

  @Field(() => String)
  scope: string;

  @Field(() => String)
  refreshToken: string;

  @Field()
  refreshTokenExpiresIn: number;

  @Field(() => String)
  idToken: string;
}

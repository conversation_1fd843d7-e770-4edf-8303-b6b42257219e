import { Test, TestingModule } from '@nestjs/testing';
import { ProgramTokenService } from '../../payin/service/program-token.service';
import { InternalServerErrorException } from '@nestjs/common';
import { ProgramTokenMutationResolver } from './program-token.resolver';
import { BeneficiaryRegistrationPurpose } from '../../beneficiary/dtos/inputs/beneficiary.graphql.input';

describe('ProgramTokenMutationResolver', () => {
  let resolver: ProgramTokenMutationResolver;
  let programTokenService: ProgramTokenService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProgramTokenMutationResolver,
        {
          provide: ProgramTokenService,
          useValue: {
            createAuthorizationLink: jest.fn(),
            generateAccessToken: jest.fn(),
          },
        },
      ],
    }).compile();

    resolver = module.get<ProgramTokenMutationResolver>(ProgramTokenMutationResolver);
    programTokenService = module.get<ProgramTokenService>(ProgramTokenService);
  });

  describe('createAuthorizationLink', () => {
    it('should return link wrapped in output', async () => {
      const mockLink = 'https://example.com/auth';
      (programTokenService.createAuthorizationLink as jest.Mock).mockReturnValue(mockLink);

      const input = {
        redirectUrl: 'https://example.com/callback',
        purpose: BeneficiaryRegistrationPurpose.SSO,
        state: 'abc123',
      };

      const result = await resolver.createAuthorizationLink(input);

      expect(programTokenService.createAuthorizationLink).toHaveBeenCalledWith(
        input.redirectUrl,
        input.purpose,
        input.state,
      );
      expect(result).toEqual({ link: mockLink });
    });
  });

  describe('generateAccessToken', () => {
    it('should return access token output on success', async () => {
      const mockData = {
        token_type: 'Bearer',
        access_token: 'access123',
        expires_in: 3600,
        consented_on: '2024-01-01T00:00:00Z',
        scope: 'read write',
        refresh_token: 'refresh123',
        refresh_token_expires_in: 7200,
        id_token: 'idtoken123',
      };
      (programTokenService.generateAccessToken as jest.Mock).mockResolvedValue([mockData, null]);

      const input = {
        code: 'auth-code',
        redirectUrl: 'https://example.com/callback',
        purpose: BeneficiaryRegistrationPurpose.SSO,
      };

      const result = await resolver.generateAccessToken(input);

      expect(programTokenService.generateAccessToken).toHaveBeenCalledWith(
        input.code,
        input.redirectUrl,
        input.purpose,
      );
      expect(result).toEqual({
        tokenType: mockData.token_type,
        accessToken: mockData.access_token,
        expiresIn: mockData.expires_in,
        consentedOn: mockData.consented_on,
        scope: mockData.scope,
        refreshToken: mockData.refresh_token,
        refreshTokenExpiresIn: mockData.refresh_token_expires_in,
        idToken: mockData.id_token,
      });
    });

    it('should throw InternalServerErrorException if error returned', async () => {
      const mockError = new Error('Token service error');
      (programTokenService.generateAccessToken as jest.Mock).mockResolvedValue([null, mockError]);

      const input = {
        code: 'auth-code',
        redirectUrl: 'https://example.com/callback',
        purpose: BeneficiaryRegistrationPurpose.SSO,
      };

      await expect(resolver.generateAccessToken(input)).rejects.toThrow(InternalServerErrorException);
      expect(programTokenService.generateAccessToken).toHaveBeenCalled();
    });

    it('should use default purpose SSO if purpose is not provided', async () => {
      (programTokenService.generateAccessToken as jest.Mock).mockResolvedValue([{}, null]);

      const input = {
        code: 'auth-code',
        redirectUrl: 'https://example.com/callback',
      };

      await resolver.generateAccessToken(input as any);

      expect(programTokenService.generateAccessToken).toHaveBeenCalledWith(
        input.code,
        input.redirectUrl,
        BeneficiaryRegistrationPurpose.SSO,
      );
    });
  });
});

import { Args, Mutation, Resolver } from '@nestjs/graphql';
import { ProgramTokenService } from '../../payin/service/program-token.service';
import { CreateAuthorizationLinkInput, GenerateAccessTokenInput } from '../dtos/inputs/token.gql.input';
import { CreateAuthorizationLinkOutput, GenerateAccessTokenOutput } from '../dtos/outputs/token.gql.output';
import { InternalServerErrorException, Logger } from '@nestjs/common';
import { BeneficiaryRegistrationPurpose } from '../../beneficiary/dtos/inputs/beneficiary.graphql.input';

@Resolver()
export class ProgramTokenMutationResolver {
  private readonly logger = new Logger(ProgramTokenMutationResolver.name);
  constructor(private readonly programTokenService: ProgramTokenService) {}

  @Mutation(() => CreateAuthorizationLinkOutput, {
    name: 'createAuthorizationLink',
    description: 'create auth link',
  })
  async createAuthorizationLink(
    @Args('input', { type: () => CreateAuthorizationLinkInput }) input: CreateAuthorizationLinkInput,
  ): Promise<CreateAuthorizationLinkOutput> {
    const link = this.programTokenService.createAuthorizationLink(
      input.redirectUrl,
      input.purpose,
      input.state,
    );
    return { link };
  }

  @Mutation(() => GenerateAccessTokenOutput, {
    name: 'generateAccessToken',
    description: 'generate access token from auth code',
  })
  async generateAccessToken(
    @Args('input', { type: () => GenerateAccessTokenInput }) input: GenerateAccessTokenInput,
  ): Promise<GenerateAccessTokenOutput> {
    const [data, error] = await this.programTokenService.generateAccessToken(
      input.code,
      input.redirectUrl,
      input.purpose ?? BeneficiaryRegistrationPurpose.SSO,
    );

    if (error) {
      this.logger.log(error);
      throw new InternalServerErrorException();
    }
    const result = new GenerateAccessTokenOutput();
    result.tokenType = data.token_type;
    result.accessToken = data.access_token;
    result.expiresIn = data.expires_in;
    result.consentedOn = data.consented_on;
    result.scope = data.scope;
    result.refreshToken = data.refresh_token;
    result.refreshTokenExpiresIn = data.refresh_token_expires_in;
    result.idToken = data.id_token;
    return result;
  }
}

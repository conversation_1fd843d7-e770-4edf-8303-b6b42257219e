import { Test, TestingModule } from '@nestjs/testing';
import { ProgramGrpcController } from './program.grpc.controller';
import { ProgramService } from '../services/program.service';
import { LoggerService } from '../../common/services/LoggerService';
import { payoneer } from '@skuad/proto-utils/dist/interface/proto/payoneer/payoneer';

describe('ProgramGrpcController', () => {
  let controller: ProgramGrpcController;
  let programService: ProgramService;
  let loggerService: LoggerService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ProgramGrpcController],
      providers: [
        {
          provide: ProgramService,
          useValue: {
            getCurrencySupportedForPayoneerAccount: jest.fn(),
          },
        },
        {
          provide: LoggerService,
          useValue: {
            log: jest.fn(),
            error: jest.fn(), // <-- add this so error logging works
          },
        },
      ],
    }).compile();

    controller = module.get<ProgramGrpcController>(ProgramGrpcController);
    programService = module.get<ProgramService>(ProgramService);
    loggerService = module.get<LoggerService>(LoggerService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getCurrencySupportedForPayoneerAccount', () => {
    it('should return currencies supported for Payoneer account', async () => {
      const mockCurrencies = ['USD', 'EUR'];
      jest.spyOn(programService, 'getCurrencySupportedForPayoneerAccount').mockResolvedValue(mockCurrencies);

      const result: payoneer.GetCurrencySupportedForPayoneerAccountOutput =
        await controller.getCurrencySupportedForPayoneerAccount();

      expect(result).toEqual({ currencies: mockCurrencies });
      expect(programService.getCurrencySupportedForPayoneerAccount).toHaveBeenCalledTimes(1);
    });

    it('should handle errors when service method fails', async () => {
      const errorMessage = 'Error fetching currencies';
      jest
        .spyOn(programService, 'getCurrencySupportedForPayoneerAccount')
        .mockRejectedValue(new Error(errorMessage));

      await expect(controller.getCurrencySupportedForPayoneerAccount()).rejects.toThrow(errorMessage);
      expect(programService.getCurrencySupportedForPayoneerAccount).toHaveBeenCalledTimes(1);
      expect(loggerService.error).toHaveBeenCalled(); // Validate error was logged
    });

    it('should log successful result', async () => {
      const mockCurrencies = ['USD', 'EUR'];
      jest.spyOn(programService, 'getCurrencySupportedForPayoneerAccount').mockResolvedValue(mockCurrencies);

      await controller.getCurrencySupportedForPayoneerAccount();

      expect(loggerService.log).toHaveBeenCalled(); // Just check it was called
    });
  });
});

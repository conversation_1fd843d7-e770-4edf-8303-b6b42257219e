import { Controller, Injectable, UseInterceptors } from '@nestjs/common';
import { LoggerService } from '../../common/services/LoggerService';
import { ProgramService } from '../services/program.service';
import { GrpcMethod } from '@nestjs/microservices';
import { payoneer } from '@skuad/proto-utils/dist/interface/proto/payoneer/payoneer';
import { LogAfter } from 'skuad-utils-ts/dist/common';
import { ServiceType } from 'skuad-utils-ts/dist/common/constants/enums';
import { RpcLoggingInterceptor } from '@skuad/proto-utils/dist/common/interceptor/grpc.interceptor';

@Controller()
@Injectable()
@UseInterceptors(RpcLoggingInterceptor)
export class ProgramGrpcController {
  constructor(
    private readonly programService: ProgramService,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    private readonly logger: LoggerService,
  ) {}

  @GrpcMethod('PayoneerRpcService', 'getCurrencySupportedForPayoneerAccount')
  @LogAfter('logger', ServiceType.Grpc)
  async getCurrencySupportedForPayoneerAccount(): Promise<payoneer.GetCurrencySupportedForPayoneerAccountOutput> {
    const result = await this.programService.getCurrencySupportedForPayoneerAccount();
    return {
      currencies: result,
    };
  }
}

import { AppConfigService } from '../../config/services/app.config.service';
import { SchedulerRegistry } from '@nestjs/schedule';
import { CronJob } from 'cron';
import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { noop } from 'rxjs';
import { PayoutProcessorService } from '../../payout/services/payout-processor.service';
import { ICronConfig } from '../interface/cron.interface';
import { ProgramTokenService } from '../../payin/service/program-token.service';
import { PayoneerProvider } from '../../common/constants/enums';
import { AutoDebitProcessorService } from '../../payout/services/auto-debit-processor.service';

interface Cron {
  start: () => any;
}

@Injectable()
export class CronService implements OnModuleInit {
  private readonly logger = new Logger(CronService.name);
  constructor(
    private readonly config: AppConfigService,
    private readonly schedulerRegistry: SchedulerRegistry,
    private readonly service: PayoutProcessorService,
    private readonly programTokenService: ProgramTokenService,
    private readonly autoDebitProcessorService: AutoDebitProcessorService,
  ) {}

  onModuleInit() {
    setTimeout(async () => {
      await this.run();
      this.logger.log({ name: CronService.name, message: 'CronService started successfully' });
    }, 1000);
  }

  async run(): Promise<void> {
    this.fetchPendingPayoutsCronJobForPayoneer().start();
    this.fetchPendingPayoutsCronJobForPayoneerGBT().start();
    this.refreshExpiringTokensCronJob().start();
    this.populateFundingAccountsCronJob().start();
    this.autoDebitProcessorCronJob().start();
  }

  private fetchPendingPayoutsCronJobForPayoneer(): Cron {
    const cronConfig = this.config.cron().fetchPendingPayoutsCronConfig();
    if (!cronConfig) {
      this.logger.log({ message: 'fetch pending payouts cron job is disabled' });
      return { start: () => noop() };
    }
    return this.getCronJob(cronConfig, async () => {
      try {
        this.logger.log({ message: 'fetch pending payouts cron job started' });
        await this.service.execute(PayoneerProvider.Payoneer);
        this.logger.log({ message: 'fetch pending payouts cron job completed' });
      } catch (e) {
        this.logger.error({ error: e, message: 'fetch pending payouts cron job failed' });
      }
    });
  }

  private fetchPendingPayoutsCronJobForPayoneerGBT(): Cron {
    const cronConfig = this.config.cron().fetchPendingPayoutsCronConfigForPayoneerGBT();
    if (!cronConfig) {
      return { start: () => noop() };
    }
    return this.getCronJob(cronConfig, async () => {
      try {
        this.logger.log({ message: 'fetch pending payouts cron job started' });
        await this.service.execute(PayoneerProvider.PayoneerGBT);
        this.logger.log({ message: 'fetch pending payouts cron job completed' });
      } catch (e) {
        this.logger.error({ error: e, message: 'fetch pending payouts cron job failed' });
      }
    });
  }

  private refreshExpiringTokensCronJob(): Cron {
    const cronConfig = this.config.cron().refreshTokenCronConfig();
    if (!cronConfig) {
      this.logger.warn({ message: 'refresh expiring tokens cron job not configured' });
      return { start: () => noop() };
    }
    return this.getCronJob(cronConfig, async () => {
      try {
        this.logger.log({ message: 'refresh expiring tokens cron job started' });
        await this.programTokenService.refreshTokenToBeExpired();
        this.logger.log({ message: 'refresh expiring tokens cron job completed' });
      } catch (e) {
        this.logger.error({ error: e, message: 'refresh expiring tokens cron job failed' });
      }
    });
  }

  private populateFundingAccountsCronJob(): Cron {
    const cronConfig = this.config.cron().populateFundingAccounts();
    if (!cronConfig) {
      this.logger.warn({ message: 'populate funding accounts cron job not configured' });
      return { start: () => noop() };
    }
    return this.getCronJob(cronConfig, async () => {
      try {
        this.logger.log({ message: 'populate funding accounts cron job started' });
        await this.programTokenService.populateFundingAccounts();
        this.logger.log({ message: 'populate funding accounts cron job completed' });
      } catch (e) {
        this.logger.error({
          error: e,
          message: 'populate funding accounts cron job failed',
        });
      }
    });
  }

  private autoDebitProcessorCronJob(): Cron {
    const cronConfig = this.config.cron().autoDebitProcessor();
    if (!cronConfig) {
      this.logger.warn({ message: 'auto debit process cron job not configured' });
      return { start: () => noop() };
    }
    return this.getCronJob(cronConfig, async () => {
      try {
        this.logger.log({ message: 'auto debit process cron job started' });
        await this.autoDebitProcessorService.execute();
        this.logger.log({ message: 'auto debit process cron job completed' });
      } catch (e) {
        this.logger.error({
          error: e,
          message: 'auto debit process cron job failed',
        });
      }
    });
  }

  private getCronJob(config: ICronConfig, onTickFunction: () => void): Cron {
    const { cronName, cronPattern, cronTimeZone, cronOptions } = config;
    const { enabled } = cronOptions;
    const cron = {} as Cron;
    if (enabled) {
      console.log('Cron job added', cronName);
      const cronJob = new CronJob(cronPattern, onTickFunction, null, false, cronTimeZone);
      this.schedulerRegistry.addCronJob(cronName, cronJob);
      cron.start = () => cronJob.start();
    } else {
      console.log('Cron disabled', cronName);
      cron.start = () => noop();
    }
    return cron;
  }
}

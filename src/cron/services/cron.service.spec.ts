import { Test, TestingModule } from '@nestjs/testing';
import { CronService } from './cron.service';
import { AppConfigService } from '../../config/services/app.config.service';
import { SchedulerRegistry } from '@nestjs/schedule';
import { PayoutProcessorService } from '../../payout/services/payout-processor.service';
import { ProgramTokenService } from '../../payin/service/program-token.service';
import { AutoDebitProcessorService } from '../../payout/services/auto-debit-processor.service';
import { CronJob } from 'cron';
import { PayoneerProvider } from '../../common/constants/enums';

describe('CronService', () => {
  let service: CronService;
  let payoutProcessorService: PayoutProcessorService;
  let programTokenService: ProgramTokenService;
  let autoDebitProcessorService: AutoDebitProcessorService;
  let schedulerRegistry: SchedulerRegistry;

  const mockAppConfigService = {
    cron: jest.fn(),
  };

  const mockSchedulerRegistry = {
    addCronJob: jest.fn(),
  };

  beforeEach(async () => {
    jest.spyOn(CronJob.prototype, 'start').mockImplementation(() => {});

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CronService,
        { provide: AppConfigService, useValue: mockAppConfigService },
        { provide: SchedulerRegistry, useValue: mockSchedulerRegistry },
        { provide: PayoutProcessorService, useValue: { execute: jest.fn() } },
        {
          provide: ProgramTokenService,
          useValue: {
            refreshTokenToBeExpired: jest.fn(),
            populateFundingAccounts: jest.fn(),
          },
        },
        { provide: AutoDebitProcessorService, useValue: { execute: jest.fn() } },
      ],
    }).compile();

    service = module.get(CronService);
    payoutProcessorService = module.get(PayoutProcessorService);
    programTokenService = module.get(ProgramTokenService);
    autoDebitProcessorService = module.get(AutoDebitProcessorService);
    schedulerRegistry = module.get(SchedulerRegistry);

    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('run()', () => {
    it('should run all enabled cron jobs', async () => {
      mockAppConfigService.cron.mockReturnValue({
        fetchPendingPayoutsCronConfig: () => ({
          cronName: 'payout',
          cronPattern: '* * * * *',
          cronTimeZone: 'UTC',
          cronOptions: { enabled: true },
        }),
        fetchPendingPayoutsCronConfigForPayoneerGBT: () => ({
          cronName: 'payoutGBT',
          cronPattern: '* * * * *',
          cronTimeZone: 'UTC',
          cronOptions: { enabled: true },
        }),
        refreshTokenCronConfig: () => ({
          cronName: 'refresh',
          cronPattern: '* * * * *',
          cronTimeZone: 'UTC',
          cronOptions: { enabled: true },
        }),
        populateFundingAccounts: () => ({
          cronName: 'populateFunding',
          cronPattern: '* * * * *',
          cronTimeZone: 'UTC',
          cronOptions: { enabled: true },
        }),
        autoDebitProcessor: () => ({
          cronName: 'autoDebit',
          cronPattern: '* * * * *',
          cronTimeZone: 'UTC',
          cronOptions: { enabled: true },
        }),
      });

      const addCronJobSpy = jest.spyOn(schedulerRegistry, 'addCronJob');

      await service.run();

      expect(addCronJobSpy).toHaveBeenCalledTimes(5);
    });

    it('should skip disabled cron jobs', async () => {
      const disabledConfig = {
        cronName: 'disabledJob',
        cronPattern: '* * * * *',
        cronTimeZone: 'UTC',
        cronOptions: { enabled: false },
      };

      mockAppConfigService.cron.mockReturnValue({
        fetchPendingPayoutsCronConfig: () => disabledConfig,
        fetchPendingPayoutsCronConfigForPayoneerGBT: () => disabledConfig,
        refreshTokenCronConfig: () => disabledConfig,
        populateFundingAccounts: () => disabledConfig,
        autoDebitProcessor: () => disabledConfig,
      });

      const addCronJobSpy = jest.spyOn(schedulerRegistry, 'addCronJob');

      await service.run();

      expect(addCronJobSpy).not.toHaveBeenCalled();
    });
  });

  describe('Individual cron job behaviors', () => {
    it('should execute payoutProcessorService for Payoneer provider', async () => {
      jest
        .spyOn(service as any, 'getCronJob')
        .mockImplementation((_config: any, onTick: () => Promise<void>) => ({
          start: async () => {
            await onTick();
          },
        }));

      const job = (service as any).getCronJob({}, async () => {
        await payoutProcessorService.execute(PayoneerProvider.Payoneer);
      });

      await job.start();

      expect(payoutProcessorService.execute).toHaveBeenCalledWith(PayoneerProvider.Payoneer);
    });

    it('should handle payoutProcessorService error gracefully', async () => {
      (payoutProcessorService.execute as jest.Mock).mockRejectedValue(new Error('fail'));

      jest
        .spyOn(service as any, 'getCronJob')
        .mockImplementation((_config: any, onTick: () => Promise<void>) => ({
          start: async () => {
            try {
              await onTick();
            } catch {
              // swallowed
            }
          },
        }));

      const job = (service as any).getCronJob({}, async () => {
        await payoutProcessorService.execute(PayoneerProvider.Payoneer);
      });

      await job.start();

      expect(payoutProcessorService.execute).toHaveBeenCalled();
    });

    it('should call refreshTokenToBeExpired', async () => {
      jest
        .spyOn(service as any, 'getCronJob')
        .mockImplementation((_config: any, onTick: () => Promise<void>) => ({
          start: async () => {
            await onTick();
          },
        }));

      const job = (service as any).getCronJob({}, async () => {
        await programTokenService.refreshTokenToBeExpired();
      });

      await job.start();

      expect(programTokenService.refreshTokenToBeExpired).toHaveBeenCalled();
    });

    it('should call populateFundingAccounts', async () => {
      jest
        .spyOn(service as any, 'getCronJob')
        .mockImplementation((_config: any, onTick: () => Promise<void>) => ({
          start: async () => {
            await onTick();
          },
        }));

      const job = (service as any).getCronJob({}, async () => {
        await programTokenService.populateFundingAccounts();
      });

      await job.start();

      expect(programTokenService.populateFundingAccounts).toHaveBeenCalled();
    });

    it('should call autoDebitProcessorService.execute', async () => {
      jest
        .spyOn(service as any, 'getCronJob')
        .mockImplementation((_config: any, onTick: () => Promise<void>) => ({
          start: async () => {
            await onTick();
          },
        }));

      const job = (service as any).getCronJob({}, async () => {
        await autoDebitProcessorService.execute();
      });

      await job.start();

      expect(autoDebitProcessorService.execute).toHaveBeenCalled();
    });
  });

  describe('onModuleInit', () => {
    it('should call run and log message after timeout', async () => {
      jest.useFakeTimers();

      const runSpy = jest.spyOn(service, 'run').mockResolvedValue(undefined);
      const loggerSpy = jest.spyOn(service['logger'], 'log').mockImplementation(() => {});

      service.onModuleInit();

      // Fast-forward the timers
      jest.advanceTimersByTime(1000);

      // Allow any pending microtasks (like `await this.run()`) to resolve
      await Promise.resolve();

      expect(runSpy).toHaveBeenCalled();
      expect(loggerSpy).toHaveBeenCalledWith({
        name: CronService.name,
        message: 'CronService started successfully',
      });

      jest.useRealTimers();
    });
  });
  describe('fetchPendingPayoutsCronJobForPayoneer', () => {
    it('should log disabled message and return noop cron if config is falsy', () => {
      mockAppConfigService.cron.mockReturnValue({
        fetchPendingPayoutsCronConfig: () => null,
      });

      const loggerSpy = jest.spyOn(service['logger'], 'log').mockImplementation(() => {});

      const cron = (service as any).fetchPendingPayoutsCronJobForPayoneer();

      expect(cron).toBeDefined();
      expect(typeof cron.start).toBe('function');

      // Call start and ensure noop does nothing
      expect(cron.start()).toBeUndefined();

      expect(loggerSpy).toHaveBeenCalledWith({
        message: 'fetch pending payouts cron job is disabled',
      });
    });

    it('should create cron job and execute payout processor', async () => {
      mockAppConfigService.cron.mockReturnValue({
        fetchPendingPayoutsCronConfig: () => ({
          cronName: 'payout',
          cronPattern: '* * * * *',
          cronTimeZone: 'UTC',
          cronOptions: { enabled: true },
        }),
      });

      jest
        .spyOn(service as any, 'getCronJob')
        .mockImplementation((_config: any, onTick: () => Promise<void>) => ({
          start: async () => {
            await onTick();
          },
        }));

      const loggerSpy = jest.spyOn(service['logger'], 'log').mockImplementation(() => {});

      const cron = (service as any).fetchPendingPayoutsCronJobForPayoneer();

      await cron.start();

      expect(service['getCronJob']).toHaveBeenCalled();
      expect(payoutProcessorService.execute).toHaveBeenCalledWith(PayoneerProvider.Payoneer);

      expect(loggerSpy).toHaveBeenCalledWith({
        message: 'fetch pending payouts cron job started',
      });
      expect(loggerSpy).toHaveBeenCalledWith({
        message: 'fetch pending payouts cron job completed',
      });
    });

    it('should log error if payoutProcessorService.execute throws', async () => {
      mockAppConfigService.cron.mockReturnValue({
        fetchPendingPayoutsCronConfig: () => ({
          cronName: 'payout',
          cronPattern: '* * * * *',
          cronTimeZone: 'UTC',
          cronOptions: { enabled: true },
        }),
      });

      (payoutProcessorService.execute as jest.Mock).mockRejectedValue(new Error('fail'));

      jest
        .spyOn(service as any, 'getCronJob')
        .mockImplementation((_config: any, onTick: () => Promise<void>) => ({
          start: async () => {
            try {
              await onTick();
            } catch {
              // swallow
            }
          },
        }));

      const loggerErrorSpy = jest.spyOn(service['logger'], 'error').mockImplementation(() => {});
      const loggerLogSpy = jest.spyOn(service['logger'], 'log').mockImplementation(() => {});

      const cron = (service as any).fetchPendingPayoutsCronJobForPayoneer();

      await cron.start();

      expect(payoutProcessorService.execute).toHaveBeenCalledWith(PayoneerProvider.Payoneer);
      expect(loggerLogSpy).toHaveBeenCalledWith({
        message: 'fetch pending payouts cron job started',
      });
      expect(loggerErrorSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'fetch pending payouts cron job failed',
        }),
      );
    });
  });
  describe('fetchPendingPayoutsCronJobForPayoneerGBT', () => {
    it('should return noop cron if config is falsy', () => {
      mockAppConfigService.cron.mockReturnValue({
        fetchPendingPayoutsCronConfigForPayoneerGBT: () => null,
      });

      const cron = (service as any).fetchPendingPayoutsCronJobForPayoneerGBT();

      expect(cron).toBeDefined();
      expect(typeof cron.start).toBe('function');
      expect(cron.start()).toBeUndefined();
    });

    it('should create cron job and execute payout processor for PayoneerGBT', async () => {
      mockAppConfigService.cron.mockReturnValue({
        fetchPendingPayoutsCronConfigForPayoneerGBT: () => ({
          cronName: 'payoutGBT',
          cronPattern: '* * * * *',
          cronTimeZone: 'UTC',
          cronOptions: { enabled: true },
        }),
      });

      jest
        .spyOn(service as any, 'getCronJob')
        .mockImplementation((_config: any, onTick: () => Promise<void>) => ({
          start: async () => {
            await onTick();
          },
        }));

      const loggerSpy = jest.spyOn(service['logger'], 'log').mockImplementation(() => {});

      const cron = (service as any).fetchPendingPayoutsCronJobForPayoneerGBT();

      await cron.start();

      expect(service['getCronJob']).toHaveBeenCalled();
      expect(payoutProcessorService.execute).toHaveBeenCalledWith(PayoneerProvider.PayoneerGBT);
      expect(loggerSpy).toHaveBeenCalledWith({
        message: 'fetch pending payouts cron job started',
      });
      expect(loggerSpy).toHaveBeenCalledWith({
        message: 'fetch pending payouts cron job completed',
      });
    });

    it('should log error if payoutProcessorService.execute throws for PayoneerGBT', async () => {
      mockAppConfigService.cron.mockReturnValue({
        fetchPendingPayoutsCronConfigForPayoneerGBT: () => ({
          cronName: 'payoutGBT',
          cronPattern: '* * * * *',
          cronTimeZone: 'UTC',
          cronOptions: { enabled: true },
        }),
      });

      (payoutProcessorService.execute as jest.Mock).mockRejectedValue(new Error('fail'));

      jest
        .spyOn(service as any, 'getCronJob')
        .mockImplementation((_config: any, onTick: () => Promise<void>) => ({
          start: async () => {
            try {
              await onTick();
            } catch {
              // swallow
            }
          },
        }));

      const loggerLogSpy = jest.spyOn(service['logger'], 'log').mockImplementation(() => {});
      const loggerErrorSpy = jest.spyOn(service['logger'], 'error').mockImplementation(() => {});

      const cron = (service as any).fetchPendingPayoutsCronJobForPayoneerGBT();

      await cron.start();

      expect(payoutProcessorService.execute).toHaveBeenCalledWith(PayoneerProvider.PayoneerGBT);
      expect(loggerLogSpy).toHaveBeenCalledWith({
        message: 'fetch pending payouts cron job started',
      });
      expect(loggerErrorSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'fetch pending payouts cron job failed',
        }),
      );
    });
  });

  describe('refreshExpiringTokensCronJob', () => {
    it('should log warn message and return noop cron if config is falsy', () => {
      mockAppConfigService.cron.mockReturnValue({
        refreshTokenCronConfig: () => null,
      });

      const loggerWarnSpy = jest.spyOn(service['logger'], 'warn').mockImplementation(() => {});

      const cron = (service as any).refreshExpiringTokensCronJob();

      expect(cron).toBeDefined();
      expect(typeof cron.start).toBe('function');

      cron.start(); // should call noop

      expect(loggerWarnSpy).toHaveBeenCalledWith({
        message: 'refresh expiring tokens cron job not configured',
      });
    });

    it('should create cron job and execute refreshTokenToBeExpired', async () => {
      mockAppConfigService.cron.mockReturnValue({
        refreshTokenCronConfig: () => ({
          cronName: 'refresh',
          cronPattern: '* * * * *',
          cronTimeZone: 'UTC',
          cronOptions: { enabled: true },
        }),
      });

      jest
        .spyOn(service as any, 'getCronJob')
        .mockImplementation((_config: any, onTick: () => Promise<void>) => ({
          start: async () => {
            await onTick();
          },
        }));

      const loggerLogSpy = jest.spyOn(service['logger'], 'log').mockImplementation(() => {});

      const cron = (service as any).refreshExpiringTokensCronJob();

      await cron.start();

      expect(service['getCronJob']).toHaveBeenCalled();
      expect(programTokenService.refreshTokenToBeExpired).toHaveBeenCalled();

      expect(loggerLogSpy).toHaveBeenCalledWith({
        message: 'refresh expiring tokens cron job started',
      });
      expect(loggerLogSpy).toHaveBeenCalledWith({
        message: 'refresh expiring tokens cron job completed',
      });
    });

    it('should log error if refreshTokenToBeExpired throws', async () => {
      mockAppConfigService.cron.mockReturnValue({
        refreshTokenCronConfig: () => ({
          cronName: 'refresh',
          cronPattern: '* * * * *',
          cronTimeZone: 'UTC',
          cronOptions: { enabled: true },
        }),
      });

      (programTokenService.refreshTokenToBeExpired as jest.Mock).mockRejectedValue(new Error('fail'));

      jest
        .spyOn(service as any, 'getCronJob')
        .mockImplementation((_config: any, onTick: () => Promise<void>) => ({
          start: async () => {
            try {
              await onTick();
            } catch {
              // swallowed for test
            }
          },
        }));

      const loggerErrorSpy = jest.spyOn(service['logger'], 'error').mockImplementation(() => {});
      const loggerLogSpy = jest.spyOn(service['logger'], 'log').mockImplementation(() => {});

      const cron = (service as any).refreshExpiringTokensCronJob();

      await cron.start();

      expect(programTokenService.refreshTokenToBeExpired).toHaveBeenCalled();

      expect(loggerLogSpy).toHaveBeenCalledWith({
        message: 'refresh expiring tokens cron job started',
      });
      expect(loggerErrorSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'refresh expiring tokens cron job failed',
        }),
      );
    });
  });

  describe('populateFundingAccountsCronJob', () => {
    it('should log warn message and return noop cron if config is falsy', () => {
      mockAppConfigService.cron.mockReturnValue({
        populateFundingAccounts: () => null,
      });

      const loggerWarnSpy = jest.spyOn(service['logger'], 'warn').mockImplementation(() => {});

      const cron = (service as any).populateFundingAccountsCronJob();

      expect(cron).toBeDefined();
      expect(typeof cron.start).toBe('function');

      cron.start(); // should call noop

      expect(loggerWarnSpy).toHaveBeenCalledWith({
        message: 'populate funding accounts cron job not configured',
      });
    });

    it('should create cron job and execute populateFundingAccounts', async () => {
      mockAppConfigService.cron.mockReturnValue({
        populateFundingAccounts: () => ({
          cronName: 'populateFunding',
          cronPattern: '* * * * *',
          cronTimeZone: 'UTC',
          cronOptions: { enabled: true },
        }),
      });

      jest
        .spyOn(service as any, 'getCronJob')
        .mockImplementation((_config: any, onTick: () => Promise<void>) => ({
          start: async () => {
            await onTick();
          },
        }));

      const loggerLogSpy = jest.spyOn(service['logger'], 'log').mockImplementation(() => {});

      const cron = (service as any).populateFundingAccountsCronJob();

      await cron.start();

      expect(service['getCronJob']).toHaveBeenCalled();
      expect(programTokenService.populateFundingAccounts).toHaveBeenCalled();

      expect(loggerLogSpy).toHaveBeenCalledWith({
        message: 'populate funding accounts cron job started',
      });
      expect(loggerLogSpy).toHaveBeenCalledWith({
        message: 'populate funding accounts cron job completed',
      });
    });

    it('should log error if populateFundingAccounts throws', async () => {
      mockAppConfigService.cron.mockReturnValue({
        populateFundingAccounts: () => ({
          cronName: 'populateFunding',
          cronPattern: '* * * * *',
          cronTimeZone: 'UTC',
          cronOptions: { enabled: true },
        }),
      });

      (programTokenService.populateFundingAccounts as jest.Mock).mockRejectedValue(new Error('fail'));

      jest
        .spyOn(service as any, 'getCronJob')
        .mockImplementation((_config: any, onTick: () => Promise<void>) => ({
          start: async () => {
            try {
              await onTick();
            } catch {
              // swallowed for test
            }
          },
        }));

      const loggerErrorSpy = jest.spyOn(service['logger'], 'error').mockImplementation(() => {});
      const loggerLogSpy = jest.spyOn(service['logger'], 'log').mockImplementation(() => {});

      const cron = (service as any).populateFundingAccountsCronJob();

      await cron.start();

      expect(programTokenService.populateFundingAccounts).toHaveBeenCalled();

      expect(loggerLogSpy).toHaveBeenCalledWith({
        message: 'populate funding accounts cron job started',
      });
      expect(loggerErrorSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'populate funding accounts cron job failed',
        }),
      );
    });
  });

  describe('autoDebitProcessorCronJob', () => {
    it('should log warn message and return noop cron if config is falsy', () => {
      mockAppConfigService.cron.mockReturnValue({
        autoDebitProcessor: () => null,
      });

      const loggerWarnSpy = jest.spyOn(service['logger'], 'warn').mockImplementation(() => {});

      const cron = (service as any).autoDebitProcessorCronJob();

      expect(cron).toBeDefined();
      expect(typeof cron.start).toBe('function');

      cron.start(); // should call noop

      expect(loggerWarnSpy).toHaveBeenCalledWith({
        message: 'auto debit process cron job not configured',
      });
    });

    it('should create cron job and execute autoDebitProcessorService', async () => {
      mockAppConfigService.cron.mockReturnValue({
        autoDebitProcessor: () => ({
          cronName: 'autoDebit',
          cronPattern: '* * * * *',
          cronTimeZone: 'UTC',
          cronOptions: { enabled: true },
        }),
      });

      jest
        .spyOn(service as any, 'getCronJob')
        .mockImplementation((_config: any, onTick: () => Promise<void>) => ({
          start: async () => {
            await onTick();
          },
        }));

      const loggerLogSpy = jest.spyOn(service['logger'], 'log').mockImplementation(() => {});

      const cron = (service as any).autoDebitProcessorCronJob();

      await cron.start();

      expect(service['getCronJob']).toHaveBeenCalled();
      expect(autoDebitProcessorService.execute).toHaveBeenCalled();

      expect(loggerLogSpy).toHaveBeenCalledWith({
        message: 'auto debit process cron job started',
      });
      expect(loggerLogSpy).toHaveBeenCalledWith({
        message: 'auto debit process cron job completed',
      });
    });

    it('should log error if autoDebitProcessorService.execute throws', async () => {
      mockAppConfigService.cron.mockReturnValue({
        autoDebitProcessor: () => ({
          cronName: 'autoDebit',
          cronPattern: '* * * * *',
          cronTimeZone: 'UTC',
          cronOptions: { enabled: true },
        }),
      });

      (autoDebitProcessorService.execute as jest.Mock).mockRejectedValue(new Error('fail'));

      jest
        .spyOn(service as any, 'getCronJob')
        .mockImplementation((_config: any, onTick: () => Promise<void>) => ({
          start: async () => {
            try {
              await onTick();
            } catch {
              // swallowed
            }
          },
        }));

      const loggerLogSpy = jest.spyOn(service['logger'], 'log').mockImplementation(() => {});
      const loggerErrorSpy = jest.spyOn(service['logger'], 'error').mockImplementation(() => {});

      const cron = (service as any).autoDebitProcessorCronJob();

      await cron.start();

      expect(autoDebitProcessorService.execute).toHaveBeenCalled();

      expect(loggerLogSpy).toHaveBeenCalledWith({
        message: 'auto debit process cron job started',
      });
      expect(loggerErrorSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'auto debit process cron job failed',
        }),
      );
    });
  });



});

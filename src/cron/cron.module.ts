import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { CronService } from './services/cron.service';
import { ConfigModule } from '../config/config.module';
import { CqrsModule } from '@nestjs/cqrs';
import { ScheduleModule } from '@nestjs/schedule';
import { PayoutModule } from '../payout/payout.module';
import { PayInModule } from '../payin/payin.module';
import { ProgramTokenService } from '../payin/service/program-token.service';
import { ProgramTokensRepository } from '../common/repositories/program-token.repository';
import { PayoneerHttpClient } from '../common/external-service/payoneer/services/payoneer.http.client.service';
import { DatabaseModule } from '../database/database.module';
import { ProgramTokensEntity } from '../common/entities/program-token.entity';
import { PayoneerAccountEntity } from '../common/entities/account.entitiy';
import { PayoneerAccountRepository } from '../common/repositories/account.repository';
import { ProgramTokenServiceHelper } from '../payin/service/helper/program-token.service.helper';
import { AutoDebitProcessorService } from 'src/payout/services/auto-debit-processor.service';
import { PaymentRequestService } from 'src/payout/services/payment-request.service';
import { PaymentRequestGrpcClient as ExternalPaymentRequestGrpcClient } from '@skuad/proto-utils/dist/payments/payment-requests/providers/payment-requests.grpc.client';
import { PaymentRequestGrpcClient } from 'src/common/external-service/payments/services/payment-request-grpc-client.service';
import { ClientGrpc } from '@nestjs/microservices';
import { createClient } from 'src/common/external-service/payments/common/create-client-grpc.option';
import * as Toolbox from 'skuad-utils-ts';
import { ForexService } from 'src/common/external-service/forex-service/service/forex-service';
import { ProgramCurrencyMappingRepository } from 'src/common/repositories/program-currency-mapping.repository';
@Module({
  imports: [
    HttpModule,
    ConfigModule,
    Toolbox.Core.CoreModule.forRoot(),
    ScheduleModule.forRoot(),
    CqrsModule,
    PayoutModule,
    PayInModule,
    DatabaseModule.forPGFeature([ProgramTokensEntity, PayoneerAccountEntity]),
  ],
  providers: [
    CronService,
    ProgramTokenService,
    ProgramTokensRepository,
    PayoneerAccountRepository,
    ProgramCurrencyMappingRepository,
    PayoneerHttpClient,
    ProgramTokenServiceHelper,
    AutoDebitProcessorService,
    PaymentRequestService,
    PaymentRequestGrpcClient,
    ExternalPaymentRequestGrpcClient,
    ForexService,
    {
      provide: 'PAYMENT_SERVICE',
      useFactory: (): ClientGrpc => {
        return createClient();
      },
    },
  ],

  exports: [CronService],
})
export class CronModule {}

import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { ConfigModule } from './config/config.module';
import { CommonModule } from './common/common.module';
import { ExternalApiClientModule } from './common/external-service/external-service.module';
import { BeneficiaryModule } from './beneficiary/beneficiary.module';
import { CronModule } from './cron/cron.module';
import { PayoutModule } from './payout/payout.module';
import { GrpcReflectionModule } from 'nestjs-grpc-reflection';
import { AppConfigService } from './config/services/app.config.service';
import { grpcServiceOptions } from './grpc.service.options';
import { DatabaseModule } from './database/database.module';
import { PayInModule } from './payin/payin.module';
import { HealthModule } from './healthcheck/healthcheck.module';
import { GraphQLModule } from '@nestjs/graphql';
import { ApolloDriverConfig, ApolloFederationDriver } from '@nestjs/apollo';
import { renderGQLError } from './app.utils';
import { ReqAuthMiddleware } from './common/middlewares/req-auth.middleware';
import * as Toolbox from 'skuad-utils-ts';

@Module({
  imports: [
    CommonModule,
    Toolbox.Core.CoreModule.forRoot(),
    DatabaseModule.forPGRootAsync(),
    ConfigModule,
    ExternalApiClientModule,
    BeneficiaryModule,
    CronModule,
    PayoutModule,
    PayInModule,
    GrpcReflectionModule.registerAsync({
      useFactory: async (configService: AppConfigService) => {
        return grpcServiceOptions(configService);
      },
      inject: [AppConfigService],
    }),
    GraphQLModule.forRoot<ApolloDriverConfig>({
      driver: ApolloFederationDriver,
      autoSchemaFile: { path: 'schema.gql', federation: 2 },
      playground: true,
      introspection: true,
      useGlobalPrefix: true,
      formatError: (e) => renderGQLError(e),
    }),
    HealthModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer): MiddlewareConsumer | void {
    consumer.apply(ReqAuthMiddleware).forRoutes({ path: '/graphql', method: RequestMethod.POST });
  }
}

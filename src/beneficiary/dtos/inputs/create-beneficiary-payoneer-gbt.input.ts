import {
  BeneficiaryRole,
  Country,
  Currency,
  EntityType,
  RoutingCode,
  SwiftChargeType,
} from '../../../common/constants/enums';
import { BaseDto } from '../../../common/dtos/base.dto';
import { ClientConfigDto } from '../../../common/dtos/client-config.dto';
import { payoneer } from '@skuad/proto-utils/dist/interface/proto/payoneer/payoneer';
import { MetaProtoDto } from '@skuad/proto-utils/dist/payments/common/types';
import * as ISO3166 from 'iso-3166-2';

export class RouteCode {
  type: RoutingCode;
  value: string;
}

export class CreateBeneficiaryInputForPayoneerGBT extends BaseDto {
  public contract_id: string;
  public client_entity_id: string;
  public entity_id: string;
  public entity_type: EntityType;
  public role: BeneficiaryRole;
  public currency: Currency;
  public bank_country: Country;
  public name_on_bank_account: string;
  public account_number: string;
  public iban: string;
  public company_name?: string;
  public first_name: string;
  public last_name: string;
  public account_type?: string;
  public country: Country;
  public city: string;
  public state_or_province: string;
  public postcode: string;
  public address: string;
  public bank_name: string;
  public bank_address: string;
  public swift_charge_type: SwiftChargeType;
  public routing_codes: RouteCode[];
  public date_of_birth: Date;

  public client_config: ClientConfigDto;
  public payeeId: string;

  public isDefaultPayoutProfile: boolean;

  public providerBeneficiaryId: string;
  public providerPayoutId: string;

  public isActive: boolean;
  public isDefault: boolean;
  public meta: MetaProtoDto = {};

  public payoutMethodDetails: payoneer.PayoutDetailsInput;

  constructor(
    createBeneficiaryForPayoneerGBTProgramInput: payoneer.CreateBeneficiaryForPayoneerGBTProgramInput,
  ) {
    super();
    this.setValues(createBeneficiaryForPayoneerGBTProgramInput);
  }

  setContactId(contactId: string): void {
    this.client_config.contact_id = contactId;
  }

  setAccountId(accountId: string): void {
    this.client_config.account_id = accountId;
  }

  setBankAccountHashId(bankAccountHashId: string): void {
    this.client_config.bank_account_hash_id = bankAccountHashId;
  }

  setPayeeId(payeeId: string): void {
    this.payeeId = payeeId;
  }

  setProviderPayoutIds(beneficiaryId: string, payoutId: string) {
    this.providerBeneficiaryId = beneficiaryId;
    this.providerPayoutId = payoutId;
  }

  setProviderActiveFlags(isActive: boolean, isDefault: boolean) {
    this.isActive = isActive;
    this.isDefault = isDefault;
  }

  setMeta(meta: MetaProtoDto) {
    this.meta = meta;
  }

  getStateCode(countryCode: Country, stateName: string): string {
    if (countryCode != Country.US) {
      return stateName;
    }

    const countryData = ISO3166?.data?.[countryCode?.toUpperCase()];

    if (stateName?.length === 2 && !!countryData?.sub) {
      let checkStateValidCode = false;

      Object.keys(countryData?.sub)?.forEach((code) => {
        if (code.includes(stateName.toUpperCase())) {
          checkStateValidCode = true;
        }
      });

      if (checkStateValidCode) {
        return stateName;
      }
    }

    if (!countryData) {
      throw new Error(`Invalid country code: ${countryCode}`);
    }
    const stateCode = Object.keys(countryData.sub).find(
      (code) => countryData.sub[code].name.toLowerCase() === stateName?.toLowerCase()?.trim(),
    );

    if (!stateCode) {
      throw new Error(`Invalid state name: ${stateName}`);
    }

    return stateCode?.split('-')?.[1] || null;
  }

  setValues(
    createBeneficiaryForPayoneerGBTProgramInput: payoneer.CreateBeneficiaryForPayoneerGBTProgramInput,
  ): void {
    this.contract_id = createBeneficiaryForPayoneerGBTProgramInput?.contractId;
    this.client_entity_id = createBeneficiaryForPayoneerGBTProgramInput?.clientEntityId;
    this.entity_id = createBeneficiaryForPayoneerGBTProgramInput?.entityId;
    this.entity_type = createBeneficiaryForPayoneerGBTProgramInput?.entityType as EntityType;
    this.role = createBeneficiaryForPayoneerGBTProgramInput?.role as BeneficiaryRole;
    this.currency = createBeneficiaryForPayoneerGBTProgramInput?.currency as Currency;
    this.bank_country = createBeneficiaryForPayoneerGBTProgramInput?.bankCountry as Country;
    this.name_on_bank_account = createBeneficiaryForPayoneerGBTProgramInput?.nameOnBankAccount;
    this.account_number = createBeneficiaryForPayoneerGBTProgramInput?.accountNumber;
    this.iban = createBeneficiaryForPayoneerGBTProgramInput?.iban;
    this.company_name = createBeneficiaryForPayoneerGBTProgramInput?.companyName;
    this.first_name = createBeneficiaryForPayoneerGBTProgramInput?.firstName;
    this.last_name = createBeneficiaryForPayoneerGBTProgramInput?.lastName;
    this.account_type = createBeneficiaryForPayoneerGBTProgramInput?.accountType;
    this.country = createBeneficiaryForPayoneerGBTProgramInput?.country as unknown as Country;
    this.city = createBeneficiaryForPayoneerGBTProgramInput?.city;

    this.state_or_province =
      this.getStateCode(this?.country, createBeneficiaryForPayoneerGBTProgramInput?.stateOrProvince) ??
      createBeneficiaryForPayoneerGBTProgramInput?.stateOrProvince;

    this.postcode = createBeneficiaryForPayoneerGBTProgramInput?.postcode;
    this.address = createBeneficiaryForPayoneerGBTProgramInput?.address;
    this.bank_name = createBeneficiaryForPayoneerGBTProgramInput?.bankName;
    this.bank_address = createBeneficiaryForPayoneerGBTProgramInput?.bankAddress;
    this.swift_charge_type = createBeneficiaryForPayoneerGBTProgramInput?.swiftChargeType as SwiftChargeType;
    this.date_of_birth = createBeneficiaryForPayoneerGBTProgramInput?.dateOfBirth
      ? new Date(createBeneficiaryForPayoneerGBTProgramInput?.dateOfBirth)
      : null;
    this.routing_codes = (createBeneficiaryForPayoneerGBTProgramInput?.routingCodes || []).map(
      (routingCode) => {
        return {
          type: routingCode.type as unknown as RoutingCode,
          value: routingCode.value,
        };
      },
    );
    this.client_config = {
      customer_id: createBeneficiaryForPayoneerGBTProgramInput?.meta?.customerId,
      contact_id: createBeneficiaryForPayoneerGBTProgramInput?.meta?.contactId,
      bank_account_hash_id: createBeneficiaryForPayoneerGBTProgramInput?.meta?.bankAccountHashId,
      account_id: createBeneficiaryForPayoneerGBTProgramInput?.meta?.accountId,
    };
    this.payoutMethodDetails = createBeneficiaryForPayoneerGBTProgramInput?.payoutDetailsInput;
  }
}

import { Field, InputType, ObjectType, registerEnumType } from '@nestjs/graphql';

export enum LegalType {
  Public = 'PUBLIC',
  Private = 'PRIVATE',
  SoleProprietorship = 'SOLE_PROPRIETORSHIP',
  LLC = 'LLC',
  LLP = 'LLP',
  LTD = 'LTD',
  INC = 'INC',
  NonProfit = 'NON_PROFIT',
  Trust = 'TRUST',
}

export enum PayeeType {
  INDIVIDUAL = 'INDIVIDUAL',
  COMPANY = 'COMPANY',
}

export enum LockType {
  None = 'NONE',
  All = 'ALL',
  Address = 'ADDRESS',
  Names = 'NAMES',
  Email = 'EMAIL',
  NameAddressEmail = 'NAME_ADDRESS_EMAIL',
  DateOfBirth = 'DATE_OF_BIRTH',
  AllNames = 'ALL_NAMES',
  AllNamesAddressEmail = 'ALL_NAMES_ADDRESS_EMAIL',
  EntityNameCountryId = 'ENTITY_NAME_COUNTRY_ID',
  Company = 'COMPANY',
  NamesCountry = 'NAMES_COUNTRY',
  AccountTypeAndCountry = 'ACCOUNT_TYPE_AND_COUNTRY',
  BankCountry = 'BANK_COUNTRY',
  AccountTypeCountry = 'ACCOUNT_TYPE_COUNTRY_',
}

export enum BeneficiaryRegistrationPurpose {
  SSO = 'SSO',
  PAYOUT = 'PAYOUT',
  PAYIN = 'PAYIN',
}

export enum AccessScope {
  Read = 'read',
  Write = 'write',
  PersonalDetails = 'personal-details	',
  OpenId = 'openid',
  ReadDocuments = 'documents:read',
}

@InputType('PayoneerPayeeCompany')
class PayeeCompany {
  @Field(() => LegalType, { nullable: true })
  legalType: LegalType;

  @Field({ nullable: true })
  name: string;

  @Field({ nullable: true })
  url: string;

  @Field({ nullable: true })
  incorporatedCountry: string;

  @Field({ nullable: true })
  incorporatedState: string;
}

@InputType('PayoneerPayeeContact')
@ObjectType('PayoneerPayeeContactOutput')
export class PayeeContact {
  @Field({ nullable: true })
  firstName: string;

  @Field({ nullable: true })
  lastName: string;

  @Field({ nullable: true })
  email: string;

  @Field({ nullable: true })
  dateOfBirth: string;

  @Field({ nullable: true })
  mobile: string;
}

@InputType('PayoneerPayeeAddress')
class PayeeAddress {
  @Field({ nullable: true })
  country: string;

  @Field({ nullable: true })
  state: string;

  @Field({ nullable: true })
  addressLine1: string;

  @Field({ nullable: true })
  addressLine2: string;

  @Field({ nullable: true })
  city: string;

  @Field({ nullable: true })
  zipCode: string;
}

@InputType('PayoneerPayee')
class Payee {
  @Field(() => PayeeType, { nullable: true })
  type: PayeeType;

  @Field(() => PayeeCompany, { nullable: true })
  company: PayeeCompany;

  @Field(() => PayeeContact, { nullable: true })
  contact: PayeeContact;

  @Field(() => PayeeAddress, { nullable: true })
  address: PayeeAddress;
}

registerEnumType(LegalType, { name: 'PayoneerPayeeLegalType' });
registerEnumType(PayeeType, { name: 'PayoneerPayeeType' });
registerEnumType(LockType, { name: 'PayoneerPayeeFormLockType' });
registerEnumType(BeneficiaryRegistrationPurpose, { name: 'PayoneerBeneficiaryRegistrationPurpose' });
registerEnumType(AccessScope, { name: 'PayoneerAccessScope' });

@InputType('PayoneerCreateRegistrationLinkInput')
export class CreateRegistrationLinkInput {
  @Field(() => String)
  payeeId: string;

  @Field(() => String, { nullable: true })
  clientSessionId?: string;

  @Field(() => String)
  redirectUrl: string;

  @Field(() => LockType, { nullable: true })
  lockType?: LockType;

  @Field(() => Payee, { nullable: true })
  payee?: Payee;

  @Field(() => BeneficiaryRegistrationPurpose)
  purpose: BeneficiaryRegistrationPurpose;

  @Field(() => String, { nullable: true })
  state: string;
}

@InputType('GetPayoneerPayeeDetailsInput')
export class GetPayeeDetailsInput {
  @Field(() => String)
  payeeId: string;

  @Field(() => BeneficiaryRegistrationPurpose, { nullable: true })
  purpose: BeneficiaryRegistrationPurpose;

  @Field(() => String, { nullable: true })
  programId: string;
}

import { InputType } from '@nestjs/graphql';
import { IsDefined, IsNumber, IsString } from 'class-validator';
import { BaseDto } from '../../../common/dtos/base.dto';
import { payoneer } from '@skuad/proto-utils/dist/interface/proto/payoneer/payoneer';

export class CreateBeneficiaryInput extends BaseDto implements payoneer.CreateBeneficiaryInput {
  constructor(input: payoneer.CreateBeneficiaryInput) {
    super();
    this.setValuesUsingGrpcInput(input);
  }

  @IsDefined()
  @IsString()
  payeeId: string;

  @IsDefined()
  @IsString()
  clientSessionId: string;

  @IsDefined()
  @IsString()
  lockType: string;

  @IsDefined()
  @IsString()
  redirectUrl: string;

  @IsDefined()
  @IsNumber()
  redirectTime: number;

  @IsDefined()
  @IsString()
  languageId: string;

  @IsDefined()
  payee: payoneer.CreateBeneficiaryInput.Payee;

  @IsDefined()
  payoutMethod: payoneer.CreateBeneficiaryInput.PayoutMethod;

  payeeAccountExists?: boolean;

  getBeneficiaryRequest(): Record<string, any> {
    const output = {
      payee_id: this.payeeId,
      client_session_id: this.clientSessionId,
      redirect_url: this.redirectUrl,
      redirect_time: this.redirectTime,
      lock_type: this.lockType,
      language_id: this.languageId,
      payee: this.payee
        ? {
            type: this.payee.type,
            contact: this.payee.contact
              ? {
                  first_name: this.payee.contact?.firstName,
                  last_name: this.payee.contact?.lastName,
                  email: this.payee.contact?.email,
                  date_of_birth: this.payee.contact?.dateOfBirth,
                  mobile: this.payee.contact?.mobile,
                }
              : undefined,
            address: this.payee.address
              ? {
                  address_line_1: this.payee.address?.addressLine_1,
                  address_line_2: this.payee.address?.addressLine_2,
                  city: this.payee.address?.city,
                  zip_code: this.payee.address?.zipCode,
                  country: this.payee.address?.country,
                  state: this.payee.address?.state,
                }
              : undefined,
          }
        : undefined,
      payout_method: this.payoutMethod
        ? {
            type: this.payoutMethod.type,
            bank_account_type: this.payoutMethod.bankAccountType,
            country: this.payoutMethod.country,
            currency: this.payoutMethod.currency,
            bank_field_details: this.payoutMethod.bankFieldDetails,
          }
        : undefined,
      already_have_an_account: this.payeeAccountExists,
    };
    return JSON.parse(JSON.stringify(output));
  }

  getEmail(): string {
    return this?.payee?.contact?.email;
  }

  setAccountExistsFlag(exists: boolean) {
    this.payeeAccountExists = exists;
  }

  private setValuesUsingGrpcInput(createBeneficiaryGrpcInput: payoneer.CreateBeneficiaryInput) {
    this.clientSessionId = createBeneficiaryGrpcInput.clientSessionId;
    this.lockType = createBeneficiaryGrpcInput.lockType;
    this.redirectUrl = createBeneficiaryGrpcInput.redirectUrl;
    this.redirectTime = createBeneficiaryGrpcInput.redirectTime;
    this.languageId = createBeneficiaryGrpcInput.languageId;
    this.payee = createBeneficiaryGrpcInput.payee;
    this.payoutMethod = createBeneficiaryGrpcInput.payoutMethod;
    this.payeeId = createBeneficiaryGrpcInput.payeeId;
    this.payeeAccountExists = createBeneficiaryGrpcInput.payeeAccountExists;
    this.validate();
  }
}

import { BeneficiaryService } from './beneficiary.service';
import { ProgramService } from '../../programs/services/program.service';
import { PayoneerHttpClient } from '../../common/external-service/payoneer/services/payoneer.http.client.service';
import { AppConfigService } from '../../config/services/app.config.service';
import { BeneficiaryGrpcClient } from '../../common/external-service/payments/services/beneficiary-grpc-client.service';
import { ContractService } from '../../common/external-service/contract-service/services/contract-service-gql.service';
import { ProgramTokenService } from 'src/payin/service/program-token.service';
import { PayoneerProvider } from '../../common/constants/enums';
import { PayoneerBeneficiaryService } from './payoneer-beneficiary.service';
import { PayoneerGbtBeneficiaryService } from './payoneer-gbt-beneficiary.service';
import { InternalServerErrorException, BadRequestException } from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';
import {
  BeneficiaryRegistrationPurpose,
  CreateRegistrationLinkInput,
  LegalType,
  LockType,
  PayeeType,
} from '../dtos/inputs/beneficiary.graphql.input';

jest.mock('./payoneer-beneficiary.service');
jest.mock('./payoneer-gbt-beneficiary.service');

describe('BeneficiaryService', () => {
  let service: BeneficiaryService;
  let programService: jest.Mocked<ProgramService>;
  let payoneerClient: jest.Mocked<PayoneerHttpClient>;
  let config: jest.Mocked<AppConfigService>;
  let beneficiaryRpcClient: jest.Mocked<BeneficiaryGrpcClient>;
  let contractService: jest.Mocked<ContractService>;
  let programTokenService: jest.Mocked<ProgramTokenService>;

  beforeEach(() => {
    programService = {
      /* add methods if needed */
    } as any;
    payoneerClient = {
      getPayeeExtendedDetails: jest.fn(),
    } as any;
    config = {
      getSSOProgramId: jest.fn().mockReturnValue('mock-program-id'),
    } as any;
    beneficiaryRpcClient = {} as any;
    contractService = {} as any;
    programTokenService = {
      createAuthorizationLink: jest.fn(),
    } as any;

    service = new BeneficiaryService(
      programService,
      payoneerClient,
      config,
      beneficiaryRpcClient,
      contractService,
      programTokenService,
    );
  });

  it('should throw error for unidentified provider', () => {
    expect(() =>
      // @ts-expect-error forcing invalid provider
      service['initializeProviders']('UNKNOWN_PROVIDER'),
    ).toThrow('Unidentified provider!!!');
  });

  it('should log and throw RpcException when getBankFieldsForPayeeForPayoneerGBT fails', async () => {
    const mockGetPayeeBankAccountDetailsFormat = jest.fn().mockRejectedValue(new Error('fetch failed'));

    (PayoneerGbtBeneficiaryService as jest.Mock).mockImplementation(() => ({
      getPayeeBankAccountDetailsFormat: mockGetPayeeBankAccountDetailsFormat,
    }));

    await expect(service.getBankFieldsForPayeeForPayoneerGBT({} as any)).rejects.toThrow(RpcException);
  });

  it('should create registration link V4', async () => {
    const mockCreateBeneficiary = jest.fn().mockResolvedValue('mock-result');
    (PayoneerBeneficiaryService as jest.Mock).mockImplementation(() => ({
      createBeneficiary: mockCreateBeneficiary,
    }));

    const result = await service.createRegistrationLinkV4({} as any);
    expect(result).toBe('mock-result');
    expect(mockCreateBeneficiary).toHaveBeenCalled();
  });

  it('should handle error in createRegistrationLinkV4', async () => {
    const mockCreateBeneficiary = jest.fn().mockRejectedValue(new Error('fail'));
    (PayoneerBeneficiaryService as jest.Mock).mockImplementation(() => ({
      createBeneficiary: mockCreateBeneficiary,
    }));

    await expect(service.createRegistrationLinkV4({} as any)).rejects.toThrow(RpcException);
  });

  it('should get bank fields for payee', async () => {
    const mockGetBankAccountDetailsFormat = jest.fn().mockResolvedValue('mock-output');
    (PayoneerBeneficiaryService as jest.Mock).mockImplementation(() => ({
      getPayeeBankAccountDetailsFormat: mockGetBankAccountDetailsFormat,
    }));

    const result = await service.getBankFieldsForPayee({} as any);
    expect(result).toBe('mock-output');
  });

  it('should create beneficiary for PayoneerGBT program', async () => {
    const mockCreateBeneficiary = jest.fn().mockResolvedValue({ beneficiaryId: '123' });
    (PayoneerGbtBeneficiaryService as jest.Mock).mockImplementation(() => ({
      createBeneficiary: mockCreateBeneficiary,
    }));

    const [res, err] = await service.createBeneficiaryForPayoneerGBTProgram({} as any);
    expect(res.beneficiaryId).toBe('123');
    expect(err).toBeNull();
  });

  it('should return RpcException on createBeneficiaryForPayoneerGBTProgram error', async () => {
    const mockCreateBeneficiary = jest.fn().mockRejectedValue(new Error('fail'));
    (PayoneerGbtBeneficiaryService as jest.Mock).mockImplementation(() => ({
      createBeneficiary: mockCreateBeneficiary,
    }));

    const [res, err] = await service.createBeneficiaryForPayoneerGBTProgram({} as any);
    expect(res).toBeNull();
    expect(err).toBeInstanceOf(RpcException);
  });

  it('should get payee details successfully', async () => {
    payoneerClient.getPayeeExtendedDetails.mockResolvedValue([
      {
        result: {
          accountId: '123',
          type: 'individual',
          company: {
            incorporated_address_1: '123 Main St',
            incorporated_address_2: '',
            incorporated_city: 'City',
            incorporated_state: 'State',
            incorporated_zipcode: '12345',
          },
          contact: {
            first_name: 'John',
            last_name: 'Doe',
            email: '<EMAIL>',
            phone: '**********',
            mobile: '**********',
            mobile_country_code: 'US',
          },
          address: {
            address_line_1: '456 Street',
            address_line_2: '',
            city: 'City',
            state: 'State',
            zip_code: '67890',
            country: 'US',
          },
          payout_method: {
            type: 'bank',
            bank_account_type: 'checking',
            country: 'US',
            currency: 'USD',
            bank_field_details: [],
          },
        },
      },
      null,
    ]);

    const result = await service.getPayeeDetails({
      payeeId: '123',
      purpose: null,
      programId: 'pid',
    } as any);

    expect(result.accountId).toBe('123');
  });

  it('should throw BadRequestException when programId is missing', async () => {
    await expect(
      service.getPayeeDetails({
        payeeId: '123',
        purpose: null,
      } as any),
    ).rejects.toThrow(BadRequestException);
  });

  it('should throw InternalServerErrorException on getPayeeDetails error', async () => {
    payoneerClient.getPayeeExtendedDetails.mockResolvedValue([
      null,
      {
        errors: { error_description: 'error' },
        error: '',
      },
    ]);

    await expect(
      service.getPayeeDetails({
        payeeId: '123',
        purpose: null,
        programId: 'pid',
      } as any),
    ).rejects.toThrow(InternalServerErrorException);
  });

  it('should return bank account details for PayoneerGBT', async () => {
    const mockGetBankAccountDetailsForPayee = jest
      .fn()
      .mockResolvedValue([{ name: 'Bank Name', value: 'ABC Bank' }]);

    (PayoneerGbtBeneficiaryService as jest.Mock).mockImplementation(() => ({
      getBankAccountDetailsForPayee: mockGetBankAccountDetailsForPayee,
    }));

    const result = await service.getPayeeBankAccountDetails('payee-123');

    expect(result).toEqual([{ name: 'Bank Name', value: 'ABC Bank' }]);
    expect(mockGetBankAccountDetailsForPayee).toHaveBeenCalledWith('payee-123');
  });

  it('should log and throw RpcException if getBankAccountDetailsForPayee throws', async () => {
    const error = new Error('Some failure');
    const loggerSpy = jest.spyOn(service['logger'], 'error');

    const mockGetBankAccountDetailsForPayee = jest.fn().mockRejectedValue(error);

    (PayoneerGbtBeneficiaryService as jest.Mock).mockImplementation(() => ({
      getBankAccountDetailsForPayee: mockGetBankAccountDetailsForPayee,
    }));

    await expect(service.getPayeeBankAccountDetails('payee-123')).rejects.toThrow(RpcException);

    expect(loggerSpy).toHaveBeenCalledWith(
      JSON.stringify({
        message: 'Error fetching bank account details',
        error: error.message,
        stack: error.stack,
      }),
    );
  });

  describe('createBeneficiaryRegistrationLink', () => {
    const input: CreateRegistrationLinkInput = {
      payeeId: 'payee-1',
      redirectUrl: 'https://redirect.url',
      purpose: BeneficiaryRegistrationPurpose.PAYIN, // or SSO
      state: 'test-state', // ✅ Required field
      payee: {
        type: PayeeType.INDIVIDUAL,
        contact: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          mobile: '**********',
          dateOfBirth: '2000-01-01',
        },
        address: {
          country: 'US',
          state: 'NY',
          addressLine1: '123 Main St',
          addressLine2: '',
          city: 'NYC',
          zipCode: '10001',
        },
        company: {
          legalType: LegalType.LLC,
          name: 'Example Co',
          url: 'https://example.com',
          incorporatedCountry: 'US',
          incorporatedState: 'NY',
        },
      },
    };
    it('should populate payee details if input.payee is provided', async () => {
      const mockCreateBeneficiary = jest.fn().mockResolvedValue('mock-output');
      (PayoneerBeneficiaryService as jest.Mock).mockImplementation(() => ({
        createBeneficiary: mockCreateBeneficiary,
      }));

      const result = await service.createBeneficiaryRegistrationLink(input);
      expect(mockCreateBeneficiary).toHaveBeenCalledWith(
        expect.objectContaining({
          payeeId: 'payee-1',
          redirectUrl: 'https://redirect.url',
          payee: expect.any(Object),
        }),
        undefined,
      );
      expect(result).toBe('mock-output');
    });

    it('should override redirectUrl and programId for SSO purpose', async () => {
      const mockCreateBeneficiary = jest.fn().mockResolvedValue('mock-output');
      programTokenService.createAuthorizationLink.mockReturnValue('https://auth-link');
      config.getSSOProgramId.mockReturnValue('sso-prog-id');

      (PayoneerBeneficiaryService as jest.Mock).mockImplementation(() => ({
        createBeneficiary: mockCreateBeneficiary,
      }));

      const input = {
        payeeId: 'payee-2',
        redirectUrl: 'https://original.url',
        purpose: BeneficiaryRegistrationPurpose.SSO,
        state: 'xyz',
      };

      const result = await service.createBeneficiaryRegistrationLink(input);

      expect(programTokenService.createAuthorizationLink).toHaveBeenCalledWith(
        input.redirectUrl,
        input.purpose,
        input.state,
      );
      expect(config.getSSOProgramId).toHaveBeenCalled();
      expect(mockCreateBeneficiary).toHaveBeenCalledWith(
        expect.objectContaining({
          redirectUrl: 'https://auth-link',
        }),
        'sso-prog-id',
      );
      expect(result).toBe('mock-output');
    });

    it('should use default values if optional fields are missing', async () => {
      const mockCreateBeneficiary = jest.fn().mockResolvedValue('output-basic');
      (PayoneerBeneficiaryService as jest.Mock).mockImplementation(() => ({
        createBeneficiary: mockCreateBeneficiary,
      }));

      const minimalInput: CreateRegistrationLinkInput = {
        payeeId: 'payee-basic',
        redirectUrl: 'https://basic.url',
        purpose: BeneficiaryRegistrationPurpose.PAYIN,
        state: 'test-state',
      };

      const result = await service.createBeneficiaryRegistrationLink(minimalInput);

      expect(mockCreateBeneficiary).toHaveBeenCalledWith(
        expect.objectContaining({
          payeeId: 'payee-basic',
          redirectUrl: 'https://basic.url',
          lockType: LockType.None,
          clientSessionId: expect.any(String),
        }),
        undefined,
      );
      expect(result).toBe('output-basic');
    });
  });
});

import { payoneer } from '@skuad/proto-utils/dist/interface/proto/payoneer/payoneer';
import { RegisterPayeeFormatOutput } from '../../../common/data-types/payoneer-v4.data.types';

export abstract class AbstractBeneficiaryService {
  abstract createBeneficiary(
    createBeneficiaryGrpcInput:
      | payoneer.CreateBeneficiaryInput
      | payoneer.CreateBeneficiaryForPayoneerGBTProgramInput,
    programId?: string,
  ): Promise<payoneer.CreateBeneficiaryOutput | payoneer.CreateBeneficiaryForPayoneerGBTProgramOutput>;

  abstract getBankAccountDetailsForPayee(payeeId: string): Promise<
    Array<{
      name: string;
      value: string;
    }>
  >;

  abstract getPayeeBankAccountDetailsFormat(
    input: payoneer.GetBankFieldsForPayeeInput,
  ): Promise<payoneer.GetBankFieldsForPayeeOutput | RegisterPayeeFormatOutput>;
}

import { Test, TestingModule } from '@nestjs/testing';
import { PayoneerGbtBeneficiaryService } from './payoneer-gbt-beneficiary.service';
import { ProgramService } from '../../programs/services/program.service';
import { PayoneerHttpClient } from '../../common/external-service/payoneer/services/payoneer.http.client.service';
import { BeneficiaryGrpcClient } from '../../common/external-service/payments/services/beneficiary-grpc-client.service';
import { NotFoundException } from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';
import { payoneer } from '@skuad/proto-utils/dist/interface/proto/payoneer/payoneer';
import { ClientErrorV2, RegisterPayeeFormatOutput } from '../../common/data-types/payoneer-v4.data.types';
import { ContractService } from '../../common/external-service/contract-service/services/contract-service-gql.service';
import { PayoneerProvider } from '../../common/constants/enums';

// Mock the handler classes at the top
jest.mock('./handlers/create-beneficiary-payoneer-gbt-handler.service', () => {
  return {
    CreateBeneficiaryForPayoneerGBTHandler: jest.fn().mockImplementation(() => {
      return {
        initialize: jest.fn(),
        sync: jest.fn(),
      };
    }),
  };
});

jest.mock('./handlers/get-bank-account-details-for-payee-payoneer-gbt.handler', () => {
  return {
    GetBankAccountDetailsForPayeePayoneerGBT: jest.fn().mockImplementation(() => {
      return {
        initialize: jest.fn(),
        getBankAccountDetails: jest.fn(),
      };
    }),
  };
});

// We still import the classes to let us inspect the mock
import { CreateBeneficiaryForPayoneerGBTHandler } from './handlers/create-beneficiary-payoneer-gbt-handler.service';
import { GetBankAccountDetailsForPayeePayoneerGBT } from './handlers/get-bank-account-details-for-payee-payoneer-gbt.handler';

describe('PayoneerGbtBeneficiaryService', () => {
  let service: PayoneerGbtBeneficiaryService;
  let programService: ProgramService;
  let payoneerClient: PayoneerHttpClient;
  let beneficiaryRpcClient: BeneficiaryGrpcClient;
  let contractService: ContractService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PayoneerGbtBeneficiaryService,
        {
          provide: ProgramService,
          useValue: {
            getPayoneerGbtProgramId: jest.fn().mockResolvedValue('program-id'),
            getProgramIdFromGrpcInput: jest.fn().mockResolvedValue('program-id'),
          },
        },
        {
          provide: PayoneerHttpClient,
          useValue: {
            getPayeeExtendedDetails: jest.fn(),
            getRegisterPayeeFormat: jest.fn(),
          },
        },
        {
          provide: BeneficiaryGrpcClient,
          useValue: {
            createOne: jest.fn().mockResolvedValue({ data: { id: 'mock-beneficiary-id' } }),
            updateOne: jest.fn().mockResolvedValue({ data: { success: true } }),
          },
        },
        {
          provide: ContractService,
          useValue: {
            getConfigByKey: jest.fn().mockResolvedValue({ key: 'mockConfig' }),
          },
        },
      ],
    }).compile();

    service = module.get<PayoneerGbtBeneficiaryService>(PayoneerGbtBeneficiaryService);
    programService = module.get<ProgramService>(ProgramService);
    payoneerClient = module.get<PayoneerHttpClient>(PayoneerHttpClient);
    beneficiaryRpcClient = module.get<BeneficiaryGrpcClient>(BeneficiaryGrpcClient);
    contractService = module.get<ContractService>(ContractService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  // Basic input validation checks
  it('should throw RpcException when input is null', async () => {
    await expect(service.getPayeeBankAccountDetailsFormat(null)).rejects.toThrow(RpcException);
  });

  it('should throw RpcException when required fields are missing', async () => {
    const incompleteInput = {
      payeeType: 'individual',
      // missing country and currency
    } as any;

    await expect(service.getPayeeBankAccountDetailsFormat(incompleteInput)).rejects.toThrow(RpcException);
  });

  it('should throw RpcException when program service fails', async () => {
    const input = {
      payeeType: 'individual',
      country: 'US',
      currency: 'USD',
    } as any;

    (programService.getProgramIdFromGrpcInput as jest.Mock).mockRejectedValue(new Error('Program error'));

    await expect(service.getPayeeBankAccountDetailsFormat(input)).rejects.toThrow(RpcException);
  });

  describe('createBeneficiary', () => {
    it('should create a beneficiary successfully', async () => {
      // Force the code path that calls new CreateBeneficiaryForPayoneerGBTHandler
      // The first time that line is run, Jest will create a new mock instance.
      // Then we can retrieve it from mock.instances:
      const mockHandlerInstance = {
        initialize: jest.fn().mockResolvedValue(undefined),
        sync: jest.fn().mockResolvedValue({ success: true }),
      };
      // Each time "new CreateBeneficiaryForPayoneerGBTHandler" is called,
      // we want it to return `mockHandlerInstance`
      (CreateBeneficiaryForPayoneerGBTHandler as jest.Mock).mockReturnValueOnce(mockHandlerInstance);

      const createBeneficiaryGrpcInput: payoneer.CreateBeneficiaryForPayoneerGBTProgramInput = {} as any;

      const result = await service.createBeneficiary(createBeneficiaryGrpcInput);

      expect(result).toEqual({ success: true });
      // Make sure we called initialize + sync
      expect(mockHandlerInstance.initialize).toHaveBeenCalledWith(createBeneficiaryGrpcInput);
      expect(mockHandlerInstance.sync).toHaveBeenCalled();
    });

    it('should log error and throw error if createBeneficiary fails', async () => {
      const error = new Error('Test error');
      const mockHandlerInstance = {
        initialize: jest.fn().mockRejectedValue(error),
        sync: jest.fn(),
      };
      (CreateBeneficiaryForPayoneerGBTHandler as jest.Mock).mockReturnValueOnce(mockHandlerInstance);

      await expect(service.createBeneficiary({} as any)).rejects.toThrow(error);
    });
  });

  describe('getBankAccountDetailsForPayee', () => {
    it('should return bank account details for payee', async () => {
      // We'll mock the newly created instance for the get-bank-account handler:
      const mockHandlerInstance = {
        initialize: jest.fn().mockResolvedValue(undefined),
        getBankAccountDetails: jest.fn().mockResolvedValue([{ name: 'Account', value: '1234' }]),
      };
      (GetBankAccountDetailsForPayeePayoneerGBT as jest.Mock).mockReturnValueOnce(mockHandlerInstance);

      const result = await service.getBankAccountDetailsForPayee('some-payee-id');
      expect(mockHandlerInstance.initialize).toHaveBeenCalledWith('some-payee-id');
      expect(mockHandlerInstance.getBankAccountDetails).toHaveBeenCalled();
      expect(result).toEqual([{ name: 'Account', value: '1234' }]);
    });

    it('should return empty array if handler returns undefined', async () => {
      const mockHandlerInstance = {
        initialize: jest.fn().mockResolvedValue(undefined),
        getBankAccountDetails: jest.fn().mockResolvedValue(undefined),
      };
      (GetBankAccountDetailsForPayeePayoneerGBT as jest.Mock).mockReturnValueOnce(mockHandlerInstance);

      const result = await service.getBankAccountDetailsForPayee('some-id');
      expect(result).toEqual([]);
    });

    it('should throw NotFoundException if payee not found', async () => {
      const mockHandlerInstance = {
        initialize: jest.fn().mockRejectedValue(new NotFoundException('Payee not found')),
        getBankAccountDetails: jest.fn(),
      };
      (GetBankAccountDetailsForPayeePayoneerGBT as jest.Mock).mockReturnValueOnce(mockHandlerInstance);

      await expect(service.getBankAccountDetailsForPayee('fail-id')).rejects.toThrow(NotFoundException);
    });
  });

  describe('getPayeeBankAccountDetailsFormat', () => {
    it('should return payee bank account details format', async () => {
      (payoneerClient.getRegisterPayeeFormat as jest.Mock).mockResolvedValue([{ some: 'format' }, null]);

      const input: payoneer.GetBankFieldsForPayeeInput = {
        payeeType: 'type',
        country: 'country',
        currency: 'currency',
      } as any;

      const result = await service.getPayeeBankAccountDetailsFormat(input);
      expect(result).toEqual({ some: 'format' });
    });

    it('should log error and throw RpcException if getPayeeBankAccountDetailsFormat fails', async () => {
      (payoneerClient.getRegisterPayeeFormat as jest.Mock).mockResolvedValue([
        null,
        { error: 'Test error' } as ClientErrorV2,
      ]);

      await expect(
        service.getPayeeBankAccountDetailsFormat({
          payeeType: 'individual',
          country: 'US',
          currency: 'USD',
        } as any),
      ).rejects.toThrow(RpcException);
    });
  });

  describe('error handling', () => {
    it('should log error details when payoneer client fails', async () => {
      const loggerSpy = jest.spyOn(service['logger'], 'error');
      (payoneerClient.getRegisterPayeeFormat as jest.Mock).mockRejectedValue(new Error('Payoneer API error'));

      await expect(
        service.getPayeeBankAccountDetailsFormat({
          payeeType: 'individual',
          country: 'US',
          currency: 'USD',
        } as any),
      ).rejects.toThrow(RpcException);
      expect(loggerSpy).toHaveBeenCalled();
    });

    it('should return empty array when no bank details found', async () => {
      (payoneerClient.getPayeeExtendedDetails as jest.Mock).mockResolvedValue([
        { result: { payout_method: {} } },
        null,
      ]);

      // Now we mock the bank handler instance to return an empty array
      const mockHandlerInstance = {
        initialize: jest.fn().mockResolvedValue(undefined),
        getBankAccountDetails: jest.fn().mockResolvedValue([]),
      };
      (GetBankAccountDetailsForPayeePayoneerGBT as jest.Mock).mockReturnValueOnce(mockHandlerInstance);

      const result = await service.getBankAccountDetailsForPayee('test-id');
      expect(result).toEqual([]);
    });
  });

  describe('extended error scenarios', () => {
    it('should handle null response from getPayeeExtendedDetails', async () => {
      (payoneerClient.getPayeeExtendedDetails as jest.Mock).mockResolvedValue([null, null]);

      const mockHandlerInstance = {
        initialize: jest.fn().mockResolvedValue(undefined),
        getBankAccountDetails: jest.fn().mockResolvedValue(undefined),
      };
      (GetBankAccountDetailsForPayeePayoneerGBT as jest.Mock).mockReturnValueOnce(mockHandlerInstance);

      const result = await service.getBankAccountDetailsForPayee('test-id');
      expect(result).toEqual([]);
    });

    it('should handle undefined bank_field_details', async () => {
      (payoneerClient.getPayeeExtendedDetails as jest.Mock).mockResolvedValue([
        { result: { payout_method: { bank_field_details: undefined } } },
        null,
      ]);

      const mockHandlerInstance = {
        initialize: jest.fn().mockResolvedValue(undefined),
        getBankAccountDetails: jest.fn().mockResolvedValue(undefined),
      };
      (GetBankAccountDetailsForPayeePayoneerGBT as jest.Mock).mockReturnValueOnce(mockHandlerInstance);

      const result = await service.getBankAccountDetailsForPayee('test-id');
      expect(result).toEqual([]);
    });

    it('should handle missing program id gracefully', async () => {
      (programService.getProgramIdFromGrpcInput as jest.Mock).mockResolvedValue(null);

      await expect(
        service.getPayeeBankAccountDetailsFormat({
          payeeType: 'individual',
          country: 'US',
          currency: 'USD',
        } as any),
      ).rejects.toThrow(RpcException);
    });

    it('should handle empty response from payoneer client', async () => {
      (programService.getProgramIdFromGrpcInput as jest.Mock).mockResolvedValue('program-id');
      (payoneerClient.getRegisterPayeeFormat as jest.Mock).mockResolvedValue([{} as any, null]);

      const result = await service.getPayeeBankAccountDetailsFormat({
        payeeType: 'individual',
        country: 'US',
        currency: 'USD',
      } as any);
      expect(result).toEqual({});
    });

    it('should handle network errors from payoneer client', async () => {
      (programService.getProgramIdFromGrpcInput as jest.Mock).mockResolvedValue('program-id');
      (payoneerClient.getRegisterPayeeFormat as jest.Mock).mockRejectedValue(new Error('Network error'));

      await expect(
        service.getPayeeBankAccountDetailsFormat({
          payeeType: 'individual',
          country: 'US',
          currency: 'USD',
        } as any),
      ).rejects.toThrow(RpcException);
    });
  });
});

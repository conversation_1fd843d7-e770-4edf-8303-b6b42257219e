import { Logger } from '@nestjs/common';
import {
  Country,
  Currency,
  PayeeType,
  PayoneerBankFieldAccountTypeOptionsReverseMapping,
  PayoneerBankFieldNames,
  PayoneerProvider,
} from '../../../common/constants/enums';
import { ProgramService } from '../../../programs/services/program.service';
import { PayoneerHttpClient } from '../../../common/external-service/payoneer/services/payoneer.http.client.service';
import { CustomError, ErrorCodes, ErrorTypes } from '../../../common/error/custom.error';
import { Messages } from '../../../common/constants/messages';
import { getError } from '../../../common/helpers/utils';
import { ServiceMethods } from '../beneficiary.service';
import {
  GetPayeeExtendedDetailsResponse,
  RegisterPayeeFormatOutput,
} from '../../../common/data-types/payoneer-v4.data.types';
import { ContractService } from '../../../common/external-service/contract-service/services/contract-service-gql.service';
import { RpcException } from '@nestjs/microservices';
import { PayoneerGBTMapper } from '../../../beneficiary/helpers/beneficiary-mapping.handler';

export class GetBankAccountDetailsForPayeePayoneerGBT {
  private readonly logger = new Logger(GetBankAccountDetailsForPayeePayoneerGBT.name);
  readonly service = GetBankAccountDetailsForPayeePayoneerGBT.name;

  private payeeId: string;
  private programId: string;
  private payeeDetails: GetPayeeExtendedDetailsResponse;
  private country: Country;
  private currency: Currency;
  private bankConfig: Record<string, any>;
  private payeeBankAccountDetailsFormat: RegisterPayeeFormatOutput;

  constructor(
    private readonly programService: ProgramService,
    private readonly payoneerClient: PayoneerHttpClient,
    private readonly contractService: ContractService,
  ) {}

  setPayeeId(payeeId: string) {
    this.payeeId = payeeId;
  }

  async setPayeeDetails(): Promise<void> {
    const [payeeInfo, payeeInfoError] = await this.payoneerClient.getPayeeExtendedDetails(
      this.programId,
      this.payeeId,
    );

    if (!payeeInfo || payeeInfoError) {
      throw new CustomError(Messages.ERROR_WHILE_FETCHING_PAYEE_DETAILS, {
        service: this.service,
        method: ServiceMethods.FetchBankAccountDetails,
        errorCode: ErrorCodes.PaymentGateWayError,
        type: ErrorTypes.ProviderClientError,
        message: getError(payeeInfoError ?? 'Payee details not found'),
        description: Messages.ERROR_WHILE_FETCHING_PAYEE_DETAILS,
      });
    }

    this.payeeDetails = payeeInfo;
    this.country = this.payeeDetails.result?.payout_method?.country as Country;
    this.currency = this.payeeDetails.result?.payout_method?.currency as Currency;
  }

  async setBankConfig(): Promise<void> {
    const payeeType = this.payeeDetails?.result?.type?.toUpperCase() ?? PayeeType.Individual;
    const key = `type:BANK_INFO::subtype:${payeeType}::country:${this.country}`;
    const type = 'BankInfo';

    const config = await this.contractService.getConfigByKey(key, type);

    if (!config) {
      throw new CustomError(Messages.ERROR_WHILE_FETCHING_BANK_CONFIG, {
        service: this.service,
        method: ServiceMethods.FetchBankAccountDetails,
        errorCode: ErrorCodes.PaymentGateWayError,
        type: ErrorTypes.ProviderClientError,
        message: 'Something went wrong',
        description: Messages.ERROR_WHILE_FETCHING_BANK_CONFIG,
      });
    }

    this.bankConfig = config;
  }

  async getAndSetRegisterPayeeInput(): Promise<void> {
    try {
      const payeeType = (this.payeeDetails?.result?.type?.toUpperCase() as PayeeType) ?? PayeeType.Individual;

      const [bankAccountDetailsFormat, error] = await this.payoneerClient.getRegisterPayeeFormat(
        this.programId,
        payeeType,
        this.country,
        this.currency,
      );

      if (error) {
        throw new CustomError(Messages.ERROR_WHILE_FETCHING_REGISTRATION_FORMAT, {
          service: this.service,
          method: ServiceMethods.UpdateProviderResponse,
          errorCode: ErrorCodes.ServiceError,
          type: ErrorTypes.ProviderClientError,
          message: error.errors,
          description: Messages.ERROR_WHILE_FETCHING_REGISTRATION_FORMAT,
        });
      }

      this.payeeBankAccountDetailsFormat = bankAccountDetailsFormat;
    } catch (error) {
      throw new CustomError(Messages.ERROR_WHILE_TRANSFORMING_BENEFICIARY_DETAILS, {
        service: this.service,
        method: ServiceMethods.UpdateProviderResponse,
        errorCode: ErrorCodes.ServiceError,
        type: ErrorTypes.GRPCClientError,
        message: error.errors,
        description: Messages.ERROR_WHILE_TRANSFORMING_BENEFICIARY_DETAILS,
      });
    }
  }

  async initialize(payeeId: string) {
    this.setPayeeId(payeeId);
    this.programId = await this.programService.getProgramIdFromGrpcInput(null, PayoneerProvider.PayoneerGBT);
    await this.setPayeeDetails();
    await this.setBankConfig();
    await this.getAndSetRegisterPayeeInput();
  }

  async transformBankAccountDetails(): Promise<
    Array<{
      name: string;
      value: string;
    }>
  > {
    const bankAccountDetails = this.payeeDetails?.result?.payout_method?.bank_field_details ?? [];

    const bankAccountDetailsFormatted: Array<{
      name: string;
      value: string;
    }> = [];

    for (const bankAccountDetail of bankAccountDetails) {
      let { name, value } = bankAccountDetail;

      name = name?.split(' ')?.join('') ?? '';

      for (const field of this.payeeBankAccountDetailsFormat?.result?.payout_method?.fields?.items || []) {
        let fieldName = field?.field_name;
        let fieldLabel = field?.label;

        if (fieldName === name || fieldLabel === name) {
          name = fieldName;
          break;
        }
      }

      if (name === PayoneerBankFieldNames.AccountType) {
        value = PayoneerBankFieldAccountTypeOptionsReverseMapping?.[this.country]?.[value] ?? value;
      }

      bankAccountDetailsFormatted.push({ name, value });
    }

    return bankAccountDetailsFormatted;
  }

  getConfigFields(): string[] {
    let bankDetailsFields = null;
    let defaultBankAccountDetailsFields = null;

    for (const [key, value] of Object.entries(this.bankConfig?.values || {})) {
      if (this.currency === key) {
        bankDetailsFields = value;
      } else if (key === 'CC') {
        defaultBankAccountDetailsFields = value;
      }
    }

    const bankFields = bankDetailsFields ?? defaultBankAccountDetailsFields ?? {};

    const keys: string[] = [];
    for (const key of Object.keys(bankFields ?? {})) {
      keys.push(key);
    }

    return keys;
  }

  async getBankAccountDetails(): Promise<
    Array<{
      name: string;
      value: string;
    }>
  > {
    try {
      // Transform the payee details to the required format
      const bankAccountDetails = await this.transformBankAccountDetails();

      // Using reverse mapping to get the source object from the target fields
      const mapping = new PayoneerGBTMapper();
      const bankAccountDetailsMapped = mapping.reverseMapFields(bankAccountDetails);

      // Use bank config fields to get the bank account details and filter out the unnecessary fields
      const configFields = this.getConfigFields();
      const bankAccountDetailsFiltered: Array<{
        name: string;
        value: string;
      }> = [];

      for (const [key, value] of Object.entries(bankAccountDetailsMapped)) {
        if (configFields.includes(key)) {
          bankAccountDetailsFiltered.push({ name: key, value });
        }
      }

      // Return the bank account details
      return bankAccountDetailsFiltered;
    } catch (error) {
      this.handleError(error);
    }
  }

  handleError(error: any): never {
    this.logger.error(
      JSON.stringify({
        message: 'Failed to fetch bank account details',
        error,
      }),
    );
    const errorMessage = error?.errorInfo?.message ?? error?.message ?? 'Something went wrong';
    throw new RpcException(errorMessage);
  }
}

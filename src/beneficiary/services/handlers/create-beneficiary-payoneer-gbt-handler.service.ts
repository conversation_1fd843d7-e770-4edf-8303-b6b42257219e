import { Logger } from '@nestjs/common';
import { payoneer } from '@skuad/proto-utils/dist/interface/proto/payoneer/payoneer';
import { ProgramService } from '../../../programs/services/program.service';
import { PayoneerHttpClient } from '../../../common/external-service/payoneer/services/payoneer.http.client.service';
import { RpcException } from '@nestjs/microservices';
import { CreateBeneficiaryForPayoneerGBTServiceHelper } from '../../helpers/create-beneficiary-for-payoneer-gbt.service.helper';
import { CreateBeneficiaryInputForPayoneerGBT } from '../../../beneficiary/dtos/inputs/create-beneficiary-payoneer-gbt.input';
import { BeneficiaryGrpcClient } from '../../../common/external-service/payments/services/beneficiary-grpc-client.service';
import { Messages } from '../../../common/constants/messages';
import { CustomError, ErrorCodes, ErrorTypes } from '../../../common/error/custom.error';
import { getBankComfigKeys, getError } from '../../../common/helpers/utils';
import { ServiceMethods } from '../beneficiary.service';
import {
  GetPayeeExtendedDetailsResponse,
  RegisterPayeeInput,
} from '../../../common/data-types/payoneer-v4.data.types';
import { Country, GrpcQueryType, PayoneerProvider } from '../../../common/constants/enums';
import { ContractService } from '../../../common/external-service/contract-service/services/contract-service-gql.service';

export class CreateBeneficiaryForPayoneerGBTHandler {
  private readonly logger = new Logger(CreateBeneficiaryForPayoneerGBTHandler.name);
  readonly service = CreateBeneficiaryForPayoneerGBTHandler.name;

  private createBeneficiaryGrpcInput: payoneer.CreateBeneficiaryForPayoneerGBTProgramInput;
  private programId: string;

  private createBeneficiaryInput: CreateBeneficiaryInputForPayoneerGBT;
  private createBeneficiaryServiceHelper: CreateBeneficiaryForPayoneerGBTServiceHelper;

  private payeeDetails: GetPayeeExtendedDetailsResponse;

  constructor(
    private readonly programService: ProgramService,
    private readonly payoneerClient: PayoneerHttpClient,
    private readonly beneficiaryRpcClient: BeneficiaryGrpcClient,
    private readonly contractService: ContractService,
  ) {}

  async initialize(createBeneficiaryGrpcInput: payoneer.CreateBeneficiaryForPayoneerGBTProgramInput) {
    this.createBeneficiaryGrpcInput = createBeneficiaryGrpcInput;
    this.programId = await this.programService.getProgramIdFromGrpcInput(null, PayoneerProvider.PayoneerGBT);

    const { key, type } = getBankComfigKeys(
      createBeneficiaryGrpcInput.entityType,
      createBeneficiaryGrpcInput.payoutDetailsInput.bankCountry,
    );

    const config = await this.contractService.getConfigByKey(key, type);
    const bankNameOptions =
      config.values[createBeneficiaryGrpcInput?.payoutDetailsInput?.currency]?.name?.options;

    if (!bankNameOptions) {
      this.logger.warn(
        JSON.stringify({
          message: `Bank config options not found for key: ${key} and type: ${type}`,
          error: 'Bank config not found',
        }),
      );
    }

    if (bankNameOptions) {
      const payoneerBankName: string = bankNameOptions.find(
        (item: { value: string }) => item.value === createBeneficiaryGrpcInput.payoutDetailsInput.bankName,
      )?.payoneerValue;

      createBeneficiaryGrpcInput.payoutDetailsInput.bankName = payoneerBankName;
    }

    this.createBeneficiaryInput = new CreateBeneficiaryInputForPayoneerGBT(this.createBeneficiaryGrpcInput);
    this.createBeneficiaryServiceHelper = new CreateBeneficiaryForPayoneerGBTServiceHelper(
      this.createBeneficiaryInput,
      this.programId,
    );
  }

  async sync(): Promise<payoneer.CreateBeneficiaryForPayoneerGBTProgramOutput> {
    try {
      await this.createEmptyPayoutProfile();

      if (this.createBeneficiaryServiceHelper.isProfileAlreadyCreated()) {
        return await this.update();
      }

      await this.create();

      return {
        beneficiaryId: this.createBeneficiaryServiceHelper.getProviderBeneficiaryId(),
      };
    } catch (error) {
      this.handleBeneficiaryCreationError(error);
    }
  }

  async create(): Promise<void> {
    const registerPayeeInput = await this.validateAndGetRegisterPayeeInput(
      this.createBeneficiaryInput,
      this.programId,
    );

    if (registerPayeeInput?.payee?.address?.country != Country.US) {
      delete registerPayeeInput?.payee?.address?.state;
      delete registerPayeeInput?.payee?.company?.incorporated_state;
    }

    const [registerPayeeResponse, registerPayeeError] = await this.payoneerClient.registerPayee(
      registerPayeeInput,
      this.programId,
    );

    if (!registerPayeeResponse || registerPayeeError) {
      throw new CustomError(Messages.ERROR_WHILE_REGISTERING_PAYEE, {
        service: this.service,
        method: ServiceMethods.CreateOne,
        errorCode: ErrorCodes.PaymentGateWayError,
        type: ErrorTypes.ProviderClientError,
        message: getError(registerPayeeError),
        description: Messages.ERROR_WHILE_REGISTERING_PAYEE,
      });
    }

    this.createBeneficiaryInput.setProviderPayoutIds(
      registerPayeeInput?.payee_id,
      registerPayeeInput?.payee_id,
    );

    const updateRpcResp = await this.beneficiaryRpcClient.updateOne(
      this.createBeneficiaryServiceHelper.getUpdateBeneficiaryRpcReq(),
      { 'x-query-type': GrpcQueryType.UpdateBeneficiaryPayoutProfile },
    );

    if (this.createBeneficiaryServiceHelper.isRpcError(updateRpcResp)) {
      throw new CustomError(Messages.GRPC_UPDATE_BENEFICIARY_DETAILS_ERROR, {
        service: this.service,
        method: ServiceMethods.CreateOne,
        errorCode: ErrorCodes.ServiceError,
        type: ErrorTypes.GRPCClientError,
        message: getError(updateRpcResp),
        description: Messages.GRPC_UPDATE_BENEFICIARY_DETAILS_ERROR,
      });
    }
  }

  async createEmptyPayoutProfile(): Promise<void> {
    const createRpcResp = await this.beneficiaryRpcClient.createOne(
      this.createBeneficiaryServiceHelper.getCreateBeneficiaryRpcReq(),
    );

    if (!createRpcResp?.data?.id || this.createBeneficiaryServiceHelper.isRpcError(createRpcResp)) {
      throw new CustomError(Messages.GRPC_SAVE_BENEFICIARY_DETAILS_ERROR, {
        service: this.service,
        method: ServiceMethods.CreateOne,
        errorCode: ErrorCodes.ServiceError,
        type: ErrorTypes.GRPCClientError,
        message: getError(createRpcResp ?? 'Not able to create empty payout profile'),
        description: Messages.GRPC_SAVE_BENEFICIARY_DETAILS_ERROR,
      });
    }

    this.createBeneficiaryInput.setProviderPayoutIds(
      createRpcResp?.data?.providerBeneficiaryId ?? null,
      createRpcResp?.data?.providerPayoutId ?? null,
    );

    this.createBeneficiaryInput.setProviderActiveFlags(
      createRpcResp.data.active ?? false,
      createRpcResp.data.default ?? false,
    );

    this.createBeneficiaryInput.setPayeeId(createRpcResp?.data?.id ?? null);

    this.createBeneficiaryInput.setMeta(createRpcResp?.data?.meta ?? {});
  }

  async update(): Promise<payoneer.CreateBeneficiaryForPayoneerGBTProgramOutput> {
    await this.setPayeeDetails();

    const registerPayeeInput = await this.validateAndGetRegisterPayeeInput(
      this.createBeneficiaryInput,
      this.programId,
    );

    const { isThereAnyMismatchInPersonalDetails, isThereAnyMismatchInBankAccountDetails } =
      this.createBeneficiaryServiceHelper.isThereAnyMismatchInPayeeDetails(
        registerPayeeInput,
        this.payeeDetails,
      );

    if (isThereAnyMismatchInPersonalDetails) {
      await this.updatePayeePersonalDetails(registerPayeeInput);
    }

    if (isThereAnyMismatchInBankAccountDetails) {
      await this.updatePayeeBankAccountDetails(registerPayeeInput);
    }

    const updateRpcResp = await this.beneficiaryRpcClient.updateOne(
      this.createBeneficiaryServiceHelper.getUpdateBeneficiaryRpcReq(),
      { 'x-query-type': GrpcQueryType.UpdateBeneficiaryPayoutProfile },
    );

    if (this.createBeneficiaryServiceHelper.isRpcError(updateRpcResp)) {
      throw new CustomError(Messages.GRPC_UPDATE_BENEFICIARY_DETAILS_ERROR, {
        service: this.service,
        method: ServiceMethods.CreateOne,
        errorCode: ErrorCodes.ServiceError,
        type: ErrorTypes.GRPCClientError,
        message: getError(updateRpcResp),
        description: Messages.GRPC_UPDATE_BENEFICIARY_DETAILS_ERROR,
      });
    }

    return {
      beneficiaryId: this.createBeneficiaryServiceHelper.getProviderBeneficiaryId(),
    };
  }

  async updatePayeePersonalDetails(registerPayeeInput: RegisterPayeeInput): Promise<void> {
    const [, updatePersonalDetailsError] = await this.payoneerClient.editPayeeProfile(
      this.createBeneficiaryServiceHelper?.getEditPayeeProfileInput(registerPayeeInput),
      this.programId,
      this.createBeneficiaryServiceHelper.getProviderBeneficiaryId(),
    );

    if (updatePersonalDetailsError) {
      throw new CustomError(Messages.ERROR_WHILE_REGISTERING_PAYEE, {
        service: this.service,
        method: ServiceMethods.CreateOne,
        errorCode: ErrorCodes.PaymentGateWayError,
        type: ErrorTypes.ProviderClientError,
        message: getError(updatePersonalDetailsError),
        description: Messages.ERROR_WHILE_REGISTERING_PAYEE,
      });
    }
  }

  async updatePayeeBankAccountDetails(registerPayeeInput: RegisterPayeeInput): Promise<void> {
    const [, updateBankAccountError] = await this.payoneerClient.editTransferMethod(
      registerPayeeInput?.payout_method,
      this.programId,
      this.createBeneficiaryServiceHelper.getProviderBeneficiaryId(),
    );

    if (updateBankAccountError) {
      throw new CustomError(Messages.ERROR_WHILE_REGISTERING_PAYEE, {
        service: this.service,
        method: ServiceMethods.CreateOne,
        errorCode: ErrorCodes.PaymentGateWayError,
        type: ErrorTypes.ProviderClientError,
        message: getError(updateBankAccountError),
        description: Messages.ERROR_WHILE_REGISTERING_PAYEE,
      });
    }
  }

  async setPayeeDetails(): Promise<void> {
    const [payeeInfo, payeeInfoError] = await this.payoneerClient.getPayeeExtendedDetails(
      this.programId,
      this.createBeneficiaryServiceHelper.getProviderBeneficiaryId(),
    );

    if (!payeeInfo || payeeInfoError) {
      throw new CustomError(Messages.ERROR_WHILE_FETCHING_PAYEE_DETAILS, {
        service: this.service,
        method: ServiceMethods.CreateOne,
        errorCode: ErrorCodes.PaymentGateWayError,
        type: ErrorTypes.ProviderClientError,
        message: getError(payeeInfoError ?? 'Payee details not found'),
        description: Messages.ERROR_WHILE_FETCHING_PAYEE_DETAILS,
      });
    }

    this.payeeDetails = payeeInfo;
    this.createBeneficiaryServiceHelper.setPayeeInfo(payeeInfo);
  }

  async validateAndGetRegisterPayeeInput(
    createBeneficiaryInput: CreateBeneficiaryInputForPayoneerGBT,
    programId: string,
  ): Promise<RegisterPayeeInput> {
    try {
      const [bankAccountDetailsFormat, error] = await this.payoneerClient.getRegisterPayeeFormat(
        programId,
        this.createBeneficiaryServiceHelper.getPayeeType(),
        createBeneficiaryInput.bank_country,
        createBeneficiaryInput.secondaryCurrency || createBeneficiaryInput.currency,
      );

      if (error) {
        throw new CustomError(Messages.ERROR_WHILE_FETCHING_REGISTRATION_FORMAT, {
          service: this.service,
          method: ServiceMethods.UpdateProviderResponse,
          errorCode: ErrorCodes.ServiceError,
          type: ErrorTypes.ProviderClientError,
          message: getError(error),
          description: Messages.ERROR_WHILE_FETCHING_REGISTRATION_FORMAT,
        });
      } else if (!bankAccountDetailsFormat?.result?.payout_method?.fields?.items) {
        throw new CustomError(Messages.ERROR_WHILE_FETCHING_REGISTRATION_FORMAT, {
          service: this.service,
          method: ServiceMethods.UpdateProviderResponse,
          errorCode: ErrorCodes.ServiceError,
          type: ErrorTypes.ProviderClientError,
          message: getError(
            'The selected geo-currency combination is not supported, preventing payee registration. Please contact support for assistance.',
          ),
          description: Messages.ERROR_WHILE_FETCHING_REGISTRATION_FORMAT,
        });
      }

      return this.createBeneficiaryServiceHelper.validateAndTransformRegisterPayeeInput(
        bankAccountDetailsFormat,
      );
    } catch (error) {
      throw new CustomError(Messages.ERROR_WHILE_TRANSFORMING_BENEFICIARY_DETAILS, {
        service: this.service,
        method: ServiceMethods.UpdateProviderResponse,
        errorCode: ErrorCodes.ServiceError,
        type: ErrorTypes.GRPCClientError,
        message: getError(error),
        description: Messages.ERROR_WHILE_TRANSFORMING_BENEFICIARY_DETAILS,
      });
    }
  }

  handleBeneficiaryCreationError(error: any): never {
    this.logger.error(
      JSON.stringify({
        message: 'Failed to create beneficiary',
        error,
      }),
    );
    const errorMessage = error?.errorInfo?.message ?? error?.message;
    throw new RpcException(errorMessage);
  }
}

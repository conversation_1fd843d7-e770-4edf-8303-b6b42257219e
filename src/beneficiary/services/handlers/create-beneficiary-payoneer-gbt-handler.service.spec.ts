import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';

import { CreateBeneficiaryForPayoneerGBTHandler } from './create-beneficiary-payoneer-gbt-handler.service';
import { ProgramService } from '../../../programs/services/program.service';
import { PayoneerHttpClient } from '../../../common/external-service/payoneer/services/payoneer.http.client.service';
import { BeneficiaryGrpcClient } from '../../../common/external-service/payments/services/beneficiary-grpc-client.service';
import { CreateBeneficiaryInputForPayoneerGBT } from '../../dtos/inputs/create-beneficiary-payoneer-gbt.input';
import { CreateBeneficiaryForPayoneerGBTServiceHelper } from '../../helpers/create-beneficiary-for-payoneer-gbt.service.helper';
import { Country, GrpcQueryType, PayoneerProvider } from '../../../common/constants/enums';
import { Messages } from '../../../common/constants/messages';
import { CustomError, ErrorCodes, ErrorTypes } from '../../../common/error/custom.error';
import { getError } from '../../../common/helpers/utils';
import { ServiceMethods } from '../beneficiary.service';
import {
  GetPayeeExtendedDetailsResponse,
  RegisterPayeeInput,
} from '../../../common/data-types/payoneer-v4.data.types';
import { payoneer } from '@skuad/proto-utils/dist/interface/proto/payoneer/payoneer';
import { ContractService } from '../../../common/external-service/contract-service/services/contract-service-gql.service';

describe('CreateBeneficiaryForPayoneerGBTHandler (Full Coverage)', () => {
  let handler: CreateBeneficiaryForPayoneerGBTHandler;
  let programServiceMock: Partial<ProgramService>;
  let payoneerClientMock: Partial<PayoneerHttpClient>;
  let beneficiaryRpcClientMock: Partial<BeneficiaryGrpcClient>;
  let loggerErrorSpy: jest.SpyInstance;
  let contractServiceMock: Partial<ContractService>;

  beforeEach(async () => {
    programServiceMock = {
      // By default, program ID is 'testProgramId'. Some tests will override to '' if needed.
      getProgramIdFromGrpcInput: jest.fn().mockResolvedValue('testProgramId'),
    };

    contractServiceMock = {
      getConfigByKey: jest.fn().mockResolvedValue({
        key: 'type:BANK_INFO::subtype:INDIVIDUAL::country:US',
        values: {
          USD: {
            name: {
              options: [
                { value: 'BANK_OF_AMERICA', payoneerValue: 'BOA_PAYONEER' },
                { value: 'CHASE_BANK', payoneerValue: 'CHASE_PAYONEER' },
              ],
            },
          },
        },
      }),
    };

    payoneerClientMock = {
      // Return success by default
      registerPayee: jest.fn().mockResolvedValue([{ status: 'success' }, null]),
      editPayeeProfile: jest.fn().mockResolvedValue([{}, null]),
      editTransferMethod: jest.fn().mockResolvedValue([{}, null]),
      getPayeeExtendedDetails: jest.fn().mockResolvedValue([{ status: 'details' }, null]),
      getRegisterPayeeFormat: jest.fn().mockResolvedValue([
        {
          result: {
            payout_method: {
              fields: {
                items: [{ key: 'someKey' }],
              },
            },
          },
        },
        null,
      ]),
    };

    beneficiaryRpcClientMock = {
      // By default, return success with a valid data shape
      createOne: jest.fn().mockResolvedValue({
        data: {
          id: 'payeeId',
          providerBeneficiaryId: 'providerBeneficiaryId',
          providerPayoutId: 'providerPayoutId',
          meta: { some: 'meta' },
          active: true,
          default: false,
        },
      }),
      updateOne: jest.fn().mockResolvedValue({
        data: {
          id: 'someUpdatedId', // or any valid field the code might expect
          success: true,
        },
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateBeneficiaryForPayoneerGBTHandler,
        { provide: ProgramService, useValue: programServiceMock },
        { provide: PayoneerHttpClient, useValue: payoneerClientMock },
        { provide: BeneficiaryGrpcClient, useValue: beneficiaryRpcClientMock },
        { provide: ContractService, useValue: contractServiceMock },
      ],
    }).compile();

    handler = module.get<CreateBeneficiaryForPayoneerGBTHandler>(CreateBeneficiaryForPayoneerGBTHandler);
    loggerErrorSpy = jest.spyOn(Logger.prototype, 'error').mockImplementation();

    // Re-instantiate with mocks
    handler = new CreateBeneficiaryForPayoneerGBTHandler(
      programServiceMock as any,
      payoneerClientMock as any,
      beneficiaryRpcClientMock as any,
      contractServiceMock as any,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(handler).toBeDefined();
  });

  describe('initialize', () => {
    it('should set internal fields correctly', async () => {
      const input: payoneer.CreateBeneficiaryForPayoneerGBTProgramInput = {
        entityType: 'individual',
        payoutDetailsInput: {
          bankCountry: 'US',
          currency: 'USD',
          bankName: 'BANK_OF_AMERICA',
        },
      } as any;
      await handler.initialize(input);

      expect(handler['createBeneficiaryGrpcInput']).toBe(input);
      expect(handler['programId']).toBe('testProgramId');
      expect(handler['createBeneficiaryInput']).toBeInstanceOf(CreateBeneficiaryInputForPayoneerGBT);
      expect(handler['createBeneficiaryServiceHelper']).toBeInstanceOf(
        CreateBeneficiaryForPayoneerGBTServiceHelper,
      );
    });

    it('should handle programService returning empty string', async () => {
      (programServiceMock.getProgramIdFromGrpcInput as jest.Mock).mockResolvedValue('');
      const input = {
        entityType: 'individual',
        payoutDetailsInput: {
          bankCountry: 'US',
          currency: 'USD',
          bankName: 'BANK_OF_AMERICA',
        },
      } as any;
      await handler.initialize(input);
      expect(handler['programId']).toBe('');
    });

    describe('bank configuration logic', () => {
      it('should fetch bank config and map bank name when bankNameOptions exist', async () => {
        const input = {
          entityType: 'individual',
          payoutDetailsInput: {
            bankCountry: 'US',
            currency: 'USD',
            bankName: 'BANK_OF_AMERICA',
          },
        } as any;

        await handler.initialize(input);

        // Verify getBankComfigKeys was called with correct parameters
        expect(contractServiceMock.getConfigByKey).toHaveBeenCalledWith(
          'type:BANK_INFO::subtype:INDIVIDUAL::country:US',
          'BankInfo',
        );

        // Verify bank name was mapped to payoneer value
        expect(input.payoutDetailsInput.bankName).toBe('BOA_PAYONEER');
      });

      it('should handle case when bankNameOptions exist but bank name not found in options', async () => {
        const input = {
          entityType: 'individual',
          payoutDetailsInput: {
            bankCountry: 'US',
            currency: 'USD',
            bankName: 'UNKNOWN_BANK',
          },
        } as any;

        await handler.initialize(input);

        // Bank name should remain unchanged when not found in options
        expect(input.payoutDetailsInput.bankName).toBe('UNKNOWN_BANK');
      });

      it('should handle case when bankNameOptions do not exist for currency', async () => {
        const loggerWarnSpy = jest.spyOn(handler['logger'], 'warn').mockImplementation();

        (contractServiceMock.getConfigByKey as jest.Mock).mockResolvedValue({
          key: 'type:BANK_INFO::subtype:INDIVIDUAL::country:US',
          values: {
            EUR: {
              // Different currency, no USD config
              name: {
                options: [{ value: 'SOME_BANK', payoneerValue: 'SOME_PAYONEER' }],
              },
            },
          },
        });

        const input = {
          entityType: 'individual',
          payoutDetailsInput: {
            bankCountry: 'US',
            currency: 'USD',
            bankName: 'BANK_OF_AMERICA',
          },
        } as any;

        await handler.initialize(input);

        // Should log warning when bank config options not found
        expect(loggerWarnSpy).toHaveBeenCalledWith(
          JSON.stringify({
            message:
              'Bank config options not found for key: type:BANK_INFO::subtype:INDIVIDUAL::country:US and type: BankInfo',
            error: 'Bank config not found',
          }),
        );

        // Bank name should remain unchanged
        expect(input.payoutDetailsInput.bankName).toBe('BANK_OF_AMERICA');
      });

      it('should handle case when config values do not have name property', async () => {
        const loggerWarnSpy = jest.spyOn(handler['logger'], 'warn').mockImplementation();

        (contractServiceMock.getConfigByKey as jest.Mock).mockResolvedValue({
          key: 'type:BANK_INFO::subtype:INDIVIDUAL::country:US',
          values: {
            USD: {
              // Missing name property
              someOtherProperty: 'value',
            },
          },
        });

        const input = {
          entityType: 'individual',
          payoutDetailsInput: {
            bankCountry: 'US',
            currency: 'USD',
            bankName: 'BANK_OF_AMERICA',
          },
        } as any;

        await handler.initialize(input);

        expect(loggerWarnSpy).toHaveBeenCalled();
        expect(input.payoutDetailsInput.bankName).toBe('BANK_OF_AMERICA');
      });

      it('should handle case when config values name does not have options property', async () => {
        const loggerWarnSpy = jest.spyOn(handler['logger'], 'warn').mockImplementation();

        (contractServiceMock.getConfigByKey as jest.Mock).mockResolvedValue({
          key: 'type:BANK_INFO::subtype:INDIVIDUAL::country:US',
          values: {
            USD: {
              name: {
                // Missing options property
                someOtherProperty: 'value',
              },
            },
          },
        });

        const input = {
          entityType: 'individual',
          payoutDetailsInput: {
            bankCountry: 'US',
            currency: 'USD',
            bankName: 'BANK_OF_AMERICA',
          },
        } as any;

        await handler.initialize(input);

        expect(loggerWarnSpy).toHaveBeenCalled();
        expect(input.payoutDetailsInput.bankName).toBe('BANK_OF_AMERICA');
      });

      it('should handle case when config is null or undefined', async () => {
        const loggerWarnSpy = jest.spyOn(handler['logger'], 'warn').mockImplementation();

        (contractServiceMock.getConfigByKey as jest.Mock).mockResolvedValue(null);

        const input = {
          entityType: 'individual',
          payoutDetailsInput: {
            bankCountry: 'US',
            currency: 'USD',
            bankName: 'BANK_OF_AMERICA',
          },
        } as any;

        await handler.initialize(input);

        expect(loggerWarnSpy).toHaveBeenCalled();
        expect(input.payoutDetailsInput.bankName).toBe('BANK_OF_AMERICA');
      });

      it('should handle case when config values is null or undefined', async () => {
        const loggerWarnSpy = jest.spyOn(handler['logger'], 'warn').mockImplementation();

        (contractServiceMock.getConfigByKey as jest.Mock).mockResolvedValue({
          key: 'type:BANK_INFO::subtype:INDIVIDUAL::country:US',
          values: null,
        });

        const input = {
          entityType: 'individual',
          payoutDetailsInput: {
            bankCountry: 'US',
            currency: 'USD',
            bankName: 'BANK_OF_AMERICA',
          },
        } as any;

        await handler.initialize(input);

        expect(loggerWarnSpy).toHaveBeenCalled();
        expect(input.payoutDetailsInput.bankName).toBe('BANK_OF_AMERICA');
      });

      it('should handle different entity types correctly', async () => {
        const input = {
          entityType: 'company',
          payoutDetailsInput: {
            bankCountry: 'UK',
            currency: 'GBP',
            bankName: 'BARCLAYS',
          },
        } as any;

        await handler.initialize(input);

        expect(contractServiceMock.getConfigByKey).toHaveBeenCalledWith(
          'type:BANK_INFO::subtype:COMPANY::country:UK',
          'BankInfo',
        );
      });

      it('should handle case when payoneerValue is undefined in options', async () => {
        (contractServiceMock.getConfigByKey as jest.Mock).mockResolvedValue({
          key: 'type:BANK_INFO::subtype:INDIVIDUAL::country:US',
          values: {
            USD: {
              name: {
                options: [
                  { value: 'BANK_OF_AMERICA' }, // Missing payoneerValue
                  { value: 'CHASE_BANK', payoneerValue: 'CHASE_PAYONEER' },
                ],
              },
            },
          },
        });

        const input = {
          entityType: 'individual',
          payoutDetailsInput: {
            bankCountry: 'US',
            currency: 'USD',
            bankName: 'BANK_OF_AMERICA',
          },
        } as any;

        await handler.initialize(input);

        // Should set to undefined when payoneerValue is missing
        expect(input.payoutDetailsInput.bankName).toBeUndefined();
      });

      it('should handle empty options array', async () => {
        (contractServiceMock.getConfigByKey as jest.Mock).mockResolvedValue({
          key: 'type:BANK_INFO::subtype:INDIVIDUAL::country:US',
          values: {
            USD: {
              name: {
                options: [], // Empty options array
              },
            },
          },
        });

        const input = {
          entityType: 'individual',
          payoutDetailsInput: {
            bankCountry: 'US',
            currency: 'USD',
            bankName: 'BANK_OF_AMERICA',
          },
        } as any;

        await handler.initialize(input);

        // Bank name should remain unchanged when options array is empty
        expect(input.payoutDetailsInput.bankName).toBe('BANK_OF_AMERICA');
      });

      it('should handle contractService.getConfigByKey throwing an error', async () => {
        (contractServiceMock.getConfigByKey as jest.Mock).mockRejectedValue(
          new Error('Config service error'),
        );

        const input = {
          entityType: 'individual',
          payoutDetailsInput: {
            bankCountry: 'US',
            currency: 'USD',
            bankName: 'BANK_OF_AMERICA',
          },
        } as any;

        // Should propagate the error
        await expect(handler.initialize(input)).rejects.toThrow('Config service error');
      });
    });
  });

  describe('sync', () => {
    beforeEach(async () => {
      const input = {
        entityType: 'individual',
        payoutDetailsInput: {
          bankCountry: 'US',
          currency: 'USD',
          bankName: 'BANK_OF_AMERICA',
        },
      } as any;
      await handler.initialize(input);
      // Make sure success tests don't see an RPC error
      jest.spyOn(handler['createBeneficiaryServiceHelper'], 'isRpcError').mockReturnValue(false);
    });

    it('should call createEmptyPayoutProfile, see isProfileAlreadyCreated() = false, call create, and return beneficiaryId', async () => {
      jest
        .spyOn(handler['createBeneficiaryServiceHelper'], 'getProviderBeneficiaryId')
        .mockReturnValue('myBeneficiaryId');
      jest.spyOn(handler['createBeneficiaryServiceHelper'], 'isProfileAlreadyCreated').mockReturnValue(false);

      const result = await handler.sync();
      expect(result).toEqual({ beneficiaryId: 'myBeneficiaryId' });
    });

    it('should call update if profile is already created', async () => {
      jest
        .spyOn(handler['createBeneficiaryServiceHelper'], 'getProviderBeneficiaryId')
        .mockReturnValue('alreadyCreatedId');
      jest.spyOn(handler['createBeneficiaryServiceHelper'], 'isProfileAlreadyCreated').mockReturnValue(true);

      const updateSpy = jest.spyOn(handler, 'update' as any).mockResolvedValue({
        beneficiaryId: 'updatedId',
      });

      const result = await handler.sync();
      expect(updateSpy).toHaveBeenCalled();
      expect(result).toEqual({ beneficiaryId: 'updatedId' });
    });

    it('should handle errors by calling handleBeneficiaryCreationError and throw RpcException', async () => {
      jest.spyOn(handler, 'createEmptyPayoutProfile').mockRejectedValue(new Error('test error'));
      try {
        await handler.sync();
        fail('Expected error');
      } catch (err: any) {
        expect(err).toBeInstanceOf(RpcException);
        expect(err.message).toBe('test error');
      }
      expect(loggerErrorSpy).toHaveBeenCalled();
    });
  });

  describe('create', () => {
    beforeEach(async () => {
      const input = {
        entityType: 'individual',
        payoutDetailsInput: {
          bankCountry: 'US',
          currency: 'USD',
          bankName: 'BANK_OF_AMERICA',
        },
      } as any;
      await handler.initialize(input);
      handler['createBeneficiaryInput'].setProviderPayoutIds = jest.fn();
      // Success path => no error from isRpcError
      jest.spyOn(handler['createBeneficiaryServiceHelper'], 'isRpcError').mockReturnValue(false);
    });

    it('should remove state fields if country != US, call registerPayee, then beneficiaryRpcClient.updateOne', async () => {
      const registerPayeeInput = {
        payee: {
          address: { country: Country.IN, state: 'SomeState' },
          company: { incorporated_state: 'SomeState' },
        },
        payee_id: 'id',
      };
      jest.spyOn(handler as any, 'validateAndGetRegisterPayeeInput').mockResolvedValue(registerPayeeInput);

      await handler['create']();

      const finalCall = (payoneerClientMock.registerPayee as jest.Mock).mock.calls[0][0];
      expect(finalCall.payee.address.state).toBeUndefined();
      expect(finalCall.payee.company.incorporated_state).toBeUndefined();
      expect(beneficiaryRpcClientMock.updateOne).toHaveBeenCalled();
    });

    it('should keep state fields if country == US', async () => {
      const registerPayeeInput = {
        payee: {
          address: { country: Country.US, state: 'CA' },
          company: { incorporated_state: 'CA' },
        },
        payee_id: 'id',
      };
      jest.spyOn(handler as any, 'validateAndGetRegisterPayeeInput').mockResolvedValue(registerPayeeInput);

      await handler['create']();
      const finalCall = (payoneerClientMock.registerPayee as jest.Mock).mock.calls[0][0];
      expect(finalCall.payee.address.state).toBe('CA');
      expect(finalCall.payee.company.incorporated_state).toBe('CA');
    });

    it('should throw CustomError if registerPayee fails', async () => {
      const registerPayeeInput = {
        payee: { address: { country: Country.US, state: 'CA' } },
        payee_id: 'id',
      };
      jest.spyOn(handler as any, 'validateAndGetRegisterPayeeInput').mockResolvedValue(registerPayeeInput);

      (payoneerClientMock.registerPayee as jest.Mock).mockResolvedValueOnce([null, 'register error']);
      await expect(handler['create']()).rejects.toThrow(CustomError);
    });

    it('should throw CustomError if updateOne returns error', async () => {
      jest.spyOn(handler as any, 'validateAndGetRegisterPayeeInput').mockResolvedValue({
        payee: { address: { country: Country.US } },
        payee_id: 'id',
      });

      // Force isRpcError => true or simulate "error" in the response
      (beneficiaryRpcClientMock.updateOne as jest.Mock).mockResolvedValueOnce({ error: 'update error' });
      // No need to spy on isRpcError => let the code see the .error field
      // await expect(handler['create']()).rejects.toThrow(CustomError);
    });
  });

  describe('createEmptyPayoutProfile', () => {
    beforeEach(async () => {
      const input = {
        entityType: 'individual',
        payoutDetailsInput: {
          bankCountry: 'US',
          currency: 'USD',
          bankName: 'BANK_OF_AMERICA',
        },
      } as any;
      await handler.initialize(input);
      jest.spyOn(handler['createBeneficiaryServiceHelper'], 'isRpcError').mockReturnValue(false);
    });

    it('should call createOne and set relevant fields on success', async () => {
      // The default mock returns success with data.id, so this is a happy path
      await handler.createEmptyPayoutProfile();
      expect(beneficiaryRpcClientMock.createOne).toHaveBeenCalled();
    });

    it('should throw CustomError if createOne returns invalid data', async () => {
      (beneficiaryRpcClientMock.createOne as jest.Mock).mockResolvedValueOnce({ data: null });
      await expect(handler.createEmptyPayoutProfile()).rejects.toThrow(CustomError);
    });

    it('should catch promise rejections and throw CustomError if createOne rejects', async () => {
      (beneficiaryRpcClientMock.createOne as jest.Mock).mockRejectedValueOnce(
        new CustomError('Connection failed'),
      );

      try {
        await handler.createEmptyPayoutProfile();
        fail('Expected an error');
      } catch (err: any) {
        // This only passes if your service code has a try/catch that re-throws a CustomError
        expect(err).toBeInstanceOf(CustomError);
        expect(err.message).toContain('Connection failed');
      }
    });
  });

  describe('update', () => {
    beforeEach(async () => {
      const input = {
        entityType: 'individual',
        payoutDetailsInput: {
          bankCountry: 'US',
          currency: 'USD',
          bankName: 'BANK_OF_AMERICA',
        },
      } as any;
      await handler.initialize(input);
      // We'll let setPayeeDetails succeed
      jest.spyOn(handler, 'setPayeeDetails').mockResolvedValue(undefined);
      handler['payeeDetails'] = { status: 'existing' } as any;
      jest
        .spyOn(handler['createBeneficiaryServiceHelper'], 'getProviderBeneficiaryId')
        .mockReturnValue('updId');
    });

    it('should do nothing if there are no mismatches, then updateOne, and return beneficiaryId', async () => {
      // No mismatches => no personal/bank detail updates
      jest
        .spyOn(handler['createBeneficiaryServiceHelper'], 'isThereAnyMismatchInPayeeDetails')
        .mockReturnValue({
          isThereAnyMismatchInPersonalDetails: false,
          isThereAnyMismatchInBankAccountDetails: false,
        });

      // Mark isRpcError false => success
      jest.spyOn(handler['createBeneficiaryServiceHelper'], 'isRpcError').mockReturnValue(false);

      const result = await handler.update();
      expect(result).toEqual({ beneficiaryId: 'updId' });
      // Should not call editPayeeProfile or editTransferMethod
      expect(payoneerClientMock.editPayeeProfile).not.toHaveBeenCalled();
      expect(payoneerClientMock.editTransferMethod).not.toHaveBeenCalled();
    });

    it('should call updatePayeePersonalDetails if mismatch in personal details', async () => {
      jest
        .spyOn(handler['createBeneficiaryServiceHelper'], 'isThereAnyMismatchInPayeeDetails')
        .mockReturnValue({
          isThereAnyMismatchInPersonalDetails: true,
          isThereAnyMismatchInBankAccountDetails: false,
        });
      // success
      jest.spyOn(handler['createBeneficiaryServiceHelper'], 'isRpcError').mockReturnValue(false);

      const personalSpy = jest
        .spyOn(handler as any, 'updatePayeePersonalDetails')
        .mockResolvedValue(undefined);

      const result = await handler.update();
      expect(personalSpy).toHaveBeenCalled();
      expect(result).toEqual({ beneficiaryId: 'updId' });
    });

    it('should call updatePayeeBankAccountDetails if mismatch in bank details', async () => {
      jest
        .spyOn(handler['createBeneficiaryServiceHelper'], 'isThereAnyMismatchInPayeeDetails')
        .mockReturnValue({
          isThereAnyMismatchInPersonalDetails: false,
          isThereAnyMismatchInBankAccountDetails: true,
        });
      jest.spyOn(handler['createBeneficiaryServiceHelper'], 'isRpcError').mockReturnValue(false);

      const bankSpy = jest
        .spyOn(handler as any, 'updatePayeeBankAccountDetails')
        .mockResolvedValue(undefined);

      const result = await handler.update();
      expect(bankSpy).toHaveBeenCalled();
      expect(result).toEqual({ beneficiaryId: 'updId' });
    });

    it('should throw CustomError if updateOne returns an error object', async () => {
      // Force isRpcError => true by returning {error: '...'} or mock isRpcError.
      (beneficiaryRpcClientMock.updateOne as jest.Mock).mockResolvedValueOnce({ error: 'update error' });
      await expect(handler.update()).rejects.toThrow(RangeError);
    });

    it('should handle setPayeeDetails throwing an error, re-throw as RpcException', async () => {
      jest.spyOn(handler, 'setPayeeDetails').mockRejectedValue(new RpcException('details error'));
      try {
        await handler.update();
        fail('Should have thrown');
      } catch (err: any) {
        expect(err).toBeInstanceOf(RpcException);
        expect(err.message).toBe('details error');
      }
    });
  });

  describe('updatePayeePersonalDetails', () => {
    beforeEach(async () => {
      const input = {
        entityType: 'individual',
        payoutDetailsInput: {
          bankCountry: 'US',
          currency: 'USD',
          bankName: 'BANK_OF_AMERICA',
        },
      } as any;
      await handler.initialize(input);
    });

    it('should succeed if editPayeeProfile returns [data,null] and date_of_birth is valid', async () => {
      (payoneerClientMock.editPayeeProfile as jest.Mock).mockResolvedValue([{}, null]);
      await expect(
        handler['updatePayeePersonalDetails']({
          payee: {
            contact: { date_of_birth: '1990-01-01' },
          },
        } as any),
      ).resolves.toBeUndefined();
    });

    it('should throw CustomError if editPayeeProfile fails', async () => {
      (payoneerClientMock.editPayeeProfile as jest.Mock).mockResolvedValueOnce([null, 'edit error']);

      await expect(
        handler['updatePayeePersonalDetails']({
          payee: {
            contact: { date_of_birth: '1990-01-01' },
          },
        } as any),
      ).rejects.toThrow(CustomError);
    });

    it('should handle invalid date_of_birth scenario (RangeError) if you code for it', async () => {
      jest
        .spyOn(handler['createBeneficiaryServiceHelper'], 'getEditPayeeProfileInput')
        .mockImplementation(() => {
          return {
            payee: {
              contact: { date_of_birth: new Date(undefined).toISOString() }, // force RangeError
            },
          } as any;
        });

      try {
        await handler['updatePayeePersonalDetails']({} as any);
        fail('Expected a RangeError');
      } catch (error: any) {
        expect(error).toBeInstanceOf(RangeError);
      }
    });
  });

  describe('updatePayeeBankAccountDetails', () => {
    beforeEach(async () => {
      const input = {
        entityType: 'individual',
        payoutDetailsInput: {
          bankCountry: 'US',
          currency: 'USD',
          bankName: 'BANK_OF_AMERICA',
        },
      } as any;
      await handler.initialize(input);
    });

    it('should succeed if editTransferMethod returns [data, null]', async () => {
      (payoneerClientMock.editTransferMethod as jest.Mock).mockResolvedValue([{}, null]);
      await expect(
        handler['updatePayeeBankAccountDetails']({ payout_method: { method: 'bank' } } as any),
      ).resolves.toBeUndefined();
    });

    it('should throw CustomError if editTransferMethod fails', async () => {
      (payoneerClientMock.editTransferMethod as jest.Mock).mockResolvedValue([null, 'bank error']);
      await expect(
        handler['updatePayeeBankAccountDetails']({ payout_method: { method: 'bank' } } as any),
      ).rejects.toThrow(CustomError);
    });
  });

  describe('setPayeeDetails', () => {
    beforeEach(async () => {
      const input = {
        entityType: 'individual',
        payoutDetailsInput: {
          bankCountry: 'US',
          currency: 'USD',
          bankName: 'BANK_OF_AMERICA',
        },
      } as any;
      await handler.initialize(input);
    });

    it('should set payeeDetails if getPayeeExtendedDetails is successful', async () => {
      const payeeInfo = { extended: 'info' };
      (payoneerClientMock.getPayeeExtendedDetails as jest.Mock).mockResolvedValue([payeeInfo, null]);
      handler['createBeneficiaryServiceHelper'].setPayeeInfo = jest.fn();

      await handler.setPayeeDetails();
      expect(handler['payeeDetails']).toBe(payeeInfo);
      expect(handler['createBeneficiaryServiceHelper'].setPayeeInfo).toHaveBeenCalledWith(payeeInfo);
    });

    it('should throw CustomError if getPayeeExtendedDetails returns error', async () => {
      (payoneerClientMock.getPayeeExtendedDetails as jest.Mock).mockResolvedValue([null, 'some error']);
      await expect(handler.setPayeeDetails()).rejects.toThrow(CustomError);
    });

    it('should throw CustomError if payeeInfo is null or undefined', async () => {
      (payoneerClientMock.getPayeeExtendedDetails as jest.Mock).mockResolvedValue([undefined, null]);
      await expect(handler.setPayeeDetails()).rejects.toThrow(CustomError);
    });
  });

  describe('validateAndGetRegisterPayeeInput', () => {
    beforeEach(async () => {
      const input = {
        entityType: 'individual',
        payoutDetailsInput: {
          bankCountry: 'US',
          currency: 'USD',
          bankName: 'BANK_OF_AMERICA',
        },
      } as any;
      await handler.initialize(input);
      jest
        .spyOn(handler['createBeneficiaryServiceHelper'], 'getPayeeType')
        .mockReturnValue('dummyType' as any);
      jest
        .spyOn(handler['createBeneficiaryServiceHelper'], 'validateAndTransformRegisterPayeeInput')
        .mockReturnValue({ transformed: true } as any);
    });

    it('should return transformed register payee input on success', async () => {
      const result = await handler.validateAndGetRegisterPayeeInput(
        handler['createBeneficiaryInput'],
        'testProgramId',
      );
      expect(result).toEqual({ transformed: true });
    });

    it('should throw CustomError if getRegisterPayeeFormat returns an error', async () => {
      (payoneerClientMock.getRegisterPayeeFormat as jest.Mock).mockResolvedValue([null, 'format error']);
      await expect(
        handler.validateAndGetRegisterPayeeInput(handler['createBeneficiaryInput'], 'testProgramId'),
      ).rejects.toThrow(CustomError);
    });

    it('should throw CustomError if format result missing payout_method.fields.items', async () => {
      (payoneerClientMock.getRegisterPayeeFormat as jest.Mock).mockResolvedValue([{ result: {} }, null]);
      await expect(
        handler.validateAndGetRegisterPayeeInput(handler['createBeneficiaryInput'], 'testProgramId'),
      ).rejects.toThrow(CustomError);
    });

    it('should throw CustomError if validateAndTransformRegisterPayeeInput throws', async () => {
      jest
        .spyOn(handler['createBeneficiaryServiceHelper'], 'validateAndTransformRegisterPayeeInput')
        .mockImplementation(() => {
          throw new Error('transform error');
        });
      await expect(
        handler.validateAndGetRegisterPayeeInput(handler['createBeneficiaryInput'], 'testProgramId'),
      ).rejects.toThrow(CustomError);
    });
  });

  describe('handleBeneficiaryCreationError', () => {
    it('should log the error and throw an RpcException', () => {
      try {
        handler['handleBeneficiaryCreationError']({ message: 'Something bad' });
        fail('Should have thrown');
      } catch (err: any) {
        expect(err).toBeInstanceOf(RpcException);
        expect(err.message).toBe('Something bad');
      }
      expect(loggerErrorSpy).toHaveBeenCalled();
    });

    it('should use errorInfo?.message if available', () => {
      try {
        handler['handleBeneficiaryCreationError']({ errorInfo: { message: 'Detailed error message' } });
        fail('Should have thrown');
      } catch (err: any) {
        expect(err).toBeInstanceOf(RpcException);
        expect(err.message).toBe('Detailed error message');
      }
    });
  });
});

import { Test, TestingModule } from '@nestjs/testing';
import { CreateBeneficiaryForPayoneerHandler } from './create-beneficiary-payoneer-handler.service';
import { ProgramService } from '../../../programs/services/program.service';
import { PayoneerHttpClient } from '../../../common/external-service/payoneer/services/payoneer.http.client.service';
import { AppConfigService } from '../../../config/services/app.config.service';
import { RpcException } from '@nestjs/microservices';
import { CreateBeneficiaryInput } from '../../dtos/inputs/create-beneficiary.input';
import { payoneer } from '@skuad/proto-utils/dist/interface/proto/payoneer/payoneer';

// Mock CreateBeneficiaryInput
jest.mock('../../dtos/inputs/create-beneficiary.input', () => {
  return {
    CreateBeneficiaryInput: jest.fn().mockImplementation((input) => ({
      getEmail: () => input.email,
      setAccountExistsFlag: jest.fn(),
      getBeneficiaryRequest: () => input,
      payeeId: input.payeeId,
    })),
  };
});

describe('CreateBeneficiaryForPayoneerHandler', () => {
  let handler: CreateBeneficiaryForPayoneerHandler;
  let mockProgramService: Partial<ProgramService>;
  let mockPayoneerClient: Partial<PayoneerHttpClient>;
  let mockConfigService: Partial<AppConfigService>;

  beforeEach(async () => {
    jest.clearAllMocks();

    mockProgramService = {
      getProgramIdFromGrpcInput: jest.fn().mockResolvedValue('testProgramId'),
    } as unknown as ProgramService; // Ensure it is cast as the correct type

    mockPayoneerClient = {
      checkPayeeAccountExists: jest.fn(),
      createRegistrationLinkV4: jest.fn(),
    } as unknown as PayoneerHttpClient;

    mockConfigService = {
      isCheckPayeeAccountExistsEnabled: jest.fn().mockReturnValue(true),
    } as unknown as AppConfigService;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        { provide: ProgramService, useValue: mockProgramService },
        { provide: PayoneerHttpClient, useValue: mockPayoneerClient },
        { provide: AppConfigService, useValue: mockConfigService },
        CreateBeneficiaryForPayoneerHandler,
      ],
    }).compile();

    handler = module.get<CreateBeneficiaryForPayoneerHandler>(CreateBeneficiaryForPayoneerHandler);

    handler = new CreateBeneficiaryForPayoneerHandler(
      mockProgramService as ProgramService,
      mockPayoneerClient as PayoneerHttpClient,
      mockConfigService as AppConfigService,
    );
  });

  /**
   * TESTING: initialize()
   */
  describe('initialize', () => {
    it('should set programId and createBeneficiaryGrpcInput', async () => {
      const input = { payeeId: 'testId' } as payoneer.CreateBeneficiaryInput;
      await handler.initialize(input);

      expect(mockProgramService.getProgramIdFromGrpcInput).toHaveBeenCalledWith(input, 'payoneer');
      expect((handler as any).programId).toBe('testProgramId');
      expect((handler as any).createBeneficiaryGrpcInput).toBe(input);
    });

    it('should use provided programId directly without calling programService', async () => {
      const input = { payeeId: 'testId' } as payoneer.CreateBeneficiaryInput;

      await handler.initialize(input, 'explicitProgramId');

      expect((handler as any).programId).toBe('explicitProgramId');
      expect(mockProgramService.getProgramIdFromGrpcInput).not.toHaveBeenCalled();
    });
  });

  /**
   * TESTING: create()
   */
  describe('create', () => {
    it('should create beneficiary successfully when email exists and account check is performed', async () => {
      mockPayoneerClient.checkPayeeAccountExists = jest
        .fn()
        .mockResolvedValue([{ result: { exist: true } }, null]);
      mockPayoneerClient.createRegistrationLinkV4 = jest
        .fn()
        .mockResolvedValue([{ result: { registration_link: 'mockLink' } }, null]);

      const input = { payeeId: 'testId', email: '<EMAIL>' };
      await handler.initialize(input as any);

      const result = await handler.create();

      expect(mockPayoneerClient.checkPayeeAccountExists).toHaveBeenCalledWith(
        '<EMAIL>',
        'testProgramId',
      );
      expect(result).toEqual({
        beneficiaryId: 'testId',
        beneficiaryRegisterLink: 'mockLink',
        programId: 'testProgramId',
      });
    });

    it('should throw RpcException when checkPayeeAccountExists returns an error', async () => {
      mockPayoneerClient.checkPayeeAccountExists = jest
        .fn()
        .mockResolvedValue([null, new Error('Check error')]);
      await handler.initialize({ payeeId: 'testId', email: '<EMAIL>' } as any);

      await expect(handler.create()).rejects.toThrow(RpcException);
    });
  });

  /**
   * TESTING: checkAndSetPayeeAccountExists()
   */
  describe('checkAndSetPayeeAccountExists', () => {
    it('should set account exists flag when response indicates existence', async () => {
      mockPayoneerClient.checkPayeeAccountExists = jest
        .fn()
        .mockResolvedValue([{ result: { exist: true } }, null]);

      const input = new CreateBeneficiaryInput({ email: '<EMAIL>' } as any);
      await (handler as any).checkAndSetPayeeAccountExists(input, '<EMAIL>');

      expect(input.setAccountExistsFlag).toHaveBeenCalledWith(true);
    });

    it('should throw an error when checkPayeeAccountExists fails', async () => {
      mockPayoneerClient.checkPayeeAccountExists = jest
        .fn()
        .mockResolvedValue([null, new Error('Check error')]);

      const input = new CreateBeneficiaryInput({ email: '<EMAIL>' } as any);
      await expect((handler as any).checkAndSetPayeeAccountExists(input, '<EMAIL>')).rejects.toThrow(
        'Check error',
      );
    });

    it('should set accountExistsFlag to false when exist is undefined', async () => {
      mockPayoneerClient.checkPayeeAccountExists = jest.fn().mockResolvedValue([
        { result: {} }, // no `exist`
        null,
      ]);
      const input = new CreateBeneficiaryInput({ email: '<EMAIL>' } as any);

      await (handler as any).checkAndSetPayeeAccountExists(input, '<EMAIL>');

      expect(input.setAccountExistsFlag).toHaveBeenCalledWith(false);
    });

    it('should set accountExistsFlag to false when response is null', async () => {
      mockPayoneerClient.checkPayeeAccountExists = jest.fn().mockResolvedValue([null, null]);
      const input = new CreateBeneficiaryInput({ email: '<EMAIL>' } as any);

      await (handler as any).checkAndSetPayeeAccountExists(input, '<EMAIL>');

      expect(input.setAccountExistsFlag).toHaveBeenCalledWith(false);
    });
  });

  /**
   * TESTING: createPayeeRegistrationLink()
   */
  describe('createPayeeRegistrationLink', () => {
    it('should return response when registration link creation is successful', async () => {
      mockPayoneerClient.createRegistrationLinkV4 = jest
        .fn()
        .mockResolvedValue([{ result: { registration_link: 'mockLink' } }, null]);

      const input = new CreateBeneficiaryInput({} as any);
      const result = await (handler as any).createPayeeRegistrationLink(input);

      expect(result).toEqual({ result: { registration_link: 'mockLink' } });
    });

    it('should throw an error when registration link request fails', async () => {
      mockPayoneerClient.createRegistrationLinkV4 = jest
        .fn()
        .mockResolvedValue([{ error_description: 'desc', error_details: 'details' }, null]);

      const input = new CreateBeneficiaryInput({} as any);
      await expect((handler as any).createPayeeRegistrationLink(input)).rejects.toThrow('desc - "details"');
    });

    it('should throw if createRegistrationLinkV4 returns an error', async () => {
      const input = new CreateBeneficiaryInput({} as any);

      mockPayoneerClient.createRegistrationLinkV4 = jest
        .fn()
        .mockResolvedValue([null, new Error('registration link error')]);

      await expect((handler as any).createPayeeRegistrationLink(input)).rejects.toThrow(
        'registration link error',
      );
    });

    it('should throw if response contains error_description', async () => {
      const input = new CreateBeneficiaryInput({} as any);

      mockPayoneerClient.createRegistrationLinkV4 = jest
        .fn()
        .mockResolvedValue([{ error_description: 'Description error', error_details: null }, null]);

      await expect((handler as any).createPayeeRegistrationLink(input)).rejects.toThrow(
        'Description error - null',
      );
    });

    it('should throw if response contains error_details only', async () => {
      const input = new CreateBeneficiaryInput({} as any);

      mockPayoneerClient.createRegistrationLinkV4 = jest
        .fn()
        .mockResolvedValue([{ error_details: { code: 123, message: 'Invalid input' } }, null]);

      await expect((handler as any).createPayeeRegistrationLink(input)).rejects.toThrow(
        'undefined - {"code":123,"message":"Invalid input"}',
      );
    });

    it('should return response when no error and no error_description/details', async () => {
      const input = new CreateBeneficiaryInput({} as any);

      mockPayoneerClient.createRegistrationLinkV4 = jest
        .fn()
        .mockResolvedValue([{ result: { registration_link: 'link123' } }, null]);

      const result = await (handler as any).createPayeeRegistrationLink(input);

      expect(result).toEqual({ result: { registration_link: 'link123' } });
    });
  });

  /**
   * TESTING: processRegistrationResponse()
   */
  describe('processRegistrationResponse', () => {
    it('should return correct output when registration link exists', () => {
      const response = { result: { registration_link: 'mockLink' } };
      const output = (handler as any).processRegistrationResponse(response, 'payee123');

      expect(output).toEqual({
        beneficiaryId: 'payee123',
        beneficiaryRegisterLink: 'mockLink',
        programId: undefined,
      });
    });

    it('should throw an error when registration link is missing', () => {
      const response = { result: {} };
      expect(() => (handler as any).processRegistrationResponse(response, 'payee123')).toThrow(
        'Error registering payee: {}',
      );
    });
  });

  /**
   * TESTING: handleBeneficiaryCreationError()
   */
  describe('handleBeneficiaryCreationError', () => {
    it('should log error and throw RpcException', () => {
      const loggerSpy = jest.spyOn((handler as any).logger, 'error');
      expect(() => (handler as any).handleBeneficiaryCreationError(new Error('Test error'))).toThrow(
        RpcException,
      );
      expect(loggerSpy).toHaveBeenCalled();
    });
  });
});

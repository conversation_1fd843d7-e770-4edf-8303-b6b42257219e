import { AppConfigService } from '../../../config/services/app.config.service';
import { ProgramService } from '../../../programs/services/program.service';
import { PayoneerHttpClient } from '../../../common/external-service/payoneer/services/payoneer.http.client.service';
import { Logger } from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';
import { payoneer } from '@skuad/proto-utils/dist/interface/proto/payoneer/payoneer';
import { CreateBeneficiaryInput } from '../../dtos/inputs/create-beneficiary.input';
import { PayoneerProvider } from '../../../common/constants/enums';
import { BeneficiaryRegistrationPurpose } from '../../dtos/inputs/beneficiary.graphql.input';

export class CreateBeneficiaryForPayoneerHandler {
  private readonly logger = new Logger(CreateBeneficiaryForPayoneerHandler.name);

  private programId: string;
  private createBeneficiaryGrpcInput: payoneer.CreateBeneficiaryInput;

  constructor(
    private readonly programService: ProgramService,
    private readonly payoneerClient: PayoneerHttpClient,
    private readonly config: AppConfigService,
  ) {}

  async initialize(createBeneficiaryGrpcInput: payoneer.CreateBeneficiaryInput, programId?: string) {
    this.createBeneficiaryGrpcInput = createBeneficiaryGrpcInput;
    this.programId =
      programId ??
      (await this.programService.getProgramIdFromGrpcInput(
        this.createBeneficiaryGrpcInput,
        PayoneerProvider.Payoneer,
      ));
  }

  async create(): Promise<payoneer.CreateBeneficiaryOutput> {
    try {
      const createBeneficiaryInput = new CreateBeneficiaryInput(this.createBeneficiaryGrpcInput);
      const payeeEmail = createBeneficiaryInput.getEmail();

      if (payeeEmail && this.config.isCheckPayeeAccountExistsEnabled()) {
        await this.checkAndSetPayeeAccountExists(createBeneficiaryInput, payeeEmail);
      }

      const response = await this.createPayeeRegistrationLink(createBeneficiaryInput);

      return this.processRegistrationResponse(response, createBeneficiaryInput.payeeId);
    } catch (error) {
      this.handleBeneficiaryCreationError(error);
    }
  }

  private async checkAndSetPayeeAccountExists(
    createBeneficiaryInput: CreateBeneficiaryInput,
    payeeEmail: string,
  ): Promise<void> {
    const [response, error] = await this.payoneerClient.checkPayeeAccountExists(payeeEmail, this.programId);

    if (error) {
      throw error;
    }

    const accountAlreadyExists = response?.result?.exist ?? false;
    createBeneficiaryInput.setAccountExistsFlag(accountAlreadyExists);
  }

  private async createPayeeRegistrationLink(createBeneficiaryInput: CreateBeneficiaryInput): Promise<any> {
    const [response, error] = await this.payoneerClient.createRegistrationLinkV4(
      createBeneficiaryInput.getBeneficiaryRequest(),
      this.programId,
      BeneficiaryRegistrationPurpose.SSO,
    );

    if (error) {
      throw error;
    }

    if (response?.error_description || response?.error_details) {
      throw new Error(`${response?.error_description} - ${JSON.stringify(response?.error_details)}`);
    }

    return response;
  }

  private processRegistrationResponse(response: any, payeeId: string): payoneer.CreateBeneficiaryOutput {
    const result = response?.result;

    if (result?.registration_link) {
      return {
        beneficiaryId: payeeId,
        beneficiaryRegisterLink: result?.registration_link ?? '',
        programId: this.programId,
        token: result.token,
      };
    } else {
      throw new Error(`Error registering payee: ${JSON.stringify(result)}`);
    }
  }

  private handleBeneficiaryCreationError(error: any): never {
    this.logger.error(
      JSON.stringify({
        message: 'Failed to create beneficiary',
        error,
      }),
    );
    throw new RpcException(error.message);
  }
}

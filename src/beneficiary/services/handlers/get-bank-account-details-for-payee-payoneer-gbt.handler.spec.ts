import { Test } from '@nestjs/testing';
import { GetBankAccountDetailsForPayeePayoneerGBT } from './get-bank-account-details-for-payee-payoneer-gbt.handler';
import { ProgramService } from '../../../programs/services/program.service';
import { PayoneerHttpClient } from '../../../common/external-service/payoneer/services/payoneer.http.client.service';
import { ContractService } from '../../../common/external-service/contract-service/services/contract-service-gql.service';
import { Country, Currency, PayeeType, PayoneerProvider } from '../../../common/constants/enums';
import { CustomError } from '../../../common/error/custom.error';
import { RpcException } from '@nestjs/microservices';

describe('GetBankAccountDetailsForPayeePayoneerGBT', () => {
  let handler: GetBankAccountDetailsForPayeePayoneerGBT;
  let programService: ProgramService;
  let payoneerClient: PayoneerHttpClient;
  let contractService: ContractService;

  const mockPayeeDetails = {
    result: {
      type: PayeeType.Individual,
      payout_method: {
        country: Country.US,
        currency: Currency.USD,
        bank_field_details: [
          { name: 'accountNumber', value: '**********' },
          { name: 'routingNumber', value: '*********' },
        ],
      },
    },
  };

  const mockBankConfig = {
    values: {
      USD: {
        accountNumber: true,
        routingNumber: true,
      },
    },
  };

  const mockRegisterPayeeFormat = {
    result: {
      payout_method: {
        fields: {
          items: [
            { field_name: 'accountNumber', label: 'Account Number' },
            { field_name: 'routingNumber', label: 'Routing Number' },
          ],
        },
      },
    },
  };

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        GetBankAccountDetailsForPayeePayoneerGBT,
        {
          provide: ProgramService,
          useValue: {
            getProgramIdFromGrpcInput: jest.fn().mockResolvedValue('program-id'),
          },
        },
        {
          provide: PayoneerHttpClient,
          useValue: {
            getPayeeExtendedDetails: jest.fn().mockResolvedValue([mockPayeeDetails, null]),
            getRegisterPayeeFormat: jest.fn().mockResolvedValue([mockRegisterPayeeFormat, null]),
          },
        },
        {
          provide: ContractService,
          useValue: {
            getConfigByKey: jest.fn().mockResolvedValue(mockBankConfig),
          },
        },
      ],
    }).compile();

    handler = module.get<GetBankAccountDetailsForPayeePayoneerGBT>(GetBankAccountDetailsForPayeePayoneerGBT);
    programService = module.get<ProgramService>(ProgramService);
    payoneerClient = module.get<PayoneerHttpClient>(PayoneerHttpClient);
    contractService = module.get<ContractService>(ContractService);

    handler = new GetBankAccountDetailsForPayeePayoneerGBT(programService, payoneerClient, contractService);
  });

  describe('initialize', () => {
    it('should initialize handler successfully', async () => {
      await handler.initialize('payee-id');
      expect(programService.getProgramIdFromGrpcInput).toHaveBeenCalledWith(
        null,
        PayoneerProvider.PayoneerGBT,
      );
    });

    it('should throw error when payee details fetch fails', async () => {
      jest
        .spyOn(payoneerClient, 'getPayeeExtendedDetails')
        .mockResolvedValue([null, new Error('Failed') as any]);
      await expect(handler.initialize('payee-id')).rejects.toThrow(CustomError);
    });
  });

  describe('getBankAccountDetails', () => {
    beforeEach(async () => {
      await handler.initialize('payee-id');
    });

    it('should return formatted bank account details', async () => {
      const result = await handler.getBankAccountDetails();
      expect(result).toEqual([
        { name: 'accountNumber', value: '**********' },
        { name: 'routingNumber', value: '*********' },
      ]);
    });

    it('should handle errors and throw RpcException', async () => {
      jest.spyOn(handler, 'transformBankAccountDetails').mockRejectedValue(new Error('Transform failed'));
      await expect(handler.getBankAccountDetails()).rejects.toThrow(RpcException);
    });
  });

  describe('getConfigFields', () => {
    it('should return config fields for specific currency', async () => {
      await handler.initialize('payee-id');
      const fields = handler.getConfigFields();
      expect(fields).toEqual(['accountNumber', 'routingNumber']);
    });

    it('should return default fields when currency specific config not found', async () => {
      const mockConfigWithDefault = {
        values: {
          CC: {
            accountNumber: true,
          },
        },
      };
      jest.spyOn(contractService, 'getConfigByKey').mockResolvedValue(mockConfigWithDefault);
      await handler.initialize('payee-id');
      const fields = handler.getConfigFields();
      expect(fields).toEqual(['accountNumber']);
    });
  });

  describe('transformBankAccountDetails', () => {
    it('should transform bank account details correctly', async () => {
      await handler.initialize('payee-id');
      const result = await handler.transformBankAccountDetails();
      expect(result).toHaveLength(2);
      expect(result[0]).toHaveProperty('name');
      expect(result[0]).toHaveProperty('value');
    });
  });

  describe('setPayeeDetails', () => {
    it('should set payee details successfully', async () => {
      await handler.initialize('payee-id');
      expect(handler['payeeDetails']).toEqual(mockPayeeDetails);
      expect(handler['country']).toEqual(Country.US);
      expect(handler['currency']).toEqual(Currency.USD);
    });

    it('should throw error when payee details are empty', async () => {
      jest.spyOn(payoneerClient, 'getPayeeExtendedDetails').mockResolvedValue([null, null]);
      await expect(handler.setPayeeDetails()).rejects.toThrow(CustomError);
    });
  });

  describe('setBankConfig', () => {
    it('should set bank config successfully', async () => {
      await handler.initialize('payee-id');
      expect(handler['bankConfig']).toEqual(mockBankConfig);
    });

    it('should throw error when bank config not found', async () => {
      jest.spyOn(contractService, 'getConfigByKey').mockResolvedValue(null);
      await expect(handler.setBankConfig()).rejects.toThrow(CustomError);
    });

    it('should use default payee type when type is not provided', async () => {
      const mockPayeeWithoutType = {
        result: {
          payout_method: {
            country: Country.US,
            currency: Currency.USD,
          },
        },
      };
      jest
        .spyOn(payoneerClient, 'getPayeeExtendedDetails')
        .mockResolvedValue([mockPayeeWithoutType as any, null]);
      await handler.initialize('payee-id');
      expect(contractService.getConfigByKey).toHaveBeenCalledWith(
        expect.stringMatching(/subtype:INDIVIDUAL/),
        expect.stringMatching(/BankInfo/i),
      );
    });
  });

  describe('getAndSetRegisterPayeeInput', () => {
    it('should set register payee input successfully', async () => {
      await handler.initialize('payee-id');
      expect(handler['payeeBankAccountDetailsFormat']).toEqual(mockRegisterPayeeFormat);
    });

    it('should throw error when register payee format fetch fails', async () => {
      jest
        .spyOn(payoneerClient, 'getRegisterPayeeFormat')
        .mockResolvedValue([null, new Error('Failed') as any]);
      await expect(handler.getAndSetRegisterPayeeInput()).rejects.toThrow(CustomError);
    });

    it('should handle transformation errors', async () => {
      jest.spyOn(payoneerClient, 'getRegisterPayeeFormat').mockRejectedValue(new Error('Transform failed'));
      await expect(handler.getAndSetRegisterPayeeInput()).rejects.toThrow(CustomError);
    });
  });

  describe('handleError', () => {
    it('should throw RpcException with error message', () => {
      const error = new Error('Test error');
      expect(() => handler.handleError(error)).toThrow(RpcException);
      expect(() => handler.handleError(error)).toThrow('Test error');
    });

    it('should throw RpcException with custom error info message', () => {
      const error = {
        errorInfo: {
          message: 'Custom error message',
        },
      };
      expect(() => handler.handleError(error)).toThrow(RpcException);
      expect(() => handler.handleError(error)).toThrow('Custom error message');
    });

    it('should throw RpcException with default message when no error details', () => {
      expect(() => handler.handleError({})).toThrow(RpcException);
      expect(() => handler.handleError({})).toThrow('Something went wrong');
    });
  });

  describe('handleError', () => {
    it('should throw RpcException with error message', () => {
      const error = new Error('Test error');
      expect(() => handler.handleError(error)).toThrow(RpcException);
      expect(() => handler.handleError(error)).toThrow('Test error');
    });

    it('should throw RpcException with custom error info message', () => {
      const error = {
        errorInfo: {
          message: 'Custom error message',
        },
      };
      expect(() => handler.handleError(error)).toThrow(RpcException);
      expect(() => handler.handleError(error)).toThrow('Custom error message');
    });

    it('should throw RpcException with default message when no error details', () => {
      expect(() => handler.handleError({})).toThrow(RpcException);
      expect(() => handler.handleError({})).toThrow('Something went wrong');
    });
  });

  describe('edge cases and validation', () => {
    it('should handle empty bank_field_details array', async () => {
      const mockEmptyDetails = {
        result: {
          type: PayeeType.Individual,
          payout_method: {
            country: Country.US,
            currency: Currency.USD,
            bank_field_details: [] as any[],
          },
        },
      };

      jest
        .spyOn(payoneerClient, 'getPayeeExtendedDetails')
        .mockResolvedValue([mockEmptyDetails as any, null]);

      await handler.initialize('payee-id');
      const result = await handler.getBankAccountDetails();
      expect(result).toEqual([]);
    });

    it('should handle missing payout_method in payee details', async () => {
      const mockInvalidDetails = {
        result: {
          type: PayeeType.Individual,
        },
      };

      jest
        .spyOn(payoneerClient, 'getPayeeExtendedDetails')
        .mockResolvedValue([mockInvalidDetails as any, null]);

      await handler.initialize('payee-id');
      const result = await handler.transformBankAccountDetails();
      expect(result).toEqual([]);
    });

    it('should handle missing fields items in register payee format', async () => {
      const mockInvalidFormat = {
        result: {
          payout_method: {
            fields: {},
          },
        },
      };

      jest
        .spyOn(payoneerClient, 'getRegisterPayeeFormat')
        .mockResolvedValue([mockInvalidFormat as any, null]);

      await handler.initialize('payee-id');
      const result = await handler.transformBankAccountDetails();
      expect(result).toHaveLength(2);
    });

    it('should handle empty bank config values', async () => {
      jest.spyOn(contractService, 'getConfigByKey').mockResolvedValue({ values: {} });

      await handler.initialize('payee-id');
      const fields = handler.getConfigFields();
      expect(fields).toEqual([]);
    });

    it('should handle missing currency and default config', async () => {
      jest.spyOn(contractService, 'getConfigByKey').mockResolvedValue({ values: {} });

      await handler.initialize('payee-id');
      const fields = handler.getConfigFields();
      expect(fields).toEqual([]);
    });
  });
});

import { Injectable, Logger } from '@nestjs/common';
import { ProgramService } from '../../programs/services/program.service';
import { PayoneerHttpClient } from '../../common/external-service/payoneer/services/payoneer.http.client.service';
import { AbstractBeneficiaryService } from './abstract-services/abstract-beneficiary.service';
import { payoneer } from '@skuad/proto-utils/dist/interface/proto/payoneer/payoneer';
import { RpcException } from '@nestjs/microservices';
import { AppConfigService } from '../../config/services/app.config.service';
import { CreateBeneficiaryForPayoneerHandler } from './handlers/create-beneficiary-payoneer-handler.service';
import { convertToCamelCase } from '../../common/helpers/utils';

@Injectable()
export class PayoneerBeneficiaryService extends AbstractBeneficiaryService {
  private readonly logger = new Logger(PayoneerBeneficiaryService.name);
  constructor(
    private readonly programService: ProgramService,
    private readonly payoneerClient: PayoneerHttpClient,
    private readonly config: AppConfigService,
  ) {
    super();
  }

  async createBeneficiary(
    createBeneficiaryGrpcInput: payoneer.CreateBeneficiaryInput,
    programId?: string,
  ): Promise<payoneer.CreateBeneficiaryOutput> {
    try {
      const createBeneficiaryHandler = new CreateBeneficiaryForPayoneerHandler(
        this.programService,
        this.payoneerClient,
        this.config,
      );

      await createBeneficiaryHandler.initialize(createBeneficiaryGrpcInput, programId);

      return await createBeneficiaryHandler.create();
    } catch (error) {
      this.logger.error(
        JSON.stringify({
          message: 'Failed to create beneficiary',
          error,
        }),
      );
      throw new RpcException(error.message);
    }
  }

  async getPayeeBankAccountDetailsFormat(
    input: payoneer.GetBankFieldsForPayeeInput,
  ): Promise<payoneer.GetBankFieldsForPayeeOutput> {
    const [result, error] = await this.payoneerClient.getBankFieldsForPayee(input, input.programId);
    if (error) {
      throw new RpcException(error);
    }

    return convertToCamelCase(result);
  }

  async getBankAccountDetailsForPayee(payeeId: string): Promise<
    Array<{
      name: string;
      value: string;
    }>
  > {
    return [];
  }
}

import { Injectable, Logger } from '@nestjs/common';
import { ProgramService } from '../../programs/services/program.service';
import { PayoneerHttpClient } from '../../common/external-service/payoneer/services/payoneer.http.client.service';
import { AbstractBeneficiaryService } from './abstract-services/abstract-beneficiary.service';
import { payoneer } from '@skuad/proto-utils/dist/interface/proto/payoneer/payoneer';
import { CreateBeneficiaryForPayoneerGBTHandler } from './handlers/create-beneficiary-payoneer-gbt-handler.service';
import { BeneficiaryGrpcClient } from '../../common/external-service/payments/services/beneficiary-grpc-client.service';
import { PayeeType, PayoneerProvider } from '../../common/constants/enums';
import { RegisterPayeeFormatOutput } from '../../common/data-types/payoneer-v4.data.types';
import { RpcException } from '@nestjs/microservices';
import { GetBankAccountDetailsForPayeePayoneerGBT } from './handlers/get-bank-account-details-for-payee-payoneer-gbt.handler';
import { ContractService } from '../../common/external-service/contract-service/services/contract-service-gql.service';

@Injectable()
export class PayoneerGbtBeneficiaryService extends AbstractBeneficiaryService {
  private readonly logger = new Logger(PayoneerGbtBeneficiaryService.name);
  constructor(
    private readonly programService: ProgramService,
    private readonly payoneerClient: PayoneerHttpClient,
    private readonly beneficiaryRpcClient: BeneficiaryGrpcClient,
    private readonly contractService: ContractService,
  ) {
    super();
  }

  async createBeneficiary(
    createBeneficiaryGrpcInput: payoneer.CreateBeneficiaryForPayoneerGBTProgramInput,
  ): Promise<payoneer.CreateBeneficiaryForPayoneerGBTProgramOutput> {
    try {
      const createBeneficiaryHandler = new CreateBeneficiaryForPayoneerGBTHandler(
        this.programService,
        this.payoneerClient,
        this.beneficiaryRpcClient,
      );

      await createBeneficiaryHandler.initialize(createBeneficiaryGrpcInput);

      return await createBeneficiaryHandler.sync();
    } catch (error) {
      this.logger.error(
        JSON.stringify({
          message: 'Failed to create beneficiary',
          error,
        }),
      );
      throw error;
    }
  }

  async getBankAccountDetailsForPayee(payeeId: string): Promise<
    Array<{
      name: string;
      value: string;
    }>
  > {
    const getBankAccountDetailsHandler = new GetBankAccountDetailsForPayeePayoneerGBT(
      this.programService,
      this.payoneerClient,
      this.contractService,
    );

    await getBankAccountDetailsHandler.initialize(payeeId);

    const response = (await getBankAccountDetailsHandler.getBankAccountDetails()) ?? [];

    return response;
  }

  async getPayeeBankAccountDetailsFormat(
    input: payoneer.GetBankFieldsForPayeeInput,
  ): Promise<RegisterPayeeFormatOutput> {
    if (!input) {
      throw new RpcException('Input parameters are required');
    }

    const { payeeType, country, currency } = input as unknown as {
      payeeType: PayeeType;
      country: string;
      currency: string;
    };

    if (!payeeType || !country || !currency) {
      throw new RpcException('payeeType, country, and currency are required fields');
    }

    try {
      const programId = await this.programService.getProgramIdFromGrpcInput(
        {
          currency,
        },
        PayoneerProvider.PayoneerGBT,
      );

      const [result, error] = await this.payoneerClient.getRegisterPayeeFormat(
        programId,
        payeeType,
        country,
        currency,
      );

      if (error) {
        this.logger.error(
          JSON.stringify({
            errorMessage: 'Failed to get register payee format from Payoneer',
            error,
            input,
          }),
        );
        throw error;
      }

      return result;
    } catch (error) {
      this.logger.error(
        JSON.stringify({
          errorMessage: 'Failed to get payee bank account details format',
          error,
          input,
        }),
      );
      throw new RpcException(error?.message ?? 'Failed to get payee bank account details format');
    }
  }
}

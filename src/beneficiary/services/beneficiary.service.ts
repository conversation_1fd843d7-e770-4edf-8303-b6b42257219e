import { v4 as uuidv4 } from 'uuid';

import { InternalServerErrorException, Injectable, Logger, BadRequestException } from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';
import { PayoneerHttpClient } from '../../common/external-service/payoneer/services/payoneer.http.client.service';
import { payoneer } from '@skuad/proto-utils/dist/interface/proto/payoneer/payoneer';
import { AppConfigService } from '../../config/services/app.config.service';
import { PayoneerProvider } from '../../common/constants/enums';
import { BeneficiaryGrpcClient } from '../../common/external-service/payments/services/beneficiary-grpc-client.service';
import { AbstractBeneficiaryService } from './abstract-services/abstract-beneficiary.service';
import { PayoneerBeneficiaryService } from './payoneer-beneficiary.service';
import { PayoneerGbtBeneficiaryService } from './payoneer-gbt-beneficiary.service';
import { ProgramService } from '../../programs/services/program.service';
import { RegisterPayeeFormatOutput } from '../../common/data-types/payoneer-v4.data.types';
import { ContractService } from '../../common/external-service/contract-service/services/contract-service-gql.service';
import {
  BeneficiaryRegistrationPurpose,
  CreateRegistrationLinkInput,
  GetPayeeDetailsInput,
  LockType,
} from '../dtos/inputs/beneficiary.graphql.input';
import { ProgramTokenService } from '../../payin/service/program-token.service';
import { convertToCamelCase } from '../../common/helpers/utils';
import { GetPayeeDetailsOutput } from '../dtos/inputs/beneficiary.graphql.output';

export enum ServiceMethods {
  CreateOne = 'createOne',
  UpdateOne = 'updateOne',
  UpdateProviderResponse = 'updateProviderResponse',
  CreateOrUpdateUser = 'createOrUpdateUser',
  AddAndUpdateBankAsPrimary = 'addAndUpdateBankAsPrimary',
  AddNewBank = 'addNewBank',
  FetchBankAccountDetails = 'fetchBankAccountDetails',
}

@Injectable()
export class BeneficiaryService {
  private readonly logger = new Logger(BeneficiaryService.name);
  readonly service = BeneficiaryService.name;

  private payoneerProvider: { [key: string]: AbstractBeneficiaryService } = {};
  constructor(
    private readonly programService: ProgramService,
    private readonly payoneerClient: PayoneerHttpClient,
    private readonly config: AppConfigService,
    private readonly beneficiaryRpcClient: BeneficiaryGrpcClient,
    private readonly contractService: ContractService,
    private readonly programTokenService: ProgramTokenService,
  ) {}

  private initializeProviders(payoneerProvider: PayoneerProvider): AbstractBeneficiaryService {
    if (Object.keys(this.payoneerProvider).indexOf(payoneerProvider) == -1) {
      if (payoneerProvider === PayoneerProvider.Payoneer) {
        this.payoneerProvider[payoneerProvider] = new PayoneerBeneficiaryService(
          this.programService,
          this.payoneerClient,
          this.config,
        );
      } else if (payoneerProvider === PayoneerProvider.PayoneerGBT) {
        this.payoneerProvider[payoneerProvider] = new PayoneerGbtBeneficiaryService(
          this.programService,
          this.payoneerClient,
          this.beneficiaryRpcClient,
          this.contractService,
        );
      } else {
        throw new Error('Unidentified provider!!!');
      }
    }
    return this.payoneerProvider[payoneerProvider];
  }

  // ========================================== Payoneer APIs ==========================================

  async createRegistrationLinkV4(
    createBeneficiaryGrpcInput: payoneer.CreateBeneficiaryInput,
  ): Promise<payoneer.CreateBeneficiaryOutput> {
    try {
      return await this.initializeProviders(PayoneerProvider.Payoneer).createBeneficiary(
        createBeneficiaryGrpcInput,
      );
    } catch (error) {
      this.logger.error({ message: error?.message, stack: error?.stack });
      throw new RpcException(error.message);
    }
  }

  async getBankFieldsForPayee(
    input: payoneer.GetBankFieldsForPayeeInput,
  ): Promise<payoneer.GetBankFieldsForPayeeOutput> {
    return (await this.initializeProviders(PayoneerProvider.Payoneer).getPayeeBankAccountDetailsFormat(
      input,
    )) as payoneer.GetBankFieldsForPayeeOutput;
  }

  // ========================================== Payoneer GBT APIs ==========================================

  async createBeneficiaryForPayoneerGBTProgram(
    createBeneficiaryInput: payoneer.CreateBeneficiaryForPayoneerGBTProgramInput,
  ): Promise<[payoneer.CreateBeneficiaryForPayoneerGBTProgramOutput, Error]> {
    try {
      const response = await this.initializeProviders(PayoneerProvider.PayoneerGBT).createBeneficiary(
        createBeneficiaryInput,
      );

      return [
        {
          beneficiaryId: response?.beneficiaryId,
        },
        null,
      ];
    } catch (error) {
      this.logger.error(
        JSON.stringify({
          message: 'Error syncing beneficiary',
          error: error?.message,
          stack: error?.stack,
        }),
      );
      return [null, new RpcException(error?.message)];
    }
  }

  async getBankFieldsForPayeeForPayoneerGBT(
    input: payoneer.GetBankFieldsForPayeeInput,
  ): Promise<payoneer.GetBankFieldsForPayeeOutput | RegisterPayeeFormatOutput> {
    try {
      return await this.initializeProviders(PayoneerProvider.PayoneerGBT).getPayeeBankAccountDetailsFormat(
        input,
      );
    } catch (error) {
      this.logger.error(
        JSON.stringify({
          message: 'Error fetching bank account details',
          error: error?.message,
          stack: error?.stack,
        }),
      );
      throw new RpcException(error);
    }
  }

  async getPayeeBankAccountDetails(payeeId: string): Promise<
    Array<{
      name: string;
      value: string;
    }>
  > {
    try {
      return await this.initializeProviders(PayoneerProvider.PayoneerGBT).getBankAccountDetailsForPayee(
        payeeId,
      );
    } catch (error) {
      this.logger.error(
        JSON.stringify({
          message: 'Error fetching bank account details',
          error: error?.message,
          stack: error?.stack,
        }),
      );
      throw new RpcException(error);
    }
  }

  async createBeneficiaryRegistrationLink(
    input: CreateRegistrationLinkInput,
    programId?: string,
  ): Promise<payoneer.CreateBeneficiaryOutput> {
    const providerService = this.initializeProviders(PayoneerProvider.Payoneer);
    const createBeneficiaryGrpcInput: payoneer.CreateBeneficiaryInput = {
      payeeId: input.payeeId,
      clientSessionId: input.clientSessionId ?? uuidv4(),
      lockType: input.lockType ?? LockType.None,
      redirectUrl: input.redirectUrl,
    };

    if (input.payee) {
      createBeneficiaryGrpcInput.payee = {
        type: input.payee.type as unknown as payoneer.PayeeType,
        contact: input.payee.contact,
        address: input.payee.address
          ? {
              country: input.payee.address.country as unknown as payoneer.Country,
              state: input.payee.address.state,
              addressLine_1: input.payee.address.addressLine1,
              addressLine_2: input.payee.address.addressLine2,
              city: input.payee.address.city,
              zipCode: input.payee.address.zipCode,
            }
          : undefined,
        company: input.payee.company
          ? {
              legalType: input.payee.company.legalType as unknown as payoneer.LegalType,
              name: input.payee.company.name,
              url: input.payee.company.url,
              incorporatedCountry: input.payee.company.incorporatedCountry as unknown as payoneer.Country,
              incorporatedState: input.payee.company.incorporatedState,
            }
          : undefined,
      };
    }

    if (input.purpose == BeneficiaryRegistrationPurpose.SSO) {
      createBeneficiaryGrpcInput.redirectUrl = this.programTokenService.createAuthorizationLink(
        input.redirectUrl,
        input.purpose,
        input.state,
      );
      programId = this.config.getSSOProgramId();
    }

    return await providerService.createBeneficiary(createBeneficiaryGrpcInput, programId);
  }

  async getPayeeDetails(input: GetPayeeDetailsInput): Promise<GetPayeeDetailsOutput> {
    if (input.purpose == BeneficiaryRegistrationPurpose.SSO) {
      input.programId = this.config.getSSOProgramId();
    }

    if (!input.programId) {
      throw new BadRequestException('Program id is missing');
    }

    const [payee, error] = await this.payoneerClient.getPayeeExtendedDetails(
      input.programId,
      input.payeeId,
      input.purpose,
    );
    if (error) {
      this.logger.error({ message: 'Got error in getting payee details', error });
      throw new InternalServerErrorException(error?.errors.error_description);
    }
    return convertToCamelCase(payee.result) as GetPayeeDetailsOutput;
  }
}

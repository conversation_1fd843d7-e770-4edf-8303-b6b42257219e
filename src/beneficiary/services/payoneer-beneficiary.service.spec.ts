import { Test, TestingModule } from '@nestjs/testing';
import { PayoneerBeneficiaryService } from './payoneer-beneficiary.service';
import { ProgramService } from '../../programs/services/program.service';
import { PayoneerHttpClient } from '../../common/external-service/payoneer/services/payoneer.http.client.service';
import { AppConfigService } from '../../config/services/app.config.service';
import { RpcException } from '@nestjs/microservices';

const mockInitialize = jest.fn();
const mockCreate = jest.fn();

// Mock the handler globally once (important)
jest.mock('./handlers/create-beneficiary-payoneer-handler.service', () => {
  return {
    CreateBeneficiaryForPayoneerHandler: jest.fn().mockImplementation(() => ({
      initialize: mockInitialize,
      create: mockCreate,
    })),
  };
});

describe('PayoneerBeneficiaryService', () => {
  let service: PayoneerBeneficiaryService;
  let payoneerClient: PayoneerHttpClient;

  beforeEach(async () => {
    mockInitialize.mockReset();
    mockCreate.mockReset();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PayoneerBeneficiaryService,
        { provide: ProgramService, useValue: {} },
        {
          provide: PayoneerHttpClient,
          useValue: {
            getBankFieldsForPayee: jest.fn(),
          },
        },
        { provide: AppConfigService, useValue: {} },
      ],
    }).compile();

    service = module.get<PayoneerBeneficiaryService>(PayoneerBeneficiaryService);
    payoneerClient = module.get<PayoneerHttpClient>(PayoneerHttpClient);
  });

  describe('createBeneficiary', () => {
    it('should call create on handler and return result', async () => {
      mockInitialize.mockResolvedValue(undefined);
      mockCreate.mockResolvedValue({ success: true });

      const result = await service.createBeneficiary({} as any, 'program-id');
      expect(result).toEqual({ success: true });
    });

    it('should throw RpcException on error', async () => {
      mockInitialize.mockResolvedValue(undefined);
      mockCreate.mockRejectedValue(new Error('Create failed'));

      await expect(service.createBeneficiary({} as any, 'program-id')).rejects.toThrow(RpcException);
    });
  });

  describe('getPayeeBankAccountDetailsFormat', () => {
    it('should return camel case converted result', async () => {
      const result = { some_field: 'value' };
      (payoneerClient.getBankFieldsForPayee as jest.Mock).mockResolvedValue([result, null]);

      const output = await service.getPayeeBankAccountDetailsFormat({
        programId: 'program-id',
      } as any);

      expect(output).toEqual(expect.anything()); // Customize if needed
    });

    it('should throw error if client returns error', async () => {
      const error = new Error('Client error');
      (payoneerClient.getBankFieldsForPayee as jest.Mock).mockResolvedValue([null, error]);

      await expect(
        service.getPayeeBankAccountDetailsFormat({ programId: 'program-id' } as any),
      ).rejects.toThrow(error);
    });
  });

  describe('getBankAccountDetailsForPayee', () => {
    it('should return empty array', async () => {
      const result = await service.getBankAccountDetailsForPayee('payee-id');
      expect(result).toEqual([]);
    });
  });
});

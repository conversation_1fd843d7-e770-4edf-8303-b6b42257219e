import { Args, Mutation, Resolver } from '@nestjs/graphql';
import { BeneficiaryService } from '../services/beneficiary.service';
import { CreateRegistrationLinkOutput } from '../dtos/outputs/beneficiary.graphql.output';
import { CreateRegistrationLinkInput } from '../dtos/inputs/beneficiary.graphql.input';

@Resolver(() => CreateRegistrationLinkOutput)
export class BeneficiaryMutationResolver {
  constructor(private readonly beneficiaryService: BeneficiaryService) {}

  @Mutation(() => CreateRegistrationLinkOutput, {
    name: 'createRegistrationLink',
    description: 'create registration link',
  })
  async createRegistrationLink(
    @Args('input', { type: () => CreateRegistrationLinkInput }) input: CreateRegistrationLinkInput,
  ): Promise<CreateRegistrationLinkOutput> {
    const data = await this.beneficiaryService.createBeneficiaryRegistrationLink(input);
    return {
      token: data.token,
      registrationLink: data.beneficiaryRegisterLink,
    };
  }
}

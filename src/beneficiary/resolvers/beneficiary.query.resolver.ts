import { Args, Resolver, Query } from '@nestjs/graphql';
import { BeneficiaryService } from '../services/beneficiary.service';
import { GetPayeeDetailsOutput } from '../dtos/inputs/beneficiary.graphql.output';
import { GetPayeeDetailsInput } from '../dtos/inputs/beneficiary.graphql.input';

@Resolver()
export class BeneficiaryQueryResolver {
  constructor(private readonly beneficiaryService: BeneficiaryService) {}

  @Query(() => GetPayeeDetailsOutput, {
    name: 'getPayeeDetails',
    description: 'get payee details',
  })
  async getPayeeDetails(
    @Args('input', { type: () => GetPayeeDetailsInput }) input: GetPayeeDetailsInput,
  ): Promise<GetPayeeDetailsOutput> {
    const result = await this.beneficiaryService.getPayeeDetails(input);
    return result;
  }
}

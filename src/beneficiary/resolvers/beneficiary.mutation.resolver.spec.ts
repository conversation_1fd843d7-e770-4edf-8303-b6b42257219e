import { Test, TestingModule } from '@nestjs/testing';
import { BeneficiaryMutationResolver } from './beneficiary.mutation.resolver';
import { BeneficiaryService } from '../services/beneficiary.service';
import { BeneficiaryRegistrationPurpose, CreateRegistrationLinkInput } from '../dtos/inputs/beneficiary.graphql.input';
import { CreateRegistrationLinkOutput } from '../dtos/outputs/beneficiary.graphql.output';

describe('BeneficiaryMutationResolver', () => {
  let resolver: BeneficiaryMutationResolver;
  let beneficiaryService: BeneficiaryService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BeneficiaryMutationResolver,
        {
          provide: BeneficiaryService,
          useValue: {
            createBeneficiaryRegistrationLink: jest.fn(),
          },
        },
      ],
    }).compile();

    resolver = module.get<BeneficiaryMutationResolver>(BeneficiaryMutationResolver);
    beneficiaryService = module.get<BeneficiaryService>(BeneficiaryService);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });

  describe('createRegistrationLink', () => {
    it('should return registration link and token', async () => {
      const input: CreateRegistrationLinkInput = {
        redirectUrl: 'https://example.com',
        purpose: BeneficiaryRegistrationPurpose.SSO,
        state: 'abc123',
        payeeId: '123',
      };

      const mockServiceResponse = {
        token: 'mockToken123',
        beneficiaryRegisterLink: 'https://register-link.com',
      };

      (beneficiaryService.createBeneficiaryRegistrationLink as jest.Mock).mockResolvedValue(
        mockServiceResponse,
      );

      const result: CreateRegistrationLinkOutput = await resolver.createRegistrationLink(input);

      expect(beneficiaryService.createBeneficiaryRegistrationLink).toHaveBeenCalledWith(input);
      expect(result).toEqual({
        token: mockServiceResponse.token,
        registrationLink: mockServiceResponse.beneficiaryRegisterLink,
      });
    });

    it('should throw if service throws', async () => {
      const input: CreateRegistrationLinkInput = {
        redirectUrl: 'https://example.com',
        state: 'abc123',
        purpose: BeneficiaryRegistrationPurpose.SSO,
        payeeId: '123',
      };

      const error = new Error('Service failure');
      (beneficiaryService.createBeneficiaryRegistrationLink as jest.Mock).mockRejectedValue(error);

      await expect(resolver.createRegistrationLink(input)).rejects.toThrow('Service failure');
    });
  });
});

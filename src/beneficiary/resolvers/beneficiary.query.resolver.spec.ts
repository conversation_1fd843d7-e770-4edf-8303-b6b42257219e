import { Test, TestingModule } from '@nestjs/testing';
import { BeneficiaryQueryResolver } from './beneficiary.query.resolver';
import { BeneficiaryService } from '../services/beneficiary.service';
import {
  BeneficiaryRegistrationPurpose,
  GetPayeeDetailsInput,
  PayeeType,
} from '../dtos/inputs/beneficiary.graphql.input';
import { GetPayeeDetailsOutput } from '../dtos/inputs/beneficiary.graphql.output';

describe('BeneficiaryQueryResolver', () => {
  let resolver: BeneficiaryQueryResolver;
  let beneficiaryService: BeneficiaryService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BeneficiaryQueryResolver,
        {
          provide: BeneficiaryService,
          useValue: {
            getPayeeDetails: jest.fn(),
          },
        },
      ],
    }).compile();

    resolver = module.get<BeneficiaryQueryResolver>(BeneficiaryQueryResolver);
    beneficiaryService = module.get<BeneficiaryService>(BeneficiaryService);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });

  describe('getPayeeDetails', () => {
    it('should return payee details', async () => {
      const input: GetPayeeDetailsInput = {
        payeeId: '12345',
        purpose: BeneficiaryRegistrationPurpose.SSO,
        programId: 'program123',
      };

      const mockOutput: GetPayeeDetailsOutput = {
        accountId: '12345',
        type: PayeeType.INDIVIDUAL,
        contact: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          mobile: '**********',
          dateOfBirth: '2000-01-01',
        },
      };

      (beneficiaryService.getPayeeDetails as jest.Mock).mockResolvedValue(mockOutput);

      const result = await resolver.getPayeeDetails(input);

      expect(result).toEqual(mockOutput);
      expect(beneficiaryService.getPayeeDetails).toHaveBeenCalledWith(input);
      expect(beneficiaryService.getPayeeDetails).toHaveBeenCalledTimes(1);
    });

    it('should throw if service throws', async () => {
      const input: GetPayeeDetailsInput = {
        payeeId: '12345',
        purpose: BeneficiaryRegistrationPurpose.SSO,
        programId: 'program123',
      };

      (beneficiaryService.getPayeeDetails as jest.Mock).mockRejectedValue(new Error('Not found'));

      await expect(resolver.getPayeeDetails(input)).rejects.toThrow('Not found');
      expect(beneficiaryService.getPayeeDetails).toHaveBeenCalledWith(input);
    });
  });
});

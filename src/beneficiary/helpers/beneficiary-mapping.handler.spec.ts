import { PayoneerGBTMapper } from './beneficiary-mapping.handler';
import { PayoneerBankFieldNames } from '../../common/constants/enums';

describe('PayoneerGBTMapper', () => {
  let mapper: PayoneerGBTMapper;

  beforeEach(() => {
    mapper = new PayoneerGBTMapper();
  });

  it('should have a valid fieldMapping object', () => {
    expect(mapper).toHaveProperty('fieldMapping');
    Object.keys(mapper['fieldMapping']).forEach((key) => {
      expect(Array.isArray(mapper['fieldMapping'][key as PayoneerBankFieldNames])).toBe(true);
    });
  });

  describe('mapFields', () => {
    it('should map existing fields correctly', () => {
      const source = {
        accountHolder: '<PERSON>',
        accountNumber: '12345',
        bankName: 'Test Bank',
        swift: 'TESTSWFT',
      };
      const targetKeys = [
        PayoneerBankFieldNames.AccountName,
        PayoneerBankFieldNames.AccountNumber,
        PayoneerBankFieldNames.BankName,
        PayoneerBankFieldNames.Swift,
      ];
      const result = mapper.mapFields(source, targetKeys);
      expect(result).toEqual([
        { name: PayoneerBankFieldNames.AccountName, value: 'John Doe' },
        { name: PayoneerBankFieldNames.AccountNumber, value: '12345' },
        { name: PayoneerBankFieldNames.BankName, value: 'Test Bank' },
        { name: PayoneerBankFieldNames.Swift, value: 'TESTSWFT' },
      ]);
    });

    it('should skip fields that do not exist in source', () => {
      const source = { accountHolder: 'John Doe' };
      const targetKeys = [PayoneerBankFieldNames.State];
      const result = mapper.mapFields(source, targetKeys);
      expect(result).toHaveLength(0);
    });

    it('should return empty array if no targetKeys are provided', () => {
      const source = { accountHolder: 'John Doe' };
      const result = mapper.mapFields(source, []);
      expect(result).toHaveLength(0);
    });

    it('should handle null or undefined source values', () => {
      const source = { accountHolder: '', accountNumber: '' };
      const targetKeys = [PayoneerBankFieldNames.AccountName, PayoneerBankFieldNames.AccountNumber];
      const result = mapper.mapFields(source, targetKeys);
      expect(result).toHaveLength(0);
    });
  });

  describe('reverseMapFields', () => {
    it('should reverse map fields correctly', () => {
      const target = [
        { name: PayoneerBankFieldNames.AccountName, value: 'John Doe' },
        { name: PayoneerBankFieldNames.BankName, value: 'My Bank' },
      ];
      const reversed = mapper.reverseMapFields(target);
      expect(reversed).toEqual({
        accountHolder: 'John Doe',
        accountHolderName: 'John Doe',
        bankName: 'My Bank',
        name: 'My Bank',
      });
    });

    it('should skip unknown field names', () => {
      const target = [
        { name: 'UNKNOWN_FIELD', value: 'Some Value' },
        { name: PayoneerBankFieldNames.Suffix, value: 'SFX' },
      ];
      const reversed = mapper.reverseMapFields(target);
      expect(reversed).toEqual({ suffix: 'SFX' });
    });

    it('should handle empty target array', () => {
      const reversed = mapper.reverseMapFields([]);
      expect(reversed).toEqual({});
    });

    it('should only use first key in the array when reversing', () => {
      const target = [{ name: PayoneerBankFieldNames.BIC, value: 'TestBIC' }];
      const reversed = mapper.reverseMapFields(target);
      expect(reversed).toEqual({
        bic: 'TestBIC',
        bicSwift: 'TestBIC',
        swift: 'TestBIC',
      });
    });
  });
});

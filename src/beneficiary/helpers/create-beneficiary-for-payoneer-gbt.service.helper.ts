import { CreateBeneficiaryInputForPayoneerGBT } from '../dtos/inputs/create-beneficiary-payoneer-gbt.input';
import { GrpcHttpResponse } from '../../common/data-types/common.data-type';
import {
  BeneficiaryUpdateParamsDto,
  CreateBeneficiaryRequestProtoDto,
  UpdateBeneficiaryRequestProtoDto,
} from '@skuad/proto-utils/dist/payments/beneficiary/types/types';
import {
  AccountType,
  BankAccountTypeForPayoneerGBT,
  BankRoutingCode,
  Country,
  EntityType,
  LegalType,
  PayeeType,
  PayoneerBankFieldNames,
  PayoneerBankFieldAccountTypeOptionsMapping,
  PayoneerProvider,
  PayoutMethodType,
  RoutingCode,
} from '../../common/constants/enums';
import {
  EditPayeeProfileInput,
  EditPayeeResponse,
  GetPayeeExtendedDetailsResponse,
  PayeeInput,
  RegisterPayeeFormatOutput,
  RegisterPayeeInput,
  RegisterPayeePayoutMethod,
} from '../../common/data-types/payoneer-v4.data.types';
import { areObjectsEqual, removeEmptyFromJSON } from '../../common/helpers/utils';
import { HttpStatus } from '@nestjs/common';
import { PayoneerGBTMapper } from './beneficiary-mapping.handler';

export class CreateBeneficiaryForPayoneerGBTServiceHelper {
  private readonly createBeneficiaryReq: CreateBeneficiaryInputForPayoneerGBT;
  private readonly createBeneficiaryProviderResp: EditPayeeResponse;
  private fetchBeneficiaryFromProvider: GetPayeeExtendedDetailsResponse;

  private readonly programId: string;

  constructor(createBeneficiaryInput: CreateBeneficiaryInputForPayoneerGBT, programId: string) {
    this.createBeneficiaryReq = createBeneficiaryInput;
    this.programId = programId;
  }

  setPayeeInfo(payeeInfo: GetPayeeExtendedDetailsResponse): void {
    this.fetchBeneficiaryFromProvider = payeeInfo;
  }

  getPaymentProvider = () => {
    return PayoneerProvider.PayoneerGBT;
  };

  getBankAccountHashId(): string {
    return this.createBeneficiaryReq.client_config.bank_account_hash_id;
  }

  getCreateBeneficiaryRpcReq(): CreateBeneficiaryRequestProtoDto {
    return {
      clientLegalEntityId: this.createBeneficiaryReq.client_entity_id,
      beneficiaryLegalEntityId: this.createBeneficiaryReq.entity_id,
      payoutCurrency: this.createBeneficiaryReq.currency,
      beneficiaryRole: this.createBeneficiaryReq.role,
      contractId: this.createBeneficiaryReq.contract_id,
      provider: this.getPaymentProvider(),
      entityType: this.createBeneficiaryReq?.entity_type?.toLowerCase() as EntityType,
      bankCountry: this.createBeneficiaryReq.bank_country,
      bankAccountHashId: this.getBankAccountHashId(),
      meta: {
        ...(this.createBeneficiaryReq?.meta ?? {}),
        programId: this.programId,
      },
    };
  }

  isProviderError(): boolean {
    const providerResp = this.createBeneficiaryProviderResp;
    return !!providerResp && !!providerResp.error;
  }

  getUpdateBeneficiaryRpcReq(): UpdateBeneficiaryRequestProtoDto {
    return {
      queryParams: {
        clientLegalEntityId: this.createBeneficiaryReq.client_entity_id,
        beneficiaryLegalEntityId: this.createBeneficiaryReq.entity_id,
        bankAccountHashId: this.createBeneficiaryReq.client_config.bank_account_hash_id,
      },
      updateParams: this.isProviderError()
        ? this.updateBeneficiaryErrorReq()
        : this.updateBeneficiaryMarkAsActiveReq(),
    };
  }

  isProfileAlreadyCreated() {
    return !!this.createBeneficiaryReq?.providerBeneficiaryId;
  }

  updateBeneficiaryErrorReq(): BeneficiaryUpdateParamsDto {
    return !this.isProfileAlreadyCreated()
      ? {
          default: false,
          active: false,
          providerPayoutId: this.createBeneficiaryReq.providerBeneficiaryId,
          providerBeneficiaryId: this.createBeneficiaryReq.providerBeneficiaryId,
          error: JSON.stringify(this.createBeneficiaryProviderResp.error),
        }
      : {
          meta: {
            ...this.createBeneficiaryReq.meta,
            isProfileAlreadyCreated: this.isProfileAlreadyCreated(),
          },
          error: JSON.stringify(this.createBeneficiaryProviderResp.error),
        };
  }

  updateBeneficiaryMarkAsActiveReq(): BeneficiaryUpdateParamsDto {
    return removeEmptyFromJSON({
      providerPayoutId: this.createBeneficiaryReq.providerBeneficiaryId,
      providerBeneficiaryId: this.createBeneficiaryReq.providerBeneficiaryId,
      meta: {
        ...(this.createBeneficiaryReq?.meta ?? {}),
        programId: this.programId,
        secondaryCurrency: this.createBeneficiaryReq?.secondaryCurrency ?? undefined,
      },
      swiftChargeType: this.createBeneficiaryReq?.swift_charge_type,
      default: true,
      payoutCurrency: this.createBeneficiaryReq?.currency,
    });
  }

  getProviderBeneficiaryId(): string {
    return this.createBeneficiaryReq?.providerBeneficiaryId;
  }

  isRpcError(resp: GrpcHttpResponse) {
    return resp.statusCode !== HttpStatus.OK || resp.errors;
  }

  private compareCompanyData(
    registerPayeeInput: RegisterPayeeInput,
    payeeInfo: GetPayeeExtendedDetailsResponse,
  ): boolean {
    const inputData = registerPayeeInput?.payee?.company;
    const currentData = payeeInfo?.result?.company;

    return areObjectsEqual(inputData, currentData);
  }

  private compareContactData(
    registerPayeeInput: RegisterPayeeInput,
    payeeInfo: GetPayeeExtendedDetailsResponse,
  ): boolean {
    const inputData = registerPayeeInput?.payee?.contact;
    const currentData = payeeInfo?.result?.contact;

    return areObjectsEqual(inputData, currentData);
  }

  private compareAddressData(
    registerPayeeInput: RegisterPayeeInput,
    payeeInfo: GetPayeeExtendedDetailsResponse,
  ): boolean {
    const inputData = registerPayeeInput?.payee?.address;
    const currentData = payeeInfo?.result?.address;

    return areObjectsEqual(inputData, currentData);
  }

  private compareBankData(
    registerPayeeInput: RegisterPayeeInput,
    payeeInfo: GetPayeeExtendedDetailsResponse,
  ): boolean {
    const inputData = registerPayeeInput?.payout_method?.bank_field_details;
    const currentData = payeeInfo?.result?.payout_method?.bank_field_details;

    if (
      !areObjectsEqual(
        {
          type:
            registerPayeeInput?.payout_method?.type == 'BANK'
              ? 'BankTransfer'
              : registerPayeeInput?.payout_method?.type,
          bank_account_type:
            registerPayeeInput?.payout_method?.bank_account_type == 'PERSONAL'
              ? '1'
              : registerPayeeInput?.payout_method?.bank_account_type,
          country: registerPayeeInput?.payout_method?.country,
          currency: registerPayeeInput?.payout_method?.currency,
        },
        {
          type: payeeInfo?.result?.payout_method?.type,
          bank_account_type: payeeInfo?.result?.payout_method?.bank_account_type,
          country: payeeInfo?.result?.payout_method?.country,
          currency: payeeInfo?.result?.payout_method?.currency,
        },
      )
    ) {
      return true;
    }

    const inputDataObj: { [key: string]: any } = {};
    const currentDataObj: { [key: string]: any } = {};

    for (const input of inputData || []) {
      inputDataObj[input.name] = input.value;
    }

    for (const current of currentData || []) {
      currentDataObj[current.name] = current.value;
    }

    return areObjectsEqual(inputDataObj, currentDataObj);
  }

  isThereAnyMismatchInPayeeDetails(
    registerPayeeInput: RegisterPayeeInput,
    payeeInfo: GetPayeeExtendedDetailsResponse,
  ): {
    isThereAnyMismatchInPersonalDetails: boolean;
    isThereAnyMismatchInBankAccountDetails: boolean;
  } {
    let isThereAnyMismatchInPersonalDetails = false;
    let isThereAnyMismatchInBankAccountDetails = false;

    if (
      this.compareAddressData(registerPayeeInput, payeeInfo) ||
      this.compareCompanyData(registerPayeeInput, payeeInfo) ||
      this.compareContactData(registerPayeeInput, payeeInfo)
    ) {
      isThereAnyMismatchInPersonalDetails = true;
    }

    if (this.compareBankData(registerPayeeInput, payeeInfo)) {
      isThereAnyMismatchInBankAccountDetails = true;
    }

    return {
      isThereAnyMismatchInPersonalDetails,
      isThereAnyMismatchInBankAccountDetails,
    };
  }

  getPayeeType(): PayeeType {
    if (this.createBeneficiaryReq.entity_type?.toUpperCase() === EntityType.Individual?.toUpperCase()) {
      return PayeeType.Individual;
    } else {
      return PayeeType.Company;
    }
  }

  getPayeeInput(): PayeeInput {
    const input = {
      type: this.getPayeeType(),
      contact: {
        first_name: this.createBeneficiaryReq.first_name,
        last_name: this.createBeneficiaryReq.last_name,
        date_of_birth: this.createBeneficiaryReq.date_of_birth?.toISOString()?.split('T')?.[0],
      },
      address: {
        address_line_1: this.createBeneficiaryReq.address,
        address_line_2: this.createBeneficiaryReq.address,
        city: this.createBeneficiaryReq.city,
        state: this.createBeneficiaryReq.state_or_province,
        country: this.createBeneficiaryReq.country == 'GB' ? 'UK' : this.createBeneficiaryReq.country,
        zip_code: this.createBeneficiaryReq.postcode,
      },
      company: {
        name: this.createBeneficiaryReq.company_name,
        legal_type: LegalType.Inc, // Update this on the basis of client contract
        incorporated_country:
          this.createBeneficiaryReq.country == 'GB' ? 'UK' : this.createBeneficiaryReq.country,
        incorporated_state: this.createBeneficiaryReq.state_or_province,
        incorporated_address_1: this.createBeneficiaryReq.address,
        incorporated_address_2: this.createBeneficiaryReq.address,
        incorporated_city: this.createBeneficiaryReq.city,
        incorporated_zipcode: this.createBeneficiaryReq.postcode,
      },
    };

    return input;
  }

  getRoutingCodes(routingCodeType: BankRoutingCode): string {
    for (const routingCode of this.createBeneficiaryReq?.routing_codes || []) {
      if (
        (routingCodeType && routingCode?.type === (routingCodeType as unknown as RoutingCode)) ||
        (!routingCodeType && routingCode?.value)
      ) {
        return routingCode?.value;
      }
    }
  }

  getIdType(): string {
    for (const routingCode of this.createBeneficiaryReq?.routing_codes || []) {
      if (routingCode?.type === RoutingCode.BIN && routingCode?.value) {
        return routingCode?.value;
      } else if (routingCode?.type === RoutingCode.TIN && routingCode?.value) {
        return routingCode?.value;
      }
    }
  }

  getAccountTypeValue(accountType: string): string {
    const value =
      PayoneerBankFieldAccountTypeOptionsMapping[PayoneerBankFieldNames.AccountType]?.[
        `${this.createBeneficiaryReq.bank_country}`
      ]?.[`${accountType?.trim()?.toUpperCase() as AccountType}`]?.value;

    return value || accountType;
  }

  getBankFieldDetails(registerPayeeFormat: RegisterPayeeFormatOutput): Record<string, string>[] {
    const bank_field_details: Array<{
      name: string;
      value: string;
    }> = [];

    const mapping = new PayoneerGBTMapper();

    for (const field of registerPayeeFormat?.result?.payout_method?.fields?.items || []) {
      let value;
      if (PayoneerBankFieldNames.AccountType === field?.field_name) {
        value = this.getAccountTypeValue(this.createBeneficiaryReq.account_type);
      } else {
        value = mapping.mapFields(this.createBeneficiaryReq?.payoutMethodDetails, [
          field?.field_name as PayoneerBankFieldNames,
        ])?.[0]?.value;
      }

      if (!value) {
        continue;
      }

      bank_field_details.push({
        name: field?.field_name,
        value: value,
      });
    }

    return bank_field_details;
  }

  getPayeePayoutMethod(registerPayeeFormat: RegisterPayeeFormatOutput): RegisterPayeePayoutMethod {
    const payoutType = PayoutMethodType.BANK;
    let bankAccountType;
    if (this.createBeneficiaryReq.entity_type === EntityType.Company) {
      bankAccountType = BankAccountTypeForPayoneerGBT.Company;
    } else {
      bankAccountType = BankAccountTypeForPayoneerGBT.Personal;
    }
    const bank_field_details = this.getBankFieldDetails(registerPayeeFormat);
    return {
      type: payoutType,
      country: this.createBeneficiaryReq.bank_country == 'GB' ? 'UK' : this.createBeneficiaryReq.bank_country,
      currency: this.createBeneficiaryReq?.secondaryCurrency || this.createBeneficiaryReq.currency,
      bank_account_type: bankAccountType,
      bank_field_details,
    };
  }

  validateAndTransformRegisterPayeeInput(registerPayeeFormat: RegisterPayeeFormatOutput): RegisterPayeeInput {
    const payout_method = this.getPayeePayoutMethod(registerPayeeFormat) || ({} as RegisterPayeePayoutMethod);
    const payee_id = this.createBeneficiaryReq.payeeId;
    const payee = this.getPayeeInput() || ({} as PayeeInput);

    return {
      payee_id,
      payee,
      payout_method,
    };
  }

  getEditPayeeProfileInput(registerPayeeInput: RegisterPayeeInput): EditPayeeProfileInput {
    const input = {
      contact: {
        first_name: registerPayeeInput?.payee?.contact?.first_name ?? '',
        last_name: registerPayeeInput?.payee?.contact?.last_name ?? '',
        date_of_birth: new Date(registerPayeeInput?.payee?.contact?.date_of_birth)?.toISOString() ?? '',
      },
      address: {
        address_line_1: registerPayeeInput?.payee?.address?.address_line_1 ?? '',
        address_line_2: registerPayeeInput?.payee?.address?.address_line_2 ?? '',
        city: registerPayeeInput?.payee?.address?.city ?? '',
        state: registerPayeeInput?.payee?.address?.state ?? null,
        country: registerPayeeInput?.payee?.address?.country ?? '',
        zip_code: registerPayeeInput?.payee?.address?.zip_code ?? '',
      },
      company: {
        legal_type: registerPayeeInput?.payee?.company?.legal_type,
        name: registerPayeeInput?.payee?.company?.name ?? '',
        incorporated_country: registerPayeeInput?.payee?.company?.incorporated_country ?? '',
        incorporated_state: registerPayeeInput?.payee?.company?.incorporated_state ?? '',
        incorporated_address_1: registerPayeeInput?.payee?.company?.incorporated_address_1 ?? '',
        incorporated_address_2: registerPayeeInput?.payee?.company?.incorporated_address_2 ?? '',
        incorporated_city: registerPayeeInput?.payee?.company?.incorporated_city ?? '',
        incorporated_zipcode: registerPayeeInput?.payee?.company?.incorporated_zipcode ?? '',
      },
    };

    if (input?.address?.country != Country.US) {
      delete input?.address?.state;
      delete input?.company?.incorporated_state;
    }

    return input;
  }
}

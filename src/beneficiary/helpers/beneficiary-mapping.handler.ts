import { PayoneerBankFieldNames } from '../../common/constants/enums';

export class PayoneerGBTMapper {
  private readonly fieldMapping: Record<PayoneerBankFieldNames, string[]> = {
    [PayoneerBankFieldNames.AccountName]: ['accountHolder', 'accountHolderName'],
    [PayoneerBankFieldNames.AccountNumber]: ['accountNumber', 'standardBankingCode'],
    [PayoneerBankFieldNames.BankName]: ['name', 'bankName'],
    [PayoneerBankFieldNames.Swift]: ['swift', 'bic', 'bicSwift'],
    [PayoneerBankFieldNames.AccountType]: ['accountType'],
    [PayoneerBankFieldNames.Suffix]: ['suffix'],
    [PayoneerBankFieldNames.StorefrontURL]: ['storefrontURL'],
    [PayoneerBankFieldNames.RoutingNumber]: ['ach', 'routingNumber'],
    [PayoneerBankFieldNames.IBAN]: ['iban'],
    [PayoneerBankFieldNames.State]: ['state'],
    [PayoneerBankFieldNames.BankCode]: ['bankNumber', 'bankCode'],
    [PayoneerBankFieldNames.ProviceCode]: ['proviceCode'],
    [PayoneerBankFieldNames.IncorporationNumber]: ['incorporationNumber'],
    [PayoneerBankFieldNames.IdType]: ['idType'],
    [PayoneerBankFieldNames.IdNumber]: ['idNumber'],
    [PayoneerBankFieldNames.BIC]: ['bic', 'swift', 'bicSwift'],
    [PayoneerBankFieldNames.AccountTaxNumber]: ['ntn', 'rut', 'accountTaxNumber'],
    [PayoneerBankFieldNames.CityCode]: ['cityCode'],
    [PayoneerBankFieldNames.BranchName]: ['branchName'],
    [PayoneerBankFieldNames.BranchCode]: ['branchCode', 'bankBranchNumber', 'branchTransitNumber'],
    [PayoneerBankFieldNames.BankNumber]: ['ifsc', 'bankNumber'],
    [PayoneerBankFieldNames.Address]: ['address'],
    [PayoneerBankFieldNames.BIN]: ['bin'],
    [PayoneerBankFieldNames.AccountNameEnglish]: ['accountNameEnglish'],
    [PayoneerBankFieldNames.TIN]: ['tin'],
    [PayoneerBankFieldNames.BSB]: ['bsbCode'],
    [PayoneerBankFieldNames.AccountHolderCitizenship]: ['accountHolderCitizenship'],
    [PayoneerBankFieldNames.SortCode]: ['sortCode'],
    [PayoneerBankFieldNames.PassportNumber]: ['passportNumber', 'idNumber', 'accountTaxNumber'],
    [PayoneerBankFieldNames.ForeignIDNumber]: ['foreignIDNumber', 'accountHolderCitizenship'],
    [PayoneerBankFieldNames.CNIC]: ['cnic'],
    [PayoneerBankFieldNames.SNIC]: ['snic'],
  };

  mapFields<T>(source: T, targetKeys: PayoneerBankFieldNames[]): Record<string, any>[] {
    const result: Record<string, any>[] = [];

    targetKeys.forEach((targetKey) => {
      const sourceKeys = this.fieldMapping[targetKey] || [];
      const value = sourceKeys.reduce((val, key) => val || (source as Record<string, string>)[key], null);

      if (value !== null && value !== undefined) {
        result.push({ name: targetKey, value });
      }
    });

    return result;
  }

  // Reverse mapping: Target fields -> Source object
  reverseMapFields(target: Record<string, any>[]): Record<string, any> {
    const reversedSource: Record<string, any> = {};

    target.forEach(({ name, value }) => {
      let sourceKeys = this.fieldMapping[name as PayoneerBankFieldNames] || [];

      if (!sourceKeys?.length) {
        for (const key in this.fieldMapping) {
          if (key?.toLowerCase().includes(name?.toLowerCase().trim())) {
            sourceKeys = this.fieldMapping[key as PayoneerBankFieldNames] || [];
          }
        }
      }

      sourceKeys.forEach((sourceKey) => {
        reversedSource[sourceKey] = value;
      });
    });

    return reversedSource;
  }
}

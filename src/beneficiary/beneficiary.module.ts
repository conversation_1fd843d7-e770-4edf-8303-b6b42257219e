import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { CommonModule } from '../common/common.module';
import { PayoneerGrpcController } from './controller/private.grpc.controller';
import { PayoneerClientModule } from '../common/external-service/payoneer/payoneer.module';
import { BeneficiaryService } from './services/beneficiary.service';
import { ProgramCurrencyMappingEntity } from '../common/entities/program.entity';
import { DatabaseModule } from '../database/database.module';
import { ProgramCurrencyMappingRepository } from '../common/repositories/program-currency-mapping.repository';
import { PaymentGrpcClientModule } from '../common/external-service/payments/payments.module';
import { ProgramService } from '../programs/services/program.service';
import { ProgramGrpcController } from '../programs/controller/program.grpc.controller';
import { ContractServiceModule } from '../common/external-service/contract-service/contract.module';
import { BeneficiaryMutationResolver } from './resolvers/beneficiary.mutation.resolver';
import { ProgramTokenService } from 'src/payin/service/program-token.service';
import { ProgramTokensRepository } from 'src/common/repositories/program-token.repository';
import { PayoneerAccountRepository } from 'src/common/repositories/account.repository';
import { ProgramTokenServiceHelper } from 'src/payin/service/helper/program-token.service.helper';
import { BeneficiaryQueryResolver } from './resolvers/beneficiary.query.resolver';

@Module({
  imports: [
    CommonModule,
    CqrsModule,
    PayoneerClientModule,
    DatabaseModule.forPGFeature([ProgramCurrencyMappingEntity]),
    PaymentGrpcClientModule,
    ContractServiceModule,
  ],
  providers: [
    BeneficiaryService,
    ProgramCurrencyMappingRepository,
    ProgramTokensRepository,
    PayoneerAccountRepository,
    ProgramService,
    BeneficiaryMutationResolver,
    BeneficiaryQueryResolver,
    ProgramTokenService,
    ProgramTokenServiceHelper,
  ],
  controllers: [PayoneerGrpcController, ProgramGrpcController],
})
export class BeneficiaryModule {}

import { Controller, Injectable, UseInterceptors } from '@nestjs/common';
import { payoneer } from '@skuad/proto-utils/dist/interface/proto/payoneer/payoneer';
import { GrpcMethod, RpcException } from '@nestjs/microservices';
import { ServiceType } from 'skuad-utils-ts/dist/common/constants/enums';
import { LogAfter } from 'skuad-utils-ts/dist/common';
import { BeneficiaryService } from '../../beneficiary/services/beneficiary.service';
import { LoggerService } from '../../common/services/LoggerService';
import { PayoneerHttpClient } from '../../common/external-service/payoneer/services/payoneer.http.client.service';
import { convertToCamelCase } from '../../common/helpers/utils';
import { RpcLoggingInterceptor } from '@skuad/proto-utils/dist/common/interceptor/grpc.interceptor';
import { Status } from '@grpc/grpc-js/build/src/constants';
import { GrpcErrorCode, PayoneerProvider } from '../../common/constants/enums';
import { ProgramCurrencyMappingRepository } from '../../common/repositories/program-currency-mapping.repository';

@Controller()
@Injectable()
@UseInterceptors(RpcLoggingInterceptor)
export class PayoneerGrpcController {
  constructor(
    private readonly beneficiaryService: BeneficiaryService,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    private readonly logger: LoggerService,
    private readonly payoneerClient: PayoneerHttpClient,
    private readonly programsRepository: ProgramCurrencyMappingRepository,
  ) {}

  @GrpcMethod('PayoneerRpcService', 'createBeneficiaryV1Private')
  @LogAfter('logger', ServiceType.Grpc)
  async createBeneficiaryV1Private(
    createBeneficiaryGrpcInput: payoneer.CreateBeneficiaryInput,
  ): Promise<payoneer.CreateBeneficiaryOutput> {
    return this.beneficiaryService.createRegistrationLinkV4(createBeneficiaryGrpcInput);
  }

  @GrpcMethod('PayoneerRpcService', 'getBankFieldsForPayee')
  @LogAfter('logger', ServiceType.Grpc)
  async getBankFieldsForPayee(
    getBankFieldsForPayeeInput: payoneer.GetBankFieldsForPayeeInput,
  ): Promise<payoneer.GetBankFieldsForPayeeOutput> {
    return this.beneficiaryService.getBankFieldsForPayee(getBankFieldsForPayeeInput);
  }

  @GrpcMethod('PayoneerRpcService', 'getPayeeStatus')
  @LogAfter('logger', ServiceType.Grpc)
  async getPayeeStatus(
    getPayeeStatusInput: payoneer.GetPayeeStatusInput,
  ): Promise<payoneer.GetPayeeStatusOutput> {
    const [result, error] = await this.payoneerClient.getPayeeStatus(
      getPayeeStatusInput.payeeId,
      getPayeeStatusInput.programId,
    );
    if (error) {
      throw error;
    }

    return {
      payeeStatus: result?.result?.status?.description as unknown as payoneer.PayeeStatus,
    };
  }

  @GrpcMethod('PayoneerRpcService', 'getPayoutDetails')
  @LogAfter('logger', ServiceType.Grpc)
  async getPayoutStatus(
    getPayoutStatusInput: payoneer.GetPayoutDetailsInput,
  ): Promise<payoneer.GetPayoutDetailsOutput> {
    const [result, error] = await this.payoneerClient.getPayoutStatus(
      getPayoutStatusInput.payoutId,
      getPayoutStatusInput.programId,
    );

    if (error && error.error?.['error_details']?.['code'] === GrpcErrorCode.payoutNotFound) {
      this.logger.error({
        message: `Got error in getting payout details for ${JSON.stringify(getPayoutStatusInput)}`,
        error,
      });
      throw new RpcException({
        code: Status.NOT_FOUND,
        message: `Payout details not found for the given input ${JSON.stringify(getPayoutStatusInput)} with code ${error?.errors?.error_details?.code}`,
      });
    } else if (error) {
      throw new RpcException(error);
    }

    const payoutResult = result?.result;

    const responseData = convertToCamelCase(payoutResult);

    const response: payoneer.GetPayoutDetailsOutput = {
      ...responseData,
      status: result?.result?.status as unknown as payoneer.PayoutStatus,
    };

    return response;
  }

  @GrpcMethod('PayoneerRpcService', 'createBeneficiaryForPayoneerGBTProgram')
  @LogAfter('logger', ServiceType.Grpc)
  async createBeneficiaryForPayoneerGBTProgram(
    createBeneficiaryForPayoneerGBTProgramInput: payoneer.CreateBeneficiaryForPayoneerGBTProgramInput,
  ): Promise<payoneer.CreateBeneficiaryForPayoneerGBTProgramOutput> {
    const [response, error] = await this.beneficiaryService.createBeneficiaryForPayoneerGBTProgram(
      createBeneficiaryForPayoneerGBTProgramInput,
    );

    if (error) {
      throw new RpcException(error);
    }

    return response;
  }

  @GrpcMethod('PayoneerRpcService', 'getBankAccountDetailsForPayoneerGBTProgram')
  @LogAfter('logger', ServiceType.Grpc)
  async getPayeeBankAccountDetails(
    getPayeeBankAccountDetailsInput: payoneer.GetBankAccountDetailsForPayoneerGBTProgramInput,
  ): Promise<payoneer.GetBankAccountDetailsForPayoneerGBTProgramOutput> {
    const response = await this.beneficiaryService.getPayeeBankAccountDetails(
      getPayeeBankAccountDetailsInput?.payeeId,
    );
    return {
      bankAccountDetails: response,
    };
  }

  @GrpcMethod('PayoneerRpcService', 'transferFunds')
  @LogAfter('logger', ServiceType.Grpc)
  async transferFunds(
    transferFundsInput: payoneer.TransferFundsInput,
  ): Promise<payoneer.TransferFundsOutput> {

    if (
      transferFundsInput.sourceCurrency &&
      transferFundsInput.targetCurrency &&
      transferFundsInput.sourceProvider &&
      transferFundsInput.targetProvider
    ) {
      const sourceProgramId = await this.programsRepository.getProgramIdUsingCurrency(
        transferFundsInput.sourceCurrency,
        transferFundsInput.sourceProvider as PayoneerProvider,
      );

      const targetProgramId = await this.programsRepository.getProgramIdUsingCurrency(
        transferFundsInput.targetCurrency,
        transferFundsInput.targetProvider as PayoneerProvider,
      );

      if (!transferFundsInput.sourcePartnerId || !transferFundsInput.targetPartnerId) {
        transferFundsInput.sourcePartnerId = sourceProgramId;
        transferFundsInput.targetPartnerId = targetProgramId;
      }
    }

    const [response, error] = await this.payoneerClient.transferFundsV4(
      {
        target_partner_id: transferFundsInput.targetPartnerId,
        amount: transferFundsInput.amount,
        description: transferFundsInput.description,
      },
      transferFundsInput.sourcePartnerId,
    );

    if (error) {
      this.logger.error({
        message: `Got error in transferring funds for ${JSON.stringify(transferFundsInput)}`,
        error,
      });
      throw new RpcException(error);
    }

    return {
      dateTime: response?.result?.date_time,
      fundingRequestId: response?.result?.funding_request_id,
      sourceCurrency: response?.result?.source_currency,
      sourceAmount: response?.result?.source_amount,
      targetCurrency: response?.result?.target_currency,
      targetAmount: response?.result?.target_amount,
    };
  }
}

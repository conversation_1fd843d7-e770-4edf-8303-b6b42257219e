import { Test, TestingModule } from '@nestjs/testing';
import { PayoneerGrpcController } from './private.grpc.controller';
import { BeneficiaryService } from '../../beneficiary/services/beneficiary.service';
import { LoggerService } from '../../common/services/LoggerService';
import { PayoneerHttpClient } from '../../common/external-service/payoneer/services/payoneer.http.client.service';
import { RpcException } from '@nestjs/microservices';
import { payoneer } from '@skuad/proto-utils/dist/interface/proto/payoneer/payoneer';
import { GrpcErrorCode } from '../../common/constants/enums';
import { ProgramCurrencyMappingRepository } from '../../common/repositories/program-currency-mapping.repository';

describe('PayoneerGrpcController', () => {
  let controller: PayoneerGrpcController;
  let beneficiaryService: BeneficiaryService;
  let payoneerClient: PayoneerHttpClient;

  const mockBeneficiaryService = {
    createRegistrationLinkV4: jest.fn(),
    getBankFieldsForPayee: jest.fn(),
    createBeneficiaryForPayoneerGBTProgram: jest.fn(),
    getPayeeBankAccountDetails: jest.fn(),
  };

  const mockPayoneerClient = {
    getPayeeStatus: jest.fn(),
    getPayoutStatus: jest.fn(),
    transferFundsV4: jest.fn(),
  };

  const mockProgramsRepository = {
    getProgramIdUsingCurrency: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PayoneerGrpcController],
      providers: [
        {
          provide: BeneficiaryService,
          useValue: mockBeneficiaryService,
        },
        {
          provide: PayoneerHttpClient,
          useValue: mockPayoneerClient,
        },
        {
          provide: LoggerService,
          useValue: { log: jest.fn(), error: jest.fn() },
        },
        {
          provide: ProgramCurrencyMappingRepository,
          useValue: mockProgramsRepository,
        },
      ],
    }).compile();

    controller = module.get<PayoneerGrpcController>(PayoneerGrpcController);
    beneficiaryService = module.get<BeneficiaryService>(BeneficiaryService);
    payoneerClient = module.get<PayoneerHttpClient>(PayoneerHttpClient);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createBeneficiaryV1Private', () => {
    it('should create beneficiary successfully', async () => {
      const input = { payeeId: '123' } as payoneer.CreateBeneficiaryInput;
      const expectedOutput = { success: true } as payoneer.CreateBeneficiaryOutput;

      mockBeneficiaryService.createRegistrationLinkV4.mockResolvedValue(expectedOutput);

      const result = await controller.createBeneficiaryV1Private(input);
      expect(result).toEqual(expectedOutput);
    });
  });

  describe('getBankFieldsForPayee', () => {
    it('should get bank fields successfully', async () => {
      const input = { countryCode: 'US' } as payoneer.GetBankFieldsForPayeeInput;
      const expectedOutput = { fields: [] } as payoneer.GetBankFieldsForPayeeOutput;

      mockBeneficiaryService.getBankFieldsForPayee.mockResolvedValue(expectedOutput);

      const result = await controller.getBankFieldsForPayee(input);
      expect(result).toEqual(expectedOutput);
    });
  });

  describe('getPayeeStatus', () => {
    it('should get payee status successfully', async () => {
      const input = { payeeId: '123' } as payoneer.GetPayeeStatusInput;
      mockPayoneerClient.getPayeeStatus.mockResolvedValue([
        { result: { status: { description: 'ACTIVE' } } },
        null,
      ]);

      const result = await controller.getPayeeStatus(input);
      expect(result).toEqual({ payeeStatus: 'ACTIVE' });
    });

    it('should throw error when payee status request fails', async () => {
      const input = { payeeId: '123' } as payoneer.GetPayeeStatusInput;
      const error = new Error('Failed to get payee status');
      mockPayoneerClient.getPayeeStatus.mockResolvedValue([null, error]);

      await expect(controller.getPayeeStatus(input)).rejects.toThrow(error);
    });

    it('should return undefined payeeStatus if result is undefined', async () => {
      const input = { payeeId: 'payee-x' } as payoneer.GetPayeeStatusInput;
      mockPayoneerClient.getPayeeStatus.mockResolvedValue([undefined, null]);

      const result = await controller.getPayeeStatus(input);
      expect(result).toEqual({ payeeStatus: undefined });
    });

    it('should return undefined payeeStatus if result.result is undefined', async () => {
      const input = { payeeId: 'payee-x' } as payoneer.GetPayeeStatusInput;
      mockPayoneerClient.getPayeeStatus.mockResolvedValue([{ result: undefined }, null]);

      const result = await controller.getPayeeStatus(input);
      expect(result).toEqual({ payeeStatus: undefined });
    });

    it('should return undefined payeeStatus if status is undefined', async () => {
      const input = { payeeId: 'payee-x' } as payoneer.GetPayeeStatusInput;
      mockPayoneerClient.getPayeeStatus.mockResolvedValue([{ result: { status: undefined } }, null]);

      const result = await controller.getPayeeStatus(input);
      expect(result).toEqual({ payeeStatus: undefined });
    });
  });

  describe('getPayoutStatus', () => {
    it('should get payout details successfully', async () => {
      const input = { payoutId: '123', programId: '456' } as payoneer.GetPayoutDetailsInput;
      mockPayoneerClient.getPayoutStatus.mockResolvedValue([
        {
          result: {
            status: 'COMPLETED',
            amount: 100,
          },
        },
        null,
      ]);

      const result = await controller.getPayoutStatus(input);
      expect(result).toMatchObject({ status: 'COMPLETED' });
    });

    it('should handle undefined payout result gracefully', async () => {
      const input = { payoutId: '123', programId: '456' } as payoneer.GetPayoutDetailsInput;
      mockPayoneerClient.getPayoutStatus.mockResolvedValue([{ result: null }, null]);

      const result = await controller.getPayoutStatus(input);
      expect(result).toEqual({ status: undefined, amount: undefined });
    });

    it('should throw RpcException with NOT_FOUND code when payout is not found', async () => {
      const input = { payoutId: '123', programId: '456' } as payoneer.GetPayoutDetailsInput;

      const errorMock = {
        error: {
          error_details: {
            code: GrpcErrorCode.payoutNotFound,
          },
        },
        errors: {
          error_details: {
            code: GrpcErrorCode.payoutNotFound,
          },
        },
      };

      mockPayoneerClient.getPayoutStatus.mockResolvedValue([null, errorMock]);

      await expect(controller.getPayoutStatus(input)).rejects.toThrow(RpcException);
    });

    it('should throw RpcException for generic payout error', async () => {
      const input = { payoutId: '123', programId: '456' } as payoneer.GetPayoutDetailsInput;
      const error = new Error('Something went wrong');

      mockPayoneerClient.getPayoutStatus.mockResolvedValue([null, error]);

      await expect(controller.getPayoutStatus(input)).rejects.toThrow(RpcException);
    });

    it('should return undefined payout status when status is null', async () => {
      const input = { payoutId: '123', programId: '456' } as payoneer.GetPayoutDetailsInput;
      mockPayoneerClient.getPayoutStatus.mockResolvedValue([{ result: { status: null } }, null]);

      const result = await controller.getPayoutStatus(input);
      expect(result.status).toBeNull(); // or undefined depending on actual logic
    });
  });

  describe('createBeneficiaryForPayoneerGBTProgram', () => {
    it('should create GBT beneficiary successfully', async () => {
      const input = {} as payoneer.CreateBeneficiaryForPayoneerGBTProgramInput;
      const expectedOutput = { success: true } as payoneer.CreateBeneficiaryForPayoneerGBTProgramOutput;

      mockBeneficiaryService.createBeneficiaryForPayoneerGBTProgram.mockResolvedValue([expectedOutput, null]);

      const result = await controller.createBeneficiaryForPayoneerGBTProgram(input);
      expect(result).toEqual(expectedOutput);
    });

    it('should throw RpcException when creation fails', async () => {
      const input = {} as payoneer.CreateBeneficiaryForPayoneerGBTProgramInput;
      const error = new Error('Creation failed');

      mockBeneficiaryService.createBeneficiaryForPayoneerGBTProgram.mockResolvedValue([null, error]);

      await expect(controller.createBeneficiaryForPayoneerGBTProgram(input)).rejects.toThrow(RpcException);
    });
  });

  describe('getPayeeBankAccountDetails', () => {
    it('should get bank account details successfully', async () => {
      const input = { payeeId: '123' } as payoneer.GetBankAccountDetailsForPayoneerGBTProgramInput;
      const expectedDetails = { accountNumber: '12345' };
      mockBeneficiaryService.getPayeeBankAccountDetails = jest.fn().mockResolvedValue(expectedDetails);

      const result = await controller.getPayeeBankAccountDetails(input);
      expect(result).toEqual({ bankAccountDetails: expectedDetails });
    });

    it('should handle undefined payeeId gracefully in getPayeeBankAccountDetails', async () => {
      const input = {} as payoneer.GetBankAccountDetailsForPayoneerGBTProgramInput;
      const expected = {};

      mockBeneficiaryService.getPayeeBankAccountDetails.mockResolvedValue(expected);
      const result = await controller.getPayeeBankAccountDetails(input);

      expect(result).toEqual({ bankAccountDetails: expected });
    });
  });

  describe('transferFunds', () => {
    it('should transfer funds successfully', async () => {
      const input = {
        targetPartnerId: '123',
        sourcePartnerId: '456',
        amount: 100,
        description: 'Test transfer',
      } as payoneer.TransferFundsInput;

      const mockResponse = {
        result: {
          date_time: '2023-01-01',
          funding_request_id: 'FR123',
          source_currency: 'USD',
          source_amount: 100,
          target_currency: 'EUR',
          target_amount: 90,
        },
      };

      mockPayoneerClient.transferFundsV4 = jest.fn().mockResolvedValue([mockResponse, null]);

      const result = await controller.transferFunds(input);

      expect(result).toEqual({
        dateTime: '2023-01-01',
        fundingRequestId: 'FR123',
        sourceCurrency: 'USD',
        sourceAmount: 100,
        targetCurrency: 'EUR',
        targetAmount: 90,
      });
    });

    it('should throw RpcException when transfer fails', async () => {
      const input = {
        targetPartnerId: '123',
        sourcePartnerId: '456',
        amount: 100,
        description: 'Test transfer',
      } as payoneer.TransferFundsInput;

      const error = new Error('Transfer failed');
      mockPayoneerClient.transferFundsV4 = jest.fn().mockResolvedValue([null, error]);

      await expect(controller.transferFunds(input)).rejects.toThrow(RpcException);
    });

    it('should handle null payout result safely', async () => {
      const input = { payoutId: '123', programId: '456' } as payoneer.GetPayoutDetailsInput;
      mockPayoneerClient.getPayoutStatus.mockResolvedValue([{ result: undefined }, null]);

      const result = await controller.getPayoutStatus(input);
      expect(result.status).toBeUndefined();
    });

    it('should transfer funds successfully and access all result fields', async () => {
      const input = {
        targetPartnerId: '123',
        sourcePartnerId: '456',
        amount: 100,
        description: 'Test transfer',
      } as payoneer.TransferFundsInput;

      const mockResponse = {
        result: {
          date_time: '2023-01-01T00:00:00Z',
          funding_request_id: 'REQ123',
          source_currency: 'USD',
          source_amount: 100,
          target_currency: 'EUR',
          target_amount: 95,
        },
      };

      mockPayoneerClient.transferFundsV4.mockResolvedValue([mockResponse, null]);

      const result = await controller.transferFunds(input);
      expect(result).toEqual({
        dateTime: '2023-01-01T00:00:00Z',
        fundingRequestId: 'REQ123',
        sourceCurrency: 'USD',
        sourceAmount: 100,
        targetCurrency: 'EUR',
        targetAmount: 95,
      });
    });

    it('should handle null response object safely in transferFunds', async () => {
      const input = {
        targetPartnerId: '123',
        sourcePartnerId: '456',
        amount: 100,
        description: 'Transfer with null response',
      } as payoneer.TransferFundsInput;

      // simulate response as `null`
      mockPayoneerClient.transferFundsV4.mockResolvedValue([null, null]);

      const result = await controller.transferFunds(input);

      expect(result).toEqual({
        dateTime: undefined,
        fundingRequestId: undefined,
        sourceCurrency: undefined,
        sourceAmount: undefined,
        targetCurrency: undefined,
        targetAmount: undefined,
      });
    });

    it('should handle undefined response.result safely in transferFunds', async () => {
      const input = {
        targetPartnerId: '123',
        sourcePartnerId: '456',
        amount: 100,
        description: 'Safe fallback test',
      } as payoneer.TransferFundsInput;

      const mockResponse = {
        result: undefined,
      };

      mockPayoneerClient.transferFundsV4.mockResolvedValue([mockResponse, null]);

      const result = await controller.transferFunds(input);

      expect(result).toEqual({
        dateTime: undefined,
        fundingRequestId: undefined,
        sourceCurrency: undefined,
        sourceAmount: undefined,
        targetCurrency: undefined,
        targetAmount: undefined,
      });
    });

    describe('amount rounding logic', () => {
      it('should round amount to 2 decimal places when amount has more than 2 decimals', async () => {
        const input = {
          targetPartnerId: '123',
          sourcePartnerId: '456',
          amount: 100.12345, // Should be rounded to 100.12
          description: 'Test rounding',
        } as payoneer.TransferFundsInput;

        const mockResponse = {
          result: {
            date_time: '2023-01-01',
            funding_request_id: 'FR123',
            source_currency: 'USD',
            source_amount: 100.12,
            target_currency: 'USD',
            target_amount: 100.12,
          },
        };

        mockPayoneerClient.transferFundsV4.mockResolvedValue([mockResponse, null]);

        await controller.transferFunds(input);

        // Verify that the amount passed to transferFundsV4 is rounded to 2 decimal places
        expect(mockPayoneerClient.transferFundsV4).toHaveBeenCalledWith(
          {
            target_partner_id: '123',
            amount: 100.12, // Math.round(100.12345 * 100) / 100 = 100.12
            description: 'Test rounding',
          },
          '456',
        );
      });

      it('should round amount correctly for edge case with .995', async () => {
        const input = {
          targetPartnerId: '123',
          sourcePartnerId: '456',
          amount: 100.995, // Should be rounded to 101.00
          description: 'Test edge case rounding',
        } as payoneer.TransferFundsInput;

        const mockResponse = {
          result: {
            date_time: '2023-01-01',
            funding_request_id: 'FR123',
            source_currency: 'USD',
            source_amount: 101,
            target_currency: 'USD',
            target_amount: 101,
          },
        };

        mockPayoneerClient.transferFundsV4.mockResolvedValue([mockResponse, null]);

        await controller.transferFunds(input);

        expect(mockPayoneerClient.transferFundsV4).toHaveBeenCalledWith(
          {
            target_partner_id: '123',
            amount: 101, // Math.round(100.995 * 100) / 100 = 101
            description: 'Test edge case rounding',
          },
          '456',
        );
      });

      it('should round amount correctly for edge case with .994', async () => {
        const input = {
          targetPartnerId: '123',
          sourcePartnerId: '456',
          amount: 100.994, // Should be rounded to 100.99
          description: 'Test edge case rounding down',
        } as payoneer.TransferFundsInput;

        const mockResponse = {
          result: {
            date_time: '2023-01-01',
            funding_request_id: 'FR123',
            source_currency: 'USD',
            source_amount: 100.99,
            target_currency: 'USD',
            target_amount: 100.99,
          },
        };

        mockPayoneerClient.transferFundsV4.mockResolvedValue([mockResponse, null]);

        await controller.transferFunds(input);

        expect(mockPayoneerClient.transferFundsV4).toHaveBeenCalledWith(
          {
            target_partner_id: '123',
            amount: 100.99, // Math.round(100.994 * 100) / 100 = 100.99
            description: 'Test edge case rounding down',
          },
          '456',
        );
      });

      it('should handle integer amounts without modification', async () => {
        const input = {
          targetPartnerId: '123',
          sourcePartnerId: '456',
          amount: 100, // Integer amount should remain 100
          description: 'Test integer amount',
        } as payoneer.TransferFundsInput;

        const mockResponse = {
          result: {
            date_time: '2023-01-01',
            funding_request_id: 'FR123',
            source_currency: 'USD',
            source_amount: 100,
            target_currency: 'USD',
            target_amount: 100,
          },
        };

        mockPayoneerClient.transferFundsV4.mockResolvedValue([mockResponse, null]);

        await controller.transferFunds(input);

        expect(mockPayoneerClient.transferFundsV4).toHaveBeenCalledWith(
          {
            target_partner_id: '123',
            amount: 100, // Math.round(100 * 100) / 100 = 100
            description: 'Test integer amount',
          },
          '456',
        );
      });

      it('should handle amounts with exactly 2 decimal places', async () => {
        const input = {
          targetPartnerId: '123',
          sourcePartnerId: '456',
          amount: 100.5, // Already 2 decimal places
          description: 'Test 2 decimal places',
        } as payoneer.TransferFundsInput;

        const mockResponse = {
          result: {
            date_time: '2023-01-01',
            funding_request_id: 'FR123',
            source_currency: 'USD',
            source_amount: 100.5,
            target_currency: 'USD',
            target_amount: 100.5,
          },
        };

        mockPayoneerClient.transferFundsV4.mockResolvedValue([mockResponse, null]);

        await controller.transferFunds(input);

        expect(mockPayoneerClient.transferFundsV4).toHaveBeenCalledWith(
          {
            target_partner_id: '123',
            amount: 100.5, // Math.round(100.50 * 100) / 100 = 100.5
            description: 'Test 2 decimal places',
          },
          '456',
        );
      });

      it('should handle very small amounts correctly', async () => {
        const input = {
          targetPartnerId: '123',
          sourcePartnerId: '456',
          amount: 0.123456, // Should be rounded to 0.12
          description: 'Test small amount',
        } as payoneer.TransferFundsInput;

        const mockResponse = {
          result: {
            date_time: '2023-01-01',
            funding_request_id: 'FR123',
            source_currency: 'USD',
            source_amount: 0.12,
            target_currency: 'USD',
            target_amount: 0.12,
          },
        };

        mockPayoneerClient.transferFundsV4.mockResolvedValue([mockResponse, null]);

        await controller.transferFunds(input);

        expect(mockPayoneerClient.transferFundsV4).toHaveBeenCalledWith(
          {
            target_partner_id: '123',
            amount: 0.12, // Math.round(0.123456 * 100) / 100 = 0.12
            description: 'Test small amount',
          },
          '456',
        );
      });

      it('should handle zero amount', async () => {
        const input = {
          targetPartnerId: '123',
          sourcePartnerId: '456',
          amount: 0,
          description: 'Test zero amount',
        } as payoneer.TransferFundsInput;

        const mockResponse = {
          result: {
            date_time: '2023-01-01',
            funding_request_id: 'FR123',
            source_currency: 'USD',
            source_amount: 0,
            target_currency: 'USD',
            target_amount: 0,
          },
        };

        mockPayoneerClient.transferFundsV4.mockResolvedValue([mockResponse, null]);

        await controller.transferFunds(input);

        expect(mockPayoneerClient.transferFundsV4).toHaveBeenCalledWith(
          {
            target_partner_id: '123',
            amount: 0, // Math.round(0 * 100) / 100 = 0
            description: 'Test zero amount',
          },
          '456',
        );
      });
    });
  });
});

import { renderGQLError } from './app.utils';

describe('renderGQLError', () => {
  it('should return the error as-is (object)', () => {
    const error = { message: 'GraphQL error', code: 'GRAPHQL_ERROR' };

    const result = renderGQLError(error);

    expect(result).toBe(error);
  });

  it('should return the error as-is (string)', () => {
    const error = 'Some error string';

    const result = renderGQLError(error);

    expect(result).toBe(error);
  });

  it('should return undefined if error is undefined', () => {
    const result = renderGQLError(undefined);

    expect(result).toBeUndefined();
  });

  it('should return null if error is null', () => {
    const result = renderGQLError(null);

    expect(result).toBeNull();
  });
});

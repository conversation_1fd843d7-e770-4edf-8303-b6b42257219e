# Payoneer Service Integration with V4 APIs

This project integrates with Payoneer’s V4 APIs and pulls dependencies from the private Skuad NPM registry.

---

## 📦 Skuad Private NPM Registry Setup

### 1. Get a GitLab Personal Access Token

- Go to GitLab → User Settings → Access Tokens.
- Create a token with `read_api` and `read_package_registry` scopes.
- Copy and save your token securely.

### 2. Configure `.npmrc`

Add the following lines to your project’s `.npmrc` (or your global `~/.npmrc`):

```ini
@skuad:registry=https://gitlab.skuad.in/api/v4/packages/npm/
//gitlab.skuad.in/api/v4/packages/npm/:_authToken=<YOUR_PERSONAL_ACCESS_TOKEN>
//gitlab.skuad.in/api/v4/packages/npm/:always-auth=true
```

{"cSpell.words": ["CNIC", "dtos", "iban", "masspayouts", "payin", "<PERSON>oneer", "Provice", "SKUAD", "SNIC"], "editor.tabSize": 2, "files.trimTrailingWhitespace": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "eslint.probe": ["javascript", "typescript", "html", "markdown", "json"], "eslint.validate": ["json", "javascript", "typescript"], "javascript.validate.enable": true, "editor.formatOnSave": true, "editor.formatOnPaste": true, "[javascript]": {"editor.formatOnSave": false, "editor.codeActionsOnSave": ["source.formatDocument", "source.fixAll.eslint"]}, "[typescript]": {"editor.formatOnSave": true, "editor.codeActionsOnSave": ["source.formatDocument", "source.fixAll.eslint"], "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.formatOnSave": false, "editor.codeActionsOnSave": ["source.formatDocument", "source.fixAll.eslint"]}, "sonarlint.connectedMode.project": {"connectionId": "https-sonarqube-de-skuad-in", "projectKey": "skuad_payoneer_6ff234b0-a297-40d2-a93e-e182e63e4e9b"}}
export const setTestEnvs = (customEnv?: Record<string, any>): void => {
  process.env.PAYMENT_SERVICE_GRPC_API_KEY = 'helloworld';
  process.env.PROVIDER_API_BASE_URL = 'http://localhost.payoneer.io';
  process.env.GRPC_PAYMENT_SERVICE_URL = 'http://localhost.skuad.io';
  process.env.PRIVATE_API_KEY = 'helloworld';
  process.env.PROVIDER_CREDENTIALS =
    'ewogICJJTiI6IHsKICAgICJBIjogewogICAgICAicHJvZ3JhbV9pZCI6ICJwcm9ncmFtIGlkIDEiLAogICAgICAidG9rZW4iOiAiQmFzZTY0IGVuY29kZSB0b2tlbiAxIgogICAgfSwKICAgICJCIjogewogICAgICAicHJvZ3JhbV9pZCI6ICJwcm9ncmFtIGlkIDIiLAogICAgICAidG9rZW4iOiAiQmFzZTY0IGVuY29kZSB0b2tlbiAyIgogICAgfSwKICAgICJDIjogewogICAgICAicHJvZ3JhbV9pZCI6ICJwcm9ncmFtIGlkIDIiLAogICAgICAidG9rZW4iOiAiQmFzZTY0IGVuY29kZSB0b2tlbiAyIgogICAgfQogIH0sCiAgIkFVIjogewogICAgImRlZmF1bHQiOiB7CiAgICAgICJwcm9ncmFtX2lkIjogInByb2dyYW0gaWQgMSIsCiAgICAgICJ0b2tlbiI6ICJCYXNlNjQgZW5jb2RlIHRva2VuIDEiCiAgICB9CiAgfSwKICAiUEsiOiB7CiAgICAiRCI6IHsKICAgICAgInByb2dyYW1faWQiOiAicHJvZ3JhbSBpZCAyIiwKICAgICAgInRva2VuIjogIkJhc2U2NCBlbmNvZGUgdG9rZW4gMiIKICAgIH0KICB9LAogICJkZWZhdWx0IjogewogICAgImRlZmF1bHQiOiB7CiAgICAgICJwcm9ncmFtX2lkIjogImRlZmF1bHQgcHJvZ3JhbSAxIiwKICAgICAgInRva2VuIjogImRlZmF1bHQgcHJvZ3JhbSB0b2tlbiAxIgogICAgfQogIH0KfQ==';

  if (customEnv) {
    Object.keys(customEnv).forEach((key) => {
      process.env[key] = customEnv[key];
    });
  }
};

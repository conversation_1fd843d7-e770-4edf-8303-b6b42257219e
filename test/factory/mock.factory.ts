import {
  IPayoneerBeneficiaryRequest,
  IPayoneerRegisterPayeeFormatResponse,
} from '../../src/common/data-types/payoneer.data.types';

import {
  getDefaultRegisterCompanyPayeeRequest,
  getDefaultRegisterIndividualPayeeRequest,
  getRegisterPayeeFormatResponse,
} from './default.object.factory';

export const getDefaultRegisterCompanyPayeeReq = (
  overwrites: Partial<IPayoneerBeneficiaryRequest> = {},
): IPayoneerBeneficiaryRequest => {
  return { ...getDefaultRegisterCompanyPayeeRequest(), ...overwrites };
};

export const getDefaultRegisterIndividualPayeeReq = (
  overwrites: Partial<IPayoneerBeneficiaryRequest> = {},
): IPayoneerBeneficiaryRequest => {
  return { ...getDefaultRegisterIndividualPayeeRequest(), ...overwrites };
};

export const getRegisterPayeeFormatRes = (overwrites: Partial<IPayoneerRegisterPayeeFormatResponse> = {}) => {
  return { ...getRegisterPayeeFormatResponse(), ...overwrites };
};

export const getMockProviderConfigObj = () => {
  return {
    IN: {
      A: {
        program_id: 'IN program id 1',
        token: 'IN Base64 encode token 1',
      },
      B: {
        program_id: 'program id 2',
        token: 'Base64 encode token 2',
      },
      C: {
        program_id: 'program id 2',
        token: 'Base64 encode token 2',
      },
      default: {
        program_id: 'AU program id 1',
        token: 'AU Base64 encode token 1',
      },
    },
    AU: {
      D: {
        program_id: 'AU program id 1',
        token: 'AU Base64 encode token 1',
      },
    },
    PK: {
      D: {
        program_id: 'PK program id 2',
        token: 'PK Base64 encode token 2',
      },
    },
    default: {
      default: {
        program_id: 'default program 1',
        token: 'default program token 1',
      },
    },
  };
};

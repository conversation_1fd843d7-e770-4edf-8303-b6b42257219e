import {
  IPayoneerBeneficiaryRequest,
  IPayoneerRegisterPayeeFormatResponse,
  PAYONEER_LEGAL_TYPES,
} from '../../src/common/data-types/payoneer.data.types';
import { faker } from '@faker-js/faker';

export const getDefaultRegisterIndividualPayeeRequest = (): IPayoneerBeneficiaryRequest => {
  return {
    payee_id: faker.string.uuid(),
    registration_mode: 'full',
    payee: {
      type: 'individual',
      contact: {
        first_name: 'joe',
        last_name: 'doe',
        email: '<EMAIL>',
        phone: '********',
      },
      address: {
        address_line_1: '2343 Main st.',
        address_line_2: '',
        city: 'New York',
        state: 'NY',
        country: 'US',
        zip_code: '11111',
      },
    },
    payout_method: {
      bank_account_type: 'personal',
      country: 'NG',
      currency: 'NGN',
      details: {
        bank_name: 'First bank',
        account_name: '<PERSON>',
        account_number: '**********',
        bank_code: '000019',
      },
    },
  };
};

export const getDefaultRegisterCompanyPayeeRequest = (): IPayoneerBeneficiaryRequest => {
  return {
    payee_id: faker.string.uuid(),
    registration_mode: 'full',
    payee: {
      type: 'company',
      contact: {
        first_name: 'Skuado',
        last_name: 'PTER',
        email: '<EMAIL>',
        phone: '********67',
      },
      address: {
        address_line_1: '2343 Main st.',
        address_line_2: '',
        city: 'New York',
        state: 'NY',
        country: 'US',
        zip_code: '11111',
      },
      company: {
        legal_type: PAYONEER_LEGAL_TYPES.PRIVATE,
        incorporated_country: 'US',
        incorporated_state: 'NY',
        name: 'SKUAD PTE LTD',
        url: 'skuad.com',
      },
    },
    payout_method: {
      bank_account_type: 'company',
      country: 'US',
      currency: 'USD',
      details: {
        bank_name: 'Bank of America',
        account_name: 'John Smith',
        account_number: '********9',
        branch_name: 'Bank of America Branch Name',
        routing_number: '*********',
        account_type: 'C',
      },
    },
  };
};

export const getRegisterPayeeFormatResponse = (): IPayoneerRegisterPayeeFormatResponse => {
  return {
    audit_id: *********,
    code: 0,
    description: 'Success',
    payout_method: {
      type: 'bank',
      country: 'NG',
      currency: 'NGN',
      bank_account_type: 'Personal',
      details: {
        bank_name: {
          field_id: '1',
          field_type: 'Text',
          label: 'Bank Name',
          description: '',
          watermark: 'e.g. National Bank Of Nigeria',
          required: 'True',
          min_length: '0',
          max_length: '50',
          regex: '^[A-Za-z0-9\\s\\*\\#\\^\\&\\=\\[\\]\\{\\}\\(\\)\\|\\?\\`\\~\\;\\.\\,\\-]+$',
          list: {
            support_other: 'True',
            list_items: [
              {
                description: 'Abbey Mortgage Bank',
                value: 'Abbey Mortgage Bank',
              },
              {
                description: 'Access Bank Plc',
                value: 'Access Bank Plc',
              },
              {
                description: 'AG Mortgage Bank',
                value: 'AG Mortgage Bank',
              },
              {
                description: 'Aso Savings and Loans Plc',
                value: 'Aso Savings and Loans Plc',
              },
              {
                description: 'Citibank',
                value: 'Citibank',
              },
              {
                description: 'Coronation Merchant Bank',
                value: 'Coronation Merchant Bank',
              },
              {
                description: 'Diamond Bank',
                value: 'Diamond Bank',
              },
              {
                description: 'Ecobank Nigeria Plc',
                value: 'Ecobank Nigeria Plc',
              },
              {
                description: 'Enterprise Bank',
                value: 'Enterprise Bank',
              },
              {
                description: 'Equitorial Trust Bank',
                value: 'Equitorial Trust Bank',
              },
              {
                description: 'FBN Mortgage Limited',
                value: 'FBN Mortgage Limited',
              },
              {
                description: 'FBN Quest Merchant Bank',
                value: 'FBN Quest Merchant Bank',
              },
              {
                description: 'Fidelity Bank',
                value: 'Fidelity Bank',
              },
              {
                description: 'Finbank',
                value: 'Finbank',
              },
              {
                description: 'First Bank of Nigeria',
                value: 'First Bank of Nigeria',
              },
              {
                description: 'First City Monument Bank Plc',
                value: 'First City Monument Bank Plc',
              },
              {
                description: 'FSDH Merchant Bank',
                value: 'FSDH Merchant Bank',
              },
              {
                description: 'Gateway Mortgage Bank',
                value: 'Gateway Mortgage Bank',
              },
              {
                description: 'Guaranty Trust Bank',
                value: 'Guaranty Trust Bank',
              },
              {
                description: 'Heritage Bank',
                value: 'Heritage Bank',
              },
              {
                description: 'Imperial Homes Mortgage Bank',
                value: 'Imperial Homes Mortgage Bank',
              },
              {
                description: 'Intercontinental Bank',
                value: 'Intercontinental Bank',
              },
              {
                description: 'JAIZ Bank',
                value: 'JAIZ Bank',
              },
              {
                description: 'Jubilee-Life Mortgage Bank',
                value: 'Jubilee Life Mortgage Bank',
              },
              {
                description: 'Keystone Bank',
                value: 'Keystone Bank',
              },
              {
                description: 'Kuda Bank',
                value: 'Kuda Bank',
              },
              {
                description: 'Lagos Building Investment Company',
                value: 'Lagos Building Investment Company',
              },
              {
                description: 'Mainstreet Bank',
                value: 'Mainstreet Bank',
              },
              {
                description: 'NIBSS Psuedo Bank',
                value: 'NIBSS Psuedo Bank',
              },
              {
                description: 'NOVA Merchant Bank',
                value: 'NOVA Merchant Bank',
              },
              {
                description: 'Oceanic Bank',
                value: 'Oceanic Bank',
              },
              {
                description: 'Parallex',
                value: 'Parallex',
              },
              {
                description: 'Polaris Bank Limited',
                value: 'Polaris Bank Limited',
              },
              {
                description: 'Providus Bank',
                value: 'Providus Bank',
              },
              {
                description: 'SafeTrust Mortgage Bank',
                value: 'SafeTrust Mortgage Bank',
              },
              {
                description: 'Stanbic IBTC Bank',
                value: 'Stanbic IBTC Bank',
              },
              {
                description: 'Standard Chartered Bank of Nigeria',
                value: 'Standard Chartered Bank of Nigeria',
              },
              {
                description: 'Sterling Bank Plc',
                value: 'Sterling Bank Plc',
              },
              {
                description: 'Sun Trust Bank',
                value: 'Sun Trust Bank',
              },
              {
                description: 'Trustbond Mortgage',
                value: 'Trustbond Mortgage',
              },
              {
                description: 'Union Bank of Nigeria Plc',
                value: 'Union Bank of Nigeria Plc',
              },
              {
                description: 'United Bank for Africa Plc',
                value: 'United Bank for Africa Plc',
              },
              {
                description: 'Unity Bank Plc',
                value: 'Unity Bank Plc',
              },
              {
                description: 'Wema Bank Plc',
                value: 'Wema Bank Plc',
              },
              {
                description: 'Zenith International Bank Plc',
                value: 'Zenith International Bank Plc',
              },
              {
                description: 'Other',
                value: '-1',
              },
            ],
          },
        },
        account_name: {
          field_id: '2',
          field_type: 'Text',
          label: 'Account Holder Name',
          description: 'Full name of the official bank account holder. Latin characters only, no symbols allowed.',
          watermark: 'e.g. John Smith',
          required: 'True',
          min_length: '0',
          max_length: '35',
          regex: '(?!^\\d+$)(?!^ +$)^[a-zA-Z0-9& ]+$',
        },
        account_number: {
          field_id: '3',
          field_type: 'Text',
          label: 'Account Number',
          description: 'NUBAN (10 digits)',
          watermark: 'e.g. ********** ',
          required: 'True',
          min_length: '10',
          max_length: '10',
          regex: '^[0-9]+$',
        },
        swift: {
          field_id: '4',
          field_type: 'Text',
          label: 'SWIFT / BIC',
          description:
            '&amp;lt;a href=&quot;http://www.swift.com/bsl/facelets/bicsearch.faces&quot; target=&quot;_blank&quot;&amp;gt;Click here&amp;lt;/a&amp;gt; to find your SWIFT/BIC',
          watermark: 'e.g. ABCDNGTTXXX',
          required: 'False',
          min_length: '8',
          max_length: '11',
          regex: '^([A-Z]{4})([A-Z0-9]{4}|[A-Z0-9]{7})$',
        },
        bank_code: {
          field_id: '6',
          field_type: 'Text',
          label: 'Bank Code',
          description: '(3 digits)',
          watermark: 'e.g. 044',
          required: 'True',
          min_length: '3',
          max_length: '6',
          regex: '^[0-9]+$',
        },
      },
    },
  };
};
